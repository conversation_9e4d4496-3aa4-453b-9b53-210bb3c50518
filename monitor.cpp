﻿#include "monitor.h"
#include "ui_monitor.h"
#include "message.h"
#include "ad_thread.h"
#include "qcustomplot.h"
#include "data_managent.h"

#include <QVBoxLayout>
#include <QCheckBox>
#include <QtCharts/QChartView>
#include <QtCharts/QLineSeries>
#include <QtCharts/QDateTimeAxis>
#include <QtCharts/QValueAxis>
#include <QTimer>
#include <QDateTime>

monitor::monitor(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::monitor)
{
    ui->setupUi(this);

    this->ui->widget_channel_chose->setMaximumWidth(10000);

    this->verticalLayout_channel_chose = new QVBoxLayout();
    this->verticalLayout_focus = new QVBoxLayout();

    this->ui->widget_channel_chose->setLayout(this->verticalLayout_channel_chose);
    this->ui->widget_focus->setLayout(this->verticalLayout_focus);

    this->timer_update_overview = new QTimer();
    this->timer_update_focus = new QTimer();



    this->data_source = new QVector<double>();

    this->ui->comboBox_device->blockSignals(true);

    for(int i = 0; i < 8; i++)
    {
        this->customPlot[i] = new QCustomPlot(this->ui->widget_channel_chose);
        this->verticalLayout_channel_chose->addWidget(customPlot[i]);
        this->customPlot[i]->addGraph();
        this->customPlot[i]->graph(0)->setLineStyle(QCPGraph::lsLine);  // 折线连接
        this->customPlot[i]->xAxis->setRange(0, 1024);
        this->customPlot[i]->yAxis->setRange(-1, 1);
        this->customPlot[i]->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom);

    }
    for(int i = 8; i < 16; i++)
    {
        this->customPlot[i] = new QCustomPlot(this->ui->widget_focus);
        this->verticalLayout_focus->addWidget(customPlot[i]);
        this->customPlot[i]->addGraph();
        this->customPlot[i]->graph(0)->setLineStyle(QCPGraph::lsLine);  // 折线连接
        this->customPlot[i]->xAxis->setRange(0, 1024);
        this->customPlot[i]->yAxis->setRange(-1, 1);
        this->customPlot[i]->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom);

    }

    connect(this->timer_update_overview, &QTimer::timeout, this, &monitor::update_plot);
    this->timer_update_overview->start(15);

    for(int i = 0; i < nums; i++)
    {
        this->ui->comboBox_device->addItem(sensor_config_message[i]->IP);
    }
    this->ui->comboBox_device->blockSignals(false);

    //this->data_source = &(pThread[0]->recvData);

    this->ui->pushButton_model_change->setStyleSheet(
        "QPushButton {"
        "    background-color: #4CAF50; /* 背景颜色 */"
        "    color: white; /* 文字颜色 */"
        "    border: none; /* 无边框 */"
        "    padding: 10px 20px; /* 内边距 */"
        "    border-radius: 5px; /* 圆角 */"
        "    font-size: 16px; /* 字体大小 */"
        "}"
        "QPushButton:hover {"
        "    background-color: #45a049; /* 鼠标悬停时的背景颜色 */"
        "}"
        "QPushButton:pressed {"
        "    background-color: #3d8b40; /* 鼠标按下时的背景颜色 */"
        "}"
    );

    this->overview_init();
    //this->focus_init();

}

void monitor::update_plot()
{
    double time_Start = (double)clock();

    QVector<QVector<double>> x(16, QVector<double>());
    QVector<QVector<double>> y(16, QVector<double>());

    QDateTime pktTime;
    for(int i = 0, index = 0; i < 16 * m_xPointNum+1; ++i)
    {
        if(i == 0)
        {
            pktTime = QDateTime::fromMSecsSinceEpoch(recive_data[0].at(0) * 1000);
            // qDebug()<<pktTime<<endl<<"net2418c_ad.cpp 576";
            continue;
        }
        // 通道号
        int chNo =index % 16;

        //轮次
        int round = index / 16;
        double dif_msec = round * (1 / 10000.00) * 1000;
        qint64 mSecs = pktTime.toMSecsSinceEpoch();

        // 在10Khz下，0.1ms采一个点，可能计算时间存在损失，尝试转为us
        double curT = (mSecs * 1000.0 + dif_msec * 1000.0) / 1000000.0;



            x[chNo].append(round);
            y[chNo].append(recive_data[0].at(i));



        ++index;
    }

    for(int i = 0; i < 16; i++)
    {
        this->customPlot[i]->graph(0)->data().clear();
        this->customPlot[i]->graph(0)->setData(x[i],y[i]);
        this->customPlot[i]->replot();
    }

    double time_Middle = (double)clock();
    //qDebug() << "绘图时间:" << (time_Middle - time_Start) << "ms" << __LINE__ << __FILE__;
}

void monitor::overview_init()
{
//    this->ui->widget_channel_chose->setMaximumWidth(10000);

//    this->ui->widget_channel_chose->setLayout(this->verticalLayout_channel_chose);
//    this->ui->widget_focus->setLayout(this->verticalLayout_focus);


//    // 创建QCustomPlot并添加到布局中
//    for(int i = 0; i < 8; i++)
//    {
//        customPlot[i] = new QCustomPlot(this->ui->widget_channel_chose);
//        this->verticalLayout_channel_chose->addWidget(customPlot[i]);

//        // 设置图表属性
//        customPlot[i]->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom);
//        customPlot[i]->legend->setVisible(true);
//        customPlot[i]->legend->setFont(QFont("SimHei", 9));

//        // 创建折线
//        customPlot[i]->addGraph();
//        customPlot[i]->graph(0)->setName(QString("通道 %1").arg(i + 1));
//        customPlot[i]->graph(0)->setLineStyle(QCPGraph::lsLine);

//        // 初始化时间范围（显示未来10秒）
//        QDateTime now = QDateTime::currentDateTime();
//        double nowSecs = now.toMSecsSinceEpoch() / 1000.0;
//        customPlot[i]->xAxis->setRange(nowSecs, nowSecs + 1);


//        // 设置X轴为时间轴
//        customPlot[i]->xAxis->setLabel("时间");
//        QSharedPointer<QCPAxisTickerDateTime> dateTicker(new QCPAxisTickerDateTime);
//        dateTicker->setDateTimeFormat("hh:mm:ss");
//        customPlot[i]->xAxis->setTicker(dateTicker);
// // customPlot[i]->xAxis->setRange(0, 1024);

//        // 设置Y轴
//        customPlot[i]->yAxis->setLabel("数值");
//        customPlot[i]->yAxis->setRange(-1, 1);
//    }

//    for(int i = 8; i < 16; i++)
//    {
//        customPlot[i] = new QCustomPlot(this->ui->widget_focus);
//        this->verticalLayout_focus->addWidget(customPlot[i]);

//        // 设置图表属性
//        customPlot[i]->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom);
//        customPlot[i]->legend->setVisible(true);
//        customPlot[i]->legend->setFont(QFont("SimHei", 9));


//        // 创建折线
//        customPlot[i]->addGraph();
//        customPlot[i]->graph(0)->setName(QString("通道 %1").arg(i + 1));
//        customPlot[i]->graph(0)->setLineStyle(QCPGraph::lsLine);

//        // 初始化时间范围（显示未来10秒）
//        QDateTime now = QDateTime::currentDateTime();
//        double nowSecs = now.toMSecsSinceEpoch() / 1000.0;


//        // 设置X轴为时间轴
//        customPlot[i]->xAxis->setLabel("时间");
//        QSharedPointer<QCPAxisTickerDateTime> dateTicker(new QCPAxisTickerDateTime);
//        dateTicker->setDateTimeFormat("hh:mm:ss");
//        customPlot[i]->xAxis->setTicker(dateTicker);

//        // 设置Y轴
//        customPlot[i]->yAxis->setLabel("数值");
//        customPlot[i]->yAxis->setRange(-0.05, 0.05);
//    }



}



void monitor::focus_init()
{
    QVector<QColor> colors = {
        QColor(255, 100, 100), QColor(100, 255, 100), QColor(100, 100, 255),
        QColor(255, 255, 100), QColor(255, 100, 255), QColor(100, 255, 255),
        QColor(200, 150, 50), QColor(50, 200, 150), QColor(150, 50, 200),
        QColor(255, 150, 100), QColor(100, 255, 150), QColor(150, 100, 255),
        QColor(220, 120, 120), QColor(120, 220, 120), QColor(120, 120, 220),
        QColor(180, 200, 100)
    };

    this->ui->widget_channel_chose->setMaximumWidth(100);

    this->ui->widget_channel_chose->setLayout(this->verticalLayout_channel_chose);
    this->ui->widget_focus->setLayout(this->verticalLayout_focus);

    // 添加16个复选框
    for (int i = 0; i < 16; i++)
    {
        QCheckBox *checkBox = new QCheckBox("通道" + QString::number(i));
        checkBox->setMinimumWidth(100);
        checkBox->setChecked(true);

        checkBox->setStyleSheet(QString(
            "QCheckBox {"
            "    color: black;"
            "}"
            "QCheckBox::indicator:checked {"
            "    background-color: %1;"
            "}"
        ).arg(colors[i].name()));

        connect(checkBox, &QCheckBox::stateChanged, [i,this](int state) {
            if (state == Qt::Unchecked)
            {
                this->line[i]->setVisible(false);
            }
            else if (state == Qt::Checked)
            {
                this->line[i]->setVisible(true);
            }
        });

        this->verticalLayout_channel_chose->addWidget(checkBox);
    }

    // 创建QCustomPlot并添加到布局中
    QCustomPlot *customPlot = new QCustomPlot(this->ui->widget_focus);
    this->verticalLayout_focus->addWidget(customPlot);

    // 设置图表属性
    customPlot->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom);
    customPlot->legend->setVisible(true);
    customPlot->legend->setFont(QFont("SimHei", 9));

    // 创建16条折线
    for (int i = 0; i < 16; ++i)
    {
        this->line[i] = customPlot->addGraph();
        QPen pen(colors[i]);
        pen.setWidthF(1.5 + i * 0.1);
        customPlot->graph(i)->setPen(pen);
        customPlot->graph(i)->setName(QString("曲线 %1").arg(i + 1));
    }

    // 初始化时间范围（显示未来10秒）
    QDateTime now = QDateTime::currentDateTime();
    double nowSecs = now.toMSecsSinceEpoch() / 1000.0;
    customPlot->xAxis->setRange(nowSecs, nowSecs + 1);
    customPlot->yAxis->setRange(-1.5, 1.5);

    // 设置X轴为时间轴
    customPlot->xAxis->setLabel("时间");
    QSharedPointer<QCPAxisTickerDateTime> dateTicker(new QCPAxisTickerDateTime);
    dateTicker->setDateTimeFormat("hh:mm:ss.zzz");
    customPlot->xAxis->setTicker(dateTicker);

    // 设置Y轴
    customPlot->yAxis->setLabel("数值");
    customPlot->yAxis->setRange(-5, 5);

    // 创建定时器，每秒更新一次数据
    connect(this->timer_update_focus, &QTimer::timeout, [=]() {

    });

    // 启动定时器
    this->timer_update_focus->start(1000); // 每秒更新一次
}

monitor::~monitor()
{
    delete ui;
}

void monitor::on_pushButton_start_clicked()
{
    if(this->ui->pushButton_start->text() == "开始采集")
    {

    }
    else if(this->ui->pushButton_start->text() == "停止采集")
    {

    }
}

void monitor::on_pushButton_model_change_clicked()
{
    if(this->ui->pushButton_model_change->text() == "总览")
    {
        this->ui->pushButton_model_change->setText("聚焦");
        //this->overview_init();
    }
    else if(this->ui->pushButton_model_change->text() == "聚焦")
    {
        this->ui->pushButton_model_change->setText("总览");
        //this->focus_init();
    }
}

void monitor::on_comboBox_device_currentTextChanged(const QString &arg1)
{
    for(int i = 0; i < nums; i++)
    {
//        if(sensor_config_message[this->pThread[i]->index]->IP == arg1)
//        {
//            this->data_source = &(this->pThread[i]->recvData);
//        }
    }
}
