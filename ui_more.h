/********************************************************************************
** Form generated from reading UI file 'more.ui'
**
** Created by: Qt User Interface Compiler version 5.9.8
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MORE_H
#define UI_MORE_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QToolButton>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_More
{
public:
    QVBoxLayout *verticalLayout;
    QWidget *widget;
    QVBoxLayout *verticalLayout_2;
    QWidget *backup;
    QHBoxLayout *horizontalLayout;
    QToolButton *toolButton_backup;
    QLabel *label;
    QSpacerItem *horizontalSpacer;
    QWidget *widget_no_more;
    QHBoxLayout *horizontalLayout_2;
    QSpacerItem *horizontalSpacer_2;
    QLabel *label_2;
    QSpacerItem *horizontalSpacer_3;
    QSpacerItem *verticalSpacer;

    void setupUi(QWidget *More)
    {
        if (More->objectName().isEmpty())
            More->setObjectName(QStringLiteral("More"));
        More->resize(955, 570);
        verticalLayout = new QVBoxLayout(More);
        verticalLayout->setSpacing(0);
        verticalLayout->setObjectName(QStringLiteral("verticalLayout"));
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        widget = new QWidget(More);
        widget->setObjectName(QStringLiteral("widget"));
        widget->setStyleSheet(QLatin1String("#widget {\n"
"	background: #fff;\n"
"}"));
        verticalLayout_2 = new QVBoxLayout(widget);
        verticalLayout_2->setSpacing(10);
        verticalLayout_2->setObjectName(QStringLiteral("verticalLayout_2"));
        verticalLayout_2->setContentsMargins(10, 10, 10, 10);
        backup = new QWidget(widget);
        backup->setObjectName(QStringLiteral("backup"));
        backup->setMinimumSize(QSize(0, 60));
        backup->setMaximumSize(QSize(16777215, 60));
        backup->setStyleSheet(QLatin1String("#backup {\n"
"	background:#fafafa;\n"
"}"));
        horizontalLayout = new QHBoxLayout(backup);
        horizontalLayout->setSpacing(10);
        horizontalLayout->setObjectName(QStringLiteral("horizontalLayout"));
        horizontalLayout->setContentsMargins(10, 0, 10, 0);
        toolButton_backup = new QToolButton(backup);
        toolButton_backup->setObjectName(QStringLiteral("toolButton_backup"));
        toolButton_backup->setMinimumSize(QSize(80, 24));
        toolButton_backup->setMaximumSize(QSize(80, 24));
        QFont font;
        font.setFamily(QStringLiteral("Microsoft YaHei UI"));
        toolButton_backup->setFont(font);
        toolButton_backup->setStyleSheet(QLatin1String("QToolButton{\n"
"	background:#1890FF;\n"
"	border:none;\n"
"	color:#fafafa;\n"
"	border-radius:2px;\n"
"}\n"
"QToolButton:hover{\n"
"	background:#1281E8;\n"
"}"));

        horizontalLayout->addWidget(toolButton_backup);

        label = new QLabel(backup);
        label->setObjectName(QStringLiteral("label"));
        label->setFont(font);
        label->setStyleSheet(QStringLiteral("color:#666;"));

        horizontalLayout->addWidget(label);

        horizontalSpacer = new QSpacerItem(543, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer);


        verticalLayout_2->addWidget(backup);

        widget_no_more = new QWidget(widget);
        widget_no_more->setObjectName(QStringLiteral("widget_no_more"));
        widget_no_more->setMinimumSize(QSize(0, 30));
        horizontalLayout_2 = new QHBoxLayout(widget_no_more);
        horizontalLayout_2->setSpacing(0);
        horizontalLayout_2->setObjectName(QStringLiteral("horizontalLayout_2"));
        horizontalLayout_2->setContentsMargins(0, 0, 0, 0);
        horizontalSpacer_2 = new QSpacerItem(423, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer_2);

        label_2 = new QLabel(widget_no_more);
        label_2->setObjectName(QStringLiteral("label_2"));
        label_2->setFont(font);
        label_2->setStyleSheet(QStringLiteral("color:#999;"));

        horizontalLayout_2->addWidget(label_2);

        horizontalSpacer_3 = new QSpacerItem(422, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer_3);


        verticalLayout_2->addWidget(widget_no_more);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_2->addItem(verticalSpacer);


        verticalLayout->addWidget(widget);


        retranslateUi(More);

        QMetaObject::connectSlotsByName(More);
    } // setupUi

    void retranslateUi(QWidget *More)
    {
        More->setWindowTitle(QApplication::translate("More", "Form", Q_NULLPTR));
        toolButton_backup->setText(QApplication::translate("More", "\346\225\260\346\215\256\345\244\207\344\273\275", Q_NULLPTR));
        label->setText(QApplication::translate("More", "\347\253\213\345\215\263\345\244\207\344\273\275\346\225\260\346\215\256\357\274\214\346\225\260\346\215\256\347\224\237\346\210\220\345\234\250\347\250\213\345\272\217\346\240\271\347\233\256\345\275\225/backup/\344\270\213", Q_NULLPTR));
        label_2->setText(QApplication::translate("More", "\346\262\241\346\234\211\346\233\264\345\244\232\345\212\237\350\203\275\344\272\206", Q_NULLPTR));
    } // retranslateUi

};

namespace Ui {
    class More: public Ui_More {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MORE_H
