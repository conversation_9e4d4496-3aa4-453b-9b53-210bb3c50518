#include "mainwindow.h"
#include "sensor_list.h"
#include "message.h"
#include "nlenospeed.h"
#include "main_window.h"
#include "fft_test.h"
#include "data_managent.h"

#include <QApplication>
#include <QDebug>
#include <QSqlQuery>
#include <QSqlError>

#include <iostream>
#include <vector>
#include <cmath>
#include <utility>
#include <iomanip>

#include <QApplication>
#include <QMainWindow>
#include <QMenuBar>
#include <QMenu>
#include <QAction>
#include <QStatusBar>
#include <QTextEdit>
#include <QMessageBox>

using namespace std;

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);

    db->connect_database();
    QSqlQuery query("SELECT * FROM top_project;");
    if(!query.exec())
    {
        qDebug() << query.lastError() << __LINE__<< __FILE__;
    }
    query.next();

    load_project_and_sensor_message_config_from_database(query.value(0).toInt());

    data_and_thread_init();

    main_window w;
    w.showMaximized();

//    fft_test w;
//    w.show();

    return a.exec();
}



