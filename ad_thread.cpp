#include "ad_thread.h"
#include "NET2418.h"
#include "message.h"
#include "data_managent.h"

#include <QDebug>

AD_THREAD::AD_THREAD(int index, QObject *parent)
    : QThread(parent)
    , threadStop(false)
{
    this->index = index;    
    for(int i=0;i<channel_count;i++)
    {
        this->channel_isEnable[i]=sensor_config_message[index]->channel_isEnable[i];
        if(int(channel_isEnable[i])==1) {
            this->enablechanelnum++;
        }
    }
        qDebug()<<this->index<< __LINE__ <<__FILE__;
    //recive_data[this->index].resize(channel_count * m_xPointNum + 1);
    qRegisterMetaType<QVector<double>>("QVector<double>");
}

void AD_THREAD::run()
{
    int     iResult = 0;
    long    x1 = 0;
    double  bufferSize = 0;

    QVector<double> recvData(enablechanelnum* m_xPointNum + 1, 0);
    threadStop = false;

    while (!threadStop)
    {

        double time_Start = (double)clock();
        //获取数据流长度
        iResult = eGet(pHandle[this->index], XC_ioGET_STREAM_LENGTH, 0, &bufferSize, &x1);
        if(iResult)
        {
            qDebug() << "AD thread get stream length error" << iResult << __LINE__ <<__FILE__;
            break;
        }
       //qDebug()<<bufferSize<<this->index<< __LINE__ <<__FILE__;
        if(bufferSize < enablechanelnum * m_xPointNum)
        {
            QThread::msleep(20);
            continue;
        }

        x1 = enablechanelnum  * m_xPointNum + 1;

        iResult = eGet(pHandle[this->index], XC_ioGET_STREAM_DATA, 0, recive_data[this->index].data(), &x1);

        if(iResult)
        {
            qDebug() << "AD thread get stream data error " << iResult << __LINE__ << __FILE__;
            continue;
        }
        //for(int i=1;i<x1;i++){qDebug()<<recive_data[this->index][i];}
       //qDebug()<< QString::number(recvData[1], 'f', 5)<<__LINE__<<__FILE__;
        //sendData.setValue(recvData);
        //emit send_recive_data(recvData, this->index ,x1);
        qDebug()<<this->index<< __LINE__ <<__FILE__;
        emit send_recive_data(recvData, this->index ,x1);
        QThread::msleep(10);

      //  double time_Middle = (double)clock();
       // qDebug() << "采集时间:" << (time_Middle - time_Start) << "ms" << __LINE__ << __FILE__;
    }
    // qDebug() << "线程终止" << __LINE__ << __FILE__;
}

void AD_THREAD::GetHandle(MyHANDLE handle)
{
    //qDebug() << "handle" << handle << __LINE__ << __FILE__;
}

void AD_THREAD::recvThreadStop(bool stop)
{
    threadStop = stop;
}


