/****************************************************************************
** Meta object code from reading C++ file 'project_create.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.8)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../project_create.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'project_create.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.8. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_project_create_t {
    QByteArrayData data[11];
    char stringdata0[287];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_project_create_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_project_create_t qt_meta_stringdata_project_create = {
    {
QT_MOC_LITERAL(0, 0, 14), // "project_create"
QT_MOC_LITERAL(1, 15, 36), // "on_pushButton_sensor_setting_..."
QT_MOC_LITERAL(2, 52, 0), // ""
QT_MOC_LITERAL(3, 53, 26), // "on_pushButton_path_clicked"
QT_MOC_LITERAL(4, 80, 26), // "on_pushButton_save_clicked"
QT_MOC_LITERAL(5, 107, 28), // "on_pushButton_cancel_clicked"
QT_MOC_LITERAL(6, 136, 28), // "on_pushButton_reload_clicked"
QT_MOC_LITERAL(7, 165, 37), // "on_lineEdit_divice_number_tex..."
QT_MOC_LITERAL(8, 203, 4), // "arg1"
QT_MOC_LITERAL(9, 208, 33), // "on_lineEdit_device_IP_textCha..."
QT_MOC_LITERAL(10, 242, 44) // "on_comboBox_device_choose_cur..."

    },
    "project_create\0on_pushButton_sensor_setting_clicked\0"
    "\0on_pushButton_path_clicked\0"
    "on_pushButton_save_clicked\0"
    "on_pushButton_cancel_clicked\0"
    "on_pushButton_reload_clicked\0"
    "on_lineEdit_divice_number_textChanged\0"
    "arg1\0on_lineEdit_device_IP_textChanged\0"
    "on_comboBox_device_choose_currentTextChanged"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_project_create[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       8,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,   54,    2, 0x08 /* Private */,
       3,    0,   55,    2, 0x08 /* Private */,
       4,    0,   56,    2, 0x08 /* Private */,
       5,    0,   57,    2, 0x08 /* Private */,
       6,    0,   58,    2, 0x08 /* Private */,
       7,    1,   59,    2, 0x08 /* Private */,
       9,    1,   62,    2, 0x08 /* Private */,
      10,    1,   65,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    8,
    QMetaType::Void, QMetaType::QString,    8,
    QMetaType::Void, QMetaType::QString,    8,

       0        // eod
};

void project_create::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        project_create *_t = static_cast<project_create *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->on_pushButton_sensor_setting_clicked(); break;
        case 1: _t->on_pushButton_path_clicked(); break;
        case 2: _t->on_pushButton_save_clicked(); break;
        case 3: _t->on_pushButton_cancel_clicked(); break;
        case 4: _t->on_pushButton_reload_clicked(); break;
        case 5: _t->on_lineEdit_divice_number_textChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 6: _t->on_lineEdit_device_IP_textChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 7: _t->on_comboBox_device_choose_currentTextChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        default: ;
        }
    }
}

const QMetaObject project_create::staticMetaObject = {
    { &QWidget::staticMetaObject, qt_meta_stringdata_project_create.data,
      qt_meta_data_project_create,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *project_create::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *project_create::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_project_create.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int project_create::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 8)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 8;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
