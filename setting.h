#ifndef SETTING_H
#define SETTING_H

#include <QWidget>

namespace Ui {
class Setting;
}

class Setting : public QWidget
{
    Q_OBJECT

public:
    explicit Setting(QWidget *parent = nullptr);
    ~Setting();

    void tableWidget_device_config_init(int card_id);

    void on_PushButton_save_clicked(int card_id);

private slots:
    void on_tableWidget_device_config_doubleClicked(const QModelIndex &index);


private:
    Ui::Setting *ui;
};

#endif // SETTING_H
