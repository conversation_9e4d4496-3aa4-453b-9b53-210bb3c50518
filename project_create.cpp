﻿#include "project_create.h"
#include "ui_project_create.h"
#include "database.h"
#include "sensor_list.h"
#include <QFileDialog>
#include "message.h"

#include <QTime>
#include <QTableWidget>
#include <QSqlQuery>
#include <QSqlError>
#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QCheckBox>
#include <QHBoxLayout>
#include <QMessageBox>

#include <QTableWidget>
#include <QLineEdit>
#include <QComboBox>
#include <QCheckBox>
#include <QDoubleSpinBox>
#include <QSpinBox>

project_create::project_create(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::project_create)
{
    ui->setupUi(this);

    this->ui->tableWidget_sensor_parameter->setColumnWidth(0,100);
    this->ui->tableWidget_sensor_parameter->setColumnWidth(1,200);
    this->ui->tableWidget_sensor_parameter->setColumnWidth(2,300);
    this->ui->tableWidget_sensor_parameter->setColumnWidth(3,200);
    this->ui->tableWidget_sensor_parameter->setColumnWidth(4,200);
    this->ui->tableWidget_sensor_parameter->setColumnWidth(5,150);
    this->ui->tableWidget_sensor_parameter->setColumnWidth(6,150);
    this->ui->tableWidget_sensor_parameter->setColumnWidth(7,150);
    this->ui->tableWidget_sensor_parameter->setColumnWidth(8,150);
    this->ui->tableWidget_sensor_parameter->setColumnWidth(9,200);
    this->ui->tableWidget_sensor_parameter->setColumnWidth(10,180);
    this->ui->tableWidget_sensor_parameter->setColumnWidth(11,180);
    this->ui->tableWidget_sensor_parameter->setColumnWidth(12,180);
    //this->ui->tableWidget_sensor_parameter->setColumnWidth(13,100);

    QString styleSheet = R"(
    /* 主窗口整体样式 */
    QMainWindow {
        background-color: #f5f7fa; /* 浅灰蓝色背景，营造清爽氛围 */
    }

    /* 中心部件，作为基础容器 */
    QWidget#centralwidget {
        background-color: transparent; /* 透明，让 stackedWidget 等自己控制背景 */
    }

    /* 栈式窗口，用于页面切换的容器 */
    QStackedWidget {
        background-color: white;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 10px;
        margin: 8px; /* 与主窗口边缘保持间距 */
    }

    /* 栈式窗口里的页面，可给不同页面差异化设置，这里统一先简单处理 */
    QWidget#page, QWidget#page_2 {
        background-color: white;
        border-radius: 6px;
        padding: 12px;
    }

    /* 若页面里有其他控件（如按钮、标签等，可提前预设通用样式，后续叠加） */
    QLabel {
        color: #333333;
        font-size: 20px;
    }

     QCombox {
         color: #333333;
         font-size: 20px;
     }

     QLineEdit {
         color: #333333;
         font-size: 20px;
     }

    QPushButton {
        background-color: #409eff; /* 经典主题蓝色 */
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 16px;
        margin: 4px;
    }
    QPushButton:hover {
        background-color: #66b1ff; /* 悬停变浅蓝 */
    }
    QPushButton:pressed {
        background-color: #337ab7; /* 按下加深 */
    }

    )";
    this->setObjectName("project_create");
    this->setStyleSheet(styleSheet);
    this->ui->groupBox_sensor->setObjectName("groupBox_sensor");
    this->ui->groupBox_sensor->setStyleSheet(styleSheet);
    this->ui->pushButton_path->setStyleSheet("QPushButton {"
                                            " background-color: #409eff;" /* 经典主题蓝色 */
                                            " color: white;"
                                            " border: none;"
                                            " border-radius: 4px;"
                                            " padding: 0px 0px;"
                                            " margin: 0px;"
                                            " }");

    QString style ="QTableWidget {"
                "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f9f9f9, stop:1 #f0f0f0);"
                "    gridline-color: #e0e0e0;"
                "    border: 1px solid #d0d0d0;"
                "    border-radius: 4px;"
                "}"
                "QTableWidget::item {"
                "    color: #333;"
                "    background-color: white;"
                "    border: none;"
                "    padding: 6px;"
                "}"
                "QTableWidget::item:alternate {"
                "    background-color: #f9f9f9;" // 交替行背景
                "}"
                "QTableWidget::item:hover {"
                "    background-color: #f0f7ff;" // 鼠标悬停背景
                "}"
                "QTableWidget::item:selected {"
                "    background-color: #c2e0ff;"
                "    color: #000;"
                "}"
                "QHeaderView::section {"
                "    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #e6f2ff, stop:1 #d9e6ff);"
                "    color: #222;"
                "    border: 1px solid #bbd6ff;"
                "    border-bottom: 2px solid #a3c6ff;"
                "    padding: 6px 12px;"
                "    font-weight: bold;"
                "    text-align: center;"
                "}"
                "QHeaderView::section:horizontal {"
                "    border-right: 2px solid #a3c6ff;" // 水平表头右侧边框
                "}"
                "QHeaderView::section:vertical {"
                "    border-right: 2px solid #a3c6ff;" // 垂直表头下边框
                "}";
     this->ui->tableWidget_sensor_parameter->setStyleSheet(style);
}

void project_create::tableWidget_sensor_parameter_init()
{
    sensor_config_message_default->class_to_tableWidget(sensor_config_message_default, this->ui->tableWidget_sensor_parameter);
}

project_create::~project_create()
{
    delete ui;
}

void project_create::on_pushButton_sensor_setting_clicked()
{
    sensor_list* sensor_list_ui = new sensor_list();
    sensor_list_ui->show();
}

void project_create::on_pushButton_path_clicked()
{
    QString path = QFileDialog::getExistingDirectory(this, "选择存储路径", "/");
    if (!path.isEmpty())
    {
        ui->lineEdit_path->setText(path);
    }
}

void project_create::on_pushButton_save_clicked()
{
    for(int i = 0; i < sensor_config_temp.size(); i++)
    {
        if(sensor_config_temp[i]->IP == this->ui->lineEdit_device_IP->text())
        {
            sensor_config_temp[i]->tableWidget_to_class(sensor_config_temp[i], this->ui->tableWidget_sensor_parameter, this->ui->lineEdit_device_IP->text());
            break;
        }
    }

    Project* project = new Project;

    //project->id;
    project->user_id = current_user->id;
    project->name = this->ui->lineEdit_name->text();
    project->description = this->ui->textEdit_description->toPlainText();
    project->file_path = this->ui->lineEdit_path->text();
    project->company = this->ui->lineEdit_company->text();
    project->Operator = this->ui->lineEdit_operator->text();
    project->status = 0;
    project->create_time = QTime::currentTime().toString("hh,mm,ss");

    QSqlQuery query;
    query.prepare("INSERT INTO top_project (id, user_id, name, description, file_path, company, operator, status, create_time) "
                  "VALUES (:id, :user_id, :name, :description, :file_path, :company, :operator, :status, :create_time);");
    query.bindValue(":user_id", project->user_id);
    query.bindValue(":name", project->name);

    query.bindValue(":description", project->description);
    query.bindValue(":file_path", project->file_path);
    query.bindValue(":company", project->company);

    query.bindValue(":operator", project->Operator);
    query.bindValue(":status", project->status);
    query.bindValue(":create_time", project->create_time);

    if(!query.exec())
    {
        qDebug() << "数据库连接失败：" << query.lastError().text() << __LINE__ << __FILE__;
    }

    query.exec("SELECT * FROM top_project;");
    if(!query.exec())
    {
        qDebug() << "数据库连接失败：" << query.lastError().text() << __LINE__ << __FILE__;
    }

    while(query.next())
    {
        project->id = query.value(0).toInt();
    }

    for(int i = 0; i < this->ui->lineEdit_divice_number->text().toInt(); i++)
    {
        sensor_config_temp[i]->sensor_config_save(project->id, "insert");
    }
}

void project_create::on_pushButton_cancel_clicked()
{
    this->close();
}

void project_create::on_pushButton_reload_clicked()
{

}

void project_create::on_lineEdit_divice_number_textChanged(const QString &arg1)
{
    this->ui->comboBox_device_choose->blockSignals(true);

    this->tableWidget_sensor_parameter_init();
    this->ui->comboBox_device_choose->clear();
    this->sensor_config_temp.resize(arg1.toInt());
    for(int i = 0; i < sensor_config_temp.size(); i++)
    {
        sensor_config_temp[i] =  new sensor_config(QString::number(i));
        this->ui->comboBox_device_choose->addItem(QString::number(i));
    }

    this->ui->comboBox_device_choose->blockSignals(false);
}

void project_create::on_lineEdit_device_IP_textChanged(const QString &arg1)
{
    this->ui->comboBox_device_choose->blockSignals(true);
    this->ui->lineEdit_device_IP->blockSignals(true);
    if(this->ui->lineEdit_divice_number->text().isEmpty())
    {
        QMessageBox::information(nullptr, "提示", "请输入设备数量");
        this->ui->lineEdit_device_IP->setText("");
        this->ui->lineEdit_device_IP->blockSignals(false);
        return;
    }
    this->ui->lineEdit_device_IP->blockSignals(false);

    int index = 0;
    for(int i = 0; i < sensor_config_temp.size(); i++)
    {
            if(this->ui->comboBox_device_choose->currentText() == sensor_config_temp[i]->IP)
            {
                for(int j = 0; j < this->ui->comboBox_device_choose->count(); j++)
                {
                    if(this->ui->comboBox_device_choose->itemText(j) == this->ui->comboBox_device_choose->currentText())
                    {
                        this->ui->comboBox_device_choose->setItemText(j, arg1);
                        break;
                    }
                }
                index = i;
                break;
            }
    }
    sensor_config_temp[index]->tableWidget_to_class(sensor_config_temp[index],this->ui->tableWidget_sensor_parameter, arg1);

    this->ui->comboBox_device_choose->blockSignals(false);
}

void project_create::on_comboBox_device_choose_currentTextChanged(const QString &arg1)
{
    this->ui->lineEdit_device_IP->blockSignals(true);

    for(int i = 0; i < sensor_config_temp.size(); i++)
    {
        if(sensor_config_temp[i]->IP == this->ui->lineEdit_device_IP->text())
        {
            sensor_config_temp[i]->tableWidget_to_class(sensor_config_temp[i], this->ui->tableWidget_sensor_parameter, this->ui->lineEdit_device_IP->text());
            break;
        }
    }
    for(int i = 0; i < sensor_config_temp.size(); i++)
    {
        if(sensor_config_temp[i]->IP == arg1)
        {
            this->ui->lineEdit_device_IP->setText(arg1);
            sensor_config_temp[i]->class_to_tableWidget(sensor_config_temp[i], this->ui->tableWidget_sensor_parameter);
            break;
        }
    }

    this->ui->lineEdit_device_IP->blockSignals(false);
}
