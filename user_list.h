#ifndef USER_LIST_H
#define USER_LIST_H

#include <QWidget>

namespace Ui {
class user_list;
}

class user_list : public QWidget
{
    Q_OBJECT

public:
    explicit user_list(QWidget *parent = nullptr);
    ~user_list();


private slots:
    void load_data(QString queryName);

    void on_pushButton_add_clicked();

    void on_pushButton_edit_clicked();

    void on_pushButton_delete_clicked();

    void on_pushButton_search_clicked();

    void on_tableWidget_activated(const QModelIndex &index);

    void on_tableWidget_clicked(const QModelIndex &index);

private:
    Ui::user_list *ui;

    QString data;
};

#endif // USER_LIST_H
