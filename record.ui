<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Record</class>
 <widget class="QWidget" name="Record">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>900</width>
    <height>500</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>数据回看</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="widget_record_box" native="true">
     <layout class="QVBoxLayout" name="verticalLayout">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QWidget" name="widget_tip" native="true">
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>40</height>
         </size>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <property name="spacing">
          <number>10</number>
         </property>
         <property name="leftMargin">
          <number>10</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QLabel" name="label_tip">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">color:red;</string>
           </property>
           <property name="text">
            <string>正在连接...</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_reconnect">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>24</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>60</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{
	background:#1890FF;
	border:none;
	color:#fafafa;
	border-radius:2px;
}
QPushButton:hover{
	background:#1281E8;
}</string>
           </property>
           <property name="text">
            <string>重连</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>537</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="widget_tool" native="true">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>40</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>40</height>
         </size>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_4">
         <property name="spacing">
          <number>10</number>
         </property>
         <property name="leftMargin">
          <number>10</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>10</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QDateTimeEdit" name="dateTimeEdit_start">
           <property name="minimumSize">
            <size>
             <width>160</width>
             <height>24</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">border:1px solid #ddd;
border-radius:2px;</string>
           </property>
           <property name="dateTime">
            <datetime>
             <hour>0</hour>
             <minute>0</minute>
             <second>0</second>
             <year>2021</year>
             <month>1</month>
             <day>1</day>
            </datetime>
           </property>
           <property name="displayFormat">
            <string>yyyy-MM-dd HH:mm:ss</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QDateTimeEdit" name="dateTimeEdit_end">
           <property name="minimumSize">
            <size>
             <width>160</width>
             <height>24</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">border:1px solid #ddd;
border-radius:2px;</string>
           </property>
           <property name="dateTime">
            <datetime>
             <hour>0</hour>
             <minute>0</minute>
             <second>0</second>
             <year>2020</year>
             <month>1</month>
             <day>1</day>
            </datetime>
           </property>
           <property name="displayFormat">
            <string>yyyy-MM-dd HH:mm:ss</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_search">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>24</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{
	background:#1890FF;
	border:none;
	color:#fafafa;
	border-radius:2px;
}
QPushButton:hover{
	background:#1281E8;
}</string>
           </property>
           <property name="text">
            <string>查询</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_pause">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>24</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{
	background:#ccc;
	border:none;
	color:#000;
	border-radius:2px;
}
QPushButton:hover{
	background:#bbb;
}</string>
           </property>
           <property name="text">
            <string>暂停</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_filter">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>24</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{
	background:#ccc;
	border:none;
	color:#000;
	border-radius:2px;
}
QPushButton:hover{
	background:#bbb;
}</string>
           </property>
           <property name="text">
            <string>关闭滤波</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="widget" native="true">
        <property name="styleSheet">
         <string notr="true">QWidget {
	background:#101010;
}</string>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_5">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QTableWidget" name="tableWidget_record">
           <property name="minimumSize">
            <size>
             <width>260</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>260</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">QTableWidget {
	background:#101010;
	border:none;
	border-left:1px solid #222;
	border-right:1px solid #222;
	margin:0 0 10px 10px;
}
QHeaderView {
    background:transparent;
}
QHeaderView::section{
	height:26px;
    color:#ccc;
    background:#111;
	border: none;
	border-right:1px solid #222;
	border-bottom:1px solid #222;
}
QTableWidget::item {
	border:none;
	color:#ccc;
	border-right:1px solid #222;
	border-bottom:1px solid #222;
}
QTableWidget::item::selected {
	color:#fff;     
	background:#1890FF;
}
QScrollBar{
	background:#222; 
	width:6px; 
	height:6px;
}</string>
           </property>
           <attribute name="horizontalHeaderDefaultSectionSize">
            <number>121</number>
           </attribute>
           <column>
            <property name="text">
             <string>工程</string>
            </property>
           </column>
           <column>
            <property name="text">
             <string>事件时间</string>
            </property>
           </column>
           <column>
            <property name="text">
             <string>事件数据</string>
            </property>
           </column>
          </widget>
         </item>
         <item>
          <widget class="QWidget" name="widget_canvas" native="true">
           <property name="styleSheet">
            <string notr="true">background:#000;</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
