/****************************************************************************
** Meta object code from reading C++ file 'ad_thread.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.8)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../ad_thread.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#include <QtCore/QVector>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ad_thread.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.8. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_AD_THREAD_t {
    QByteArrayData data[12];
    char stringdata0[103];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_AD_THREAD_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_AD_THREAD_t qt_meta_stringdata_AD_THREAD = {
    {
QT_MOC_LITERAL(0, 0, 9), // "AD_THREAD"
QT_MOC_LITERAL(1, 10, 16), // "send_recive_data"
QT_MOC_LITERAL(2, 27, 0), // ""
QT_MOC_LITERAL(3, 28, 15), // "QVector<double>"
QT_MOC_LITERAL(4, 44, 4), // "data"
QT_MOC_LITERAL(5, 49, 5), // "index"
QT_MOC_LITERAL(6, 55, 1), // "x"
QT_MOC_LITERAL(7, 57, 9), // "GetHandle"
QT_MOC_LITERAL(8, 67, 8), // "MyHANDLE"
QT_MOC_LITERAL(9, 76, 6), // "handle"
QT_MOC_LITERAL(10, 83, 14), // "recvThreadStop"
QT_MOC_LITERAL(11, 98, 4) // "stop"

    },
    "AD_THREAD\0send_recive_data\0\0QVector<double>\0"
    "data\0index\0x\0GetHandle\0MyHANDLE\0handle\0"
    "recvThreadStop\0stop"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_AD_THREAD[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       3,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    3,   29,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       7,    1,   36,    2, 0x08 /* Private */,
      10,    1,   39,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3, QMetaType::Int, QMetaType::Long,    4,    5,    6,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 8,    9,
    QMetaType::Void, QMetaType::Bool,   11,

       0        // eod
};

void AD_THREAD::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        AD_THREAD *_t = static_cast<AD_THREAD *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->send_recive_data((*reinterpret_cast< QVector<double>(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< long(*)>(_a[3]))); break;
        case 1: _t->GetHandle((*reinterpret_cast< MyHANDLE(*)>(_a[1]))); break;
        case 2: _t->recvThreadStop((*reinterpret_cast< bool(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 0:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QVector<double> >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            typedef void (AD_THREAD::*_t)(QVector<double> , int , long );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&AD_THREAD::send_recive_data)) {
                *result = 0;
                return;
            }
        }
    }
}

const QMetaObject AD_THREAD::staticMetaObject = {
    { &QThread::staticMetaObject, qt_meta_stringdata_AD_THREAD.data,
      qt_meta_data_AD_THREAD,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *AD_THREAD::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *AD_THREAD::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_AD_THREAD.stringdata0))
        return static_cast<void*>(this);
    return QThread::qt_metacast(_clname);
}

int AD_THREAD::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QThread::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 3)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 3)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    }
    return _id;
}

// SIGNAL 0
void AD_THREAD::send_recive_data(QVector<double> _t1, int _t2, long _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)), const_cast<void*>(reinterpret_cast<const void*>(&_t3)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
