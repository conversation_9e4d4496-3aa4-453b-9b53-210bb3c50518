#include "deal_thread.h"
#include "data_managent.h"
#include "message.h"
#include <QVariant>
#include <QVector>
#include <QDateTime>
#include <QMutexLocker>


deal_thread::deal_thread()
{

}

void deal_thread::deal(QVector<double> data, int index, long size)
{
    // QVector<double> adData = data.value<QVector<double>>();
     double freq = 10 * 1000;
    // int channel_number,round  ;

    // 使用传入的data参数而不是全局数组，避免竞争条件
    pktTime[index] = QDateTime::fromMSecsSinceEpoch(data[0]*1000);
    // pktTime[index] = QDateTime::fromMSecsSinceEpoch(data[0] * 1000);
  //  qDebug()<<pktTime[0]<<__LINE__<<__FILE__;
     if(nextad_event_flag[index] == 1)
     {
         for(int i = 0;i<m_xPointNum; ++i)
         {
            int s=1;
             for(int m=0;m<channel_count;m++)
             {
                 if(sensor_config_message[index]->channel_isEnable[m])
                 {

                     if(datapool[m+index*16].size()>=1224)
                     {
                             datapool[m+index*16].dequeue();
                     }
                     // 使用传入的data参数而不是全局数组
                     if(s < data.size()) {
                         datapool[m+index*16].enqueue(data[s]);
                     } else {
                         datapool[m+index*16].enqueue(0.0);  // 默认值
                     }
                     s++;
                 }
             }
         }
         emit sendData();
     }
     else
     {
         int round=0;
         for(int i = 0;i<m_xPointNum; ++i)
         {
            int s=1;
             for(int m=0;m<channel_count;m++)
             {
                 if(sensor_config_message[index]->channel_isEnable[m])
                 {
                     // 使用传入的data参数而不是全局数组
                     if(s < data.size()) {
                         event_next_data[m+index*16][round] = data[s];
                     } else {
                         event_next_data[m+index*16][round] = 0.0;  // 默认值
                     }
                     s++;
                 }
             }
             round++;
         }
         emit sendeventData(index);
     }

}
