#include "deal_thread.h"
#include "data_managent.h"
#include "message.h"
#include <QVariant>
#include <QVector>
#include <QDateTime>


deal_thread::deal_thread()
{

}

void deal_thread::deal(QVector<double> data, int index, long size)
{
    // QVector<double> adData = data.value<QVector<double>>();
     double freq = 10 * 1000;
    // int channel_number,round  ;
      pktTime[index] = QDateTime::fromMSecsSinceEpoch(recive_data[index][0]*1000);
     // pktTime[index] = QDateTime::fromMSecsSinceEpoch(data[0] * 1000);
  //  qDebug()<<pktTime[0]<<__LINE__<<__FILE__;
     if(nextad_event_flag[index] == 1)
     {
         for(int i = 0;i<m_xPointNum; ++i)
         {
            int s=1;
             for(int m=0;m<channel_count;m++)
             {
                 if(sensor_config_message[index]->channel_isEnable[m])
                 {

                     if(datapool[m+index*16].size()>=1224)
                     {
                             datapool[m+index*16].dequeue();
                     }
                     datapool[m+index*16].enqueue(recive_data[index][s]);
                     s++;
                 }
             }
         }
         emit sendData();
     }
     else
     {
         int round=0;
         for(int i = 0;i<m_xPointNum; ++i)
         {
            int s=1;
             for(int m=0;m<channel_count;m++)
             {
                 if(sensor_config_message[index]->channel_isEnable[m])
                 {
                  event_next_data[m+index*16][round]=recive_data[index][s];
                  s++;
                 }
             }
             round++;
         }
         emit sendeventData(index);
     }

}
