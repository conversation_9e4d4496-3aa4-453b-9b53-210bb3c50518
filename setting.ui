<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Setting</class>
 <widget class="QWidget" name="Setting">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>900</width>
    <height>500</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>系统配置</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <item>
    <widget class="QWidget" name="widget" native="true">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">QWidget{
	background:#fff;
}
QLineEdit{
	border:1px solid #bbb;
	border-radius:2px;
	color:#333;
}
QLineEdit:disabled{
	background:#f3f3f3;
}</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <item>
       <widget class="QTabWidget" name="tabWidget">
        <property name="font">
         <font>
          <family>Microsoft YaHei UI</family>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="tabShape">
         <enum>QTabWidget::Rounded</enum>
        </property>
        <property name="currentIndex">
         <number>2</number>
        </property>
        <property name="elideMode">
         <enum>Qt::ElideNone</enum>
        </property>
        <widget class="QWidget" name="tab_system">
         <attribute name="title">
          <string>系统信息</string>
         </attribute>
         <layout class="QHBoxLayout" name="horizontalLayout_3">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QWidget" name="widget_system" native="true">
            <layout class="QVBoxLayout" name="verticalLayout_2">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>20</number>
             </property>
             <property name="topMargin">
              <number>20</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QWidget" name="horizontalWidget" native="true">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>40</height>
                </size>
               </property>
               <property name="layoutDirection">
                <enum>Qt::RightToLeft</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_7">
                <property name="spacing">
                 <number>10</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <spacer name="horizontalSpacer_3">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>471</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_system_name">
                  <property name="enabled">
                   <bool>false</bool>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>300</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>300</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei UI</family>
                   </font>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="readOnly">
                   <bool>false</bool>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="label">
                  <property name="minimumSize">
                   <size>
                    <width>60</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei UI</family>
                   </font>
                  </property>
                  <property name="text">
                   <string>名称</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QWidget" name="widget_8" native="true">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>40</height>
                </size>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_12">
                <property name="spacing">
                 <number>10</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QLabel" name="label_8">
                  <property name="minimumSize">
                   <size>
                    <width>60</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei UI</family>
                   </font>
                  </property>
                  <property name="text">
                   <string>机器码</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_machine_code">
                  <property name="enabled">
                   <bool>false</bool>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>300</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>300</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei UI</family>
                   </font>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="styleSheet">
                   <string notr="true"/>
                  </property>
                  <property name="readOnly">
                   <bool>false</bool>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_8">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>471</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QWidget" name="widget_9" native="true">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>40</height>
                </size>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_13">
                <property name="spacing">
                 <number>10</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QLabel" name="label_9">
                  <property name="minimumSize">
                   <size>
                    <width>60</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei UI</family>
                   </font>
                  </property>
                  <property name="text">
                   <string>序列号</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_serial">
                  <property name="enabled">
                   <bool>false</bool>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>300</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>300</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei UI</family>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true"/>
                  </property>
                  <property name="readOnly">
                   <bool>false</bool>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_9">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>471</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QWidget" name="widget_10" native="true">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>40</height>
                </size>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_11">
                <property name="spacing">
                 <number>10</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QLabel" name="label_10">
                  <property name="minimumSize">
                   <size>
                    <width>60</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei UI</family>
                   </font>
                  </property>
                  <property name="text">
                   <string>过期时间</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_expire_time">
                  <property name="enabled">
                   <bool>false</bool>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>300</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>300</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei UI</family>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true"/>
                  </property>
                  <property name="readOnly">
                   <bool>false</bool>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_10">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>471</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QWidget" name="horizontalWidget" native="true">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>40</height>
                </size>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_34">
                <property name="spacing">
                 <number>10</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QLabel" name="label_49">
                  <property name="minimumSize">
                   <size>
                    <width>60</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei UI</family>
                   </font>
                  </property>
                  <property name="text">
                   <string>公司</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_company">
                  <property name="enabled">
                   <bool>false</bool>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>300</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>300</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei UI</family>
                   </font>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_31">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>471</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QWidget" name="horizontalWidget_2" native="true">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>40</height>
                </size>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_35">
                <property name="spacing">
                 <number>10</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QLabel" name="label_50">
                  <property name="minimumSize">
                   <size>
                    <width>60</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei UI</family>
                   </font>
                  </property>
                  <property name="text">
                   <string>联系电话</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_phone">
                  <property name="enabled">
                   <bool>false</bool>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>300</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>300</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei UI</family>
                   </font>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_33">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>471</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QWidget" name="horizontalWidget" native="true">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>40</height>
                </size>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_36">
                <property name="spacing">
                 <number>10</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QLabel" name="label_51">
                  <property name="minimumSize">
                   <size>
                    <width>60</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei UI</family>
                   </font>
                  </property>
                  <property name="text">
                   <string>版本号</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLineEdit" name="lineEdit_version">
                  <property name="enabled">
                   <bool>false</bool>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>300</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>300</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft YaHei UI</family>
                   </font>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_32">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>471</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <spacer name="verticalSpacer_2">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>150</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="tab_tcp">
         <attribute name="title">
          <string>TCP服务</string>
         </attribute>
         <widget class="QWidget" name="widget_tcp" native="true">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>0</y>
            <width>871</width>
            <height>401</height>
           </rect>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_3">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="leftMargin">
            <number>20</number>
           </property>
           <property name="topMargin">
            <number>20</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="QWidget" name="widget_4" native="true">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>40</height>
              </size>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_4">
              <property name="spacing">
               <number>10</number>
              </property>
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QLabel" name="label_2">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="text">
                 <string>TCP服务端 端口</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit_server_port">
                <property name="minimumSize">
                 <size>
                  <width>300</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>300</width>
                  <height>26</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true"/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_5">
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:#666;</string>
                </property>
                <property name="text">
                 <string>用于接受ASP连接，推送文件消息</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_4">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QWidget" name="widget_5" native="true">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>40</height>
              </size>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_5">
              <property name="spacing">
               <number>10</number>
              </property>
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QLabel" name="label_3">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="text">
                 <string>TCP客户端 端口</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit_client_port">
                <property name="minimumSize">
                 <size>
                  <width>300</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>300</width>
                  <height>26</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true"/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_6">
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:#666;</string>
                </property>
                <property name="text">
                 <string>用于连接微震设备，获取数据</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_5">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QWidget" name="widget_6" native="true">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>40</height>
              </size>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_6">
              <property name="spacing">
               <number>10</number>
              </property>
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QLabel" name="label_4">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="text">
                 <string>TCP客户端 心跳</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit_client_delay">
                <property name="minimumSize">
                 <size>
                  <width>300</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>300</width>
                  <height>26</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true"/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_7">
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:#666;</string>
                </property>
                <property name="text">
                 <string>查询微震设备时间间隔/毫秒</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_6">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <spacer name="verticalSpacer">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>178</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </widget>
        <widget class="QWidget" name="tab_device">
         <attribute name="title">
          <string>设备配置</string>
         </attribute>
         <widget class="QTableWidget" name="tableWidget_device_config">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>0</y>
            <width>881</width>
            <height>411</height>
           </rect>
          </property>
          <column>
           <property name="text">
            <string>通道号</string>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>采样率</string>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>增益</string>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>通道使用状态</string>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>跳线选择(电流/电压)</string>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>AD最大等待时间</string>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>缓冲区大小</string>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>是否使用校准</string>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>是否返回时间戳</string>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>传输通路(TCP/UDP)</string>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>AD启动方式</string>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>X坐标</string>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>Y坐标</string>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>Z坐标</string>
           </property>
           <property name="font">
            <font>
             <pointsize>12</pointsize>
            </font>
           </property>
          </column>
         </widget>
         <widget class="QPushButton" name="pushButton_save">
          <property name="geometry">
           <rect>
            <x>80</x>
            <y>420</y>
            <width>80</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>保存</string>
          </property>
         </widget>
         <widget class="QComboBox" name="comboBox_cardID">
          <property name="geometry">
           <rect>
            <x>190</x>
            <y>420</y>
            <width>62</width>
            <height>22</height>
           </rect>
          </property>
          <item>
           <property name="text">
            <string>0</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>1</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>2</string>
           </property>
          </item>
         </widget>
        </widget>
        <widget class="QWidget" name="tab_event">
         <property name="font">
          <font>
           <family>Microsoft YaHei UI</family>
          </font>
         </property>
         <attribute name="title">
          <string>事件配置</string>
         </attribute>
         <widget class="QWidget" name="widget_event" native="true">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>0</y>
            <width>871</width>
            <height>401</height>
           </rect>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_5">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="leftMargin">
            <number>20</number>
           </property>
           <property name="topMargin">
            <number>20</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="QWidget" name="widget_11" native="true">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>40</height>
              </size>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_29">
              <property name="spacing">
               <number>10</number>
              </property>
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QLabel" name="label_12">
                <property name="minimumSize">
                 <size>
                  <width>60</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="text">
                 <string>事件阈值</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit_threshold">
                <property name="minimumSize">
                 <size>
                  <width>300</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>300</width>
                  <height>26</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true"/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_13">
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:#666;</string>
                </property>
                <property name="text">
                 <string>数值越低，灵敏度越高，范围1.0~3.0</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_11">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QWidget" name="widget_28" native="true">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>40</height>
              </size>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_30">
              <property name="spacing">
               <number>10</number>
              </property>
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QLabel" name="label_14">
                <property name="minimumSize">
                 <size>
                  <width>60</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="text">
                 <string>有效道数</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit_effective">
                <property name="minimumSize">
                 <size>
                  <width>300</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>300</width>
                  <height>26</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true"/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_15">
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:#666;</string>
                </property>
                <property name="text">
                 <string>震动道数大于有效道数时，视为有效事件，不得超过DSU连接的检波器数量</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_29">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QWidget" name="widget_29" native="true">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>40</height>
              </size>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_31">
              <property name="spacing">
               <number>10</number>
              </property>
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QLabel" name="label_16">
                <property name="minimumSize">
                 <size>
                  <width>60</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="text">
                 <string>数据池长度</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit_record_length">
                <property name="minimumSize">
                 <size>
                  <width>300</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>300</width>
                  <height>26</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true"/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_17">
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:#666;</string>
                </property>
                <property name="text">
                 <string>事件数据池需收集的数据长度，范围500~2000</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_30">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <spacer name="verticalSpacer_3">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>178</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </widget>
        <widget class="QWidget" name="tab_db">
         <attribute name="title">
          <string>数据库配置</string>
         </attribute>
         <widget class="QWidget" name="widget_db" native="true">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>0</y>
            <width>620</width>
            <height>498</height>
           </rect>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_6">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="leftMargin">
            <number>20</number>
           </property>
           <property name="topMargin">
            <number>20</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="QWidget" name="widget_18" native="true">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>40</height>
              </size>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_19">
              <property name="spacing">
               <number>10</number>
              </property>
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QLabel" name="label_30">
                <property name="minimumSize">
                 <size>
                  <width>60</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="text">
                 <string>本地数据库</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QWidget" name="widget_26" native="true">
                <property name="minimumSize">
                 <size>
                  <width>300</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>300</width>
                  <height>16777215</height>
                 </size>
                </property>
                <layout class="QHBoxLayout" name="horizontalLayout_28">
                 <property name="spacing">
                  <number>20</number>
                 </property>
                 <property name="leftMargin">
                  <number>0</number>
                 </property>
                 <property name="topMargin">
                  <number>0</number>
                 </property>
                 <property name="rightMargin">
                  <number>0</number>
                 </property>
                 <property name="bottomMargin">
                  <number>0</number>
                 </property>
                 <item>
                  <widget class="QRadioButton" name="radioButton_db_type_1">
                   <property name="maximumSize">
                    <size>
                     <width>100</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <family>Microsoft YaHei UI</family>
                    </font>
                   </property>
                   <property name="text">
                    <string>启用</string>
                   </property>
                   <property name="checked">
                    <bool>false</bool>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QRadioButton" name="radioButton_db_type_2">
                   <property name="maximumSize">
                    <size>
                     <width>100</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <family>Microsoft YaHei UI</family>
                    </font>
                   </property>
                   <property name="text">
                    <string>时序数据库</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QRadioButton" name="radioButton_db_type_0">
                   <property name="maximumSize">
                    <size>
                     <width>100</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <family>Microsoft YaHei UI</family>
                    </font>
                   </property>
                   <property name="text">
                    <string>停用</string>
                   </property>
                   <property name="checked">
                    <bool>true</bool>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_27">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_31">
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:#666;</string>
                </property>
                <property name="text">
                 <string>选择时序数据库需安装并配置连接信息</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_18">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QWidget" name="widget_19" native="true">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>40</height>
              </size>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_20">
              <property name="spacing">
               <number>10</number>
              </property>
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QLabel" name="label_32">
                <property name="minimumSize">
                 <size>
                  <width>60</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="text">
                 <string>主机</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit_db_host">
                <property name="minimumSize">
                 <size>
                  <width>300</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>300</width>
                  <height>26</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true"/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_33">
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:#666;</string>
                </property>
                <property name="text">
                 <string>时序数据库所在服务器的IP</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_19">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QWidget" name="widget_20" native="true">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>40</height>
              </size>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_21">
              <property name="spacing">
               <number>10</number>
              </property>
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QLabel" name="label_34">
                <property name="minimumSize">
                 <size>
                  <width>60</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="text">
                 <string>端口</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit_db_port">
                <property name="minimumSize">
                 <size>
                  <width>300</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>300</width>
                  <height>26</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true"/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_35">
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:#666;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_20">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QWidget" name="widget_21" native="true">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>40</height>
              </size>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_22">
              <property name="spacing">
               <number>10</number>
              </property>
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QLabel" name="label_36">
                <property name="minimumSize">
                 <size>
                  <width>60</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="text">
                 <string>数据库</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit_db_name">
                <property name="minimumSize">
                 <size>
                  <width>300</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>300</width>
                  <height>26</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true"/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_37">
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:#666;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_21">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QWidget" name="widget_22" native="true">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>40</height>
              </size>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_23">
              <property name="spacing">
               <number>10</number>
              </property>
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QLabel" name="label_38">
                <property name="minimumSize">
                 <size>
                  <width>60</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="text">
                 <string>用户名</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit_db_user">
                <property name="minimumSize">
                 <size>
                  <width>300</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>300</width>
                  <height>26</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true"/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_39">
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:#666;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_22">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QWidget" name="widget_23" native="true">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>40</height>
              </size>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_24">
              <property name="spacing">
               <number>10</number>
              </property>
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QLabel" name="label_40">
                <property name="minimumSize">
                 <size>
                  <width>60</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="text">
                 <string>密码</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit_db_pwd">
                <property name="minimumSize">
                 <size>
                  <width>300</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>300</width>
                  <height>26</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true"/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_41">
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:#666;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_23">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <spacer name="verticalSpacer_5">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>178</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </widget>
        <widget class="QWidget" name="tab_file">
         <attribute name="title">
          <string>数据文件配置</string>
         </attribute>
         <widget class="QWidget" name="widget_file" native="true">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>0</y>
            <width>600</width>
            <height>298</height>
           </rect>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_7">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="leftMargin">
            <number>20</number>
           </property>
           <property name="topMargin">
            <number>20</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="QWidget" name="widget_24" native="true">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>40</height>
              </size>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_25">
              <property name="spacing">
               <number>10</number>
              </property>
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QLabel" name="label_42">
                <property name="minimumSize">
                 <size>
                  <width>70</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="text">
                 <string>数据文件功能</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QWidget" name="widget_3" native="true">
                <property name="minimumSize">
                 <size>
                  <width>300</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>300</width>
                  <height>16777215</height>
                 </size>
                </property>
                <layout class="QHBoxLayout" name="horizontalLayout_27">
                 <property name="spacing">
                  <number>20</number>
                 </property>
                 <property name="leftMargin">
                  <number>0</number>
                 </property>
                 <property name="topMargin">
                  <number>0</number>
                 </property>
                 <property name="rightMargin">
                  <number>0</number>
                 </property>
                 <property name="bottomMargin">
                  <number>0</number>
                 </property>
                 <item>
                  <widget class="QRadioButton" name="radioButton_enable_file_1">
                   <property name="maximumSize">
                    <size>
                     <width>60</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <family>Microsoft YaHei UI</family>
                    </font>
                   </property>
                   <property name="text">
                    <string>启用</string>
                   </property>
                   <property name="checked">
                    <bool>true</bool>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QRadioButton" name="radioButton_enable_file_0">
                   <property name="maximumSize">
                    <size>
                     <width>60</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <family>Microsoft YaHei UI</family>
                    </font>
                   </property>
                   <property name="text">
                    <string>停用</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_26">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_43">
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:#666;</string>
                </property>
                <property name="text">
                 <string>保存dat文件，供ASP分析</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_24">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QWidget" name="widget_25" native="true">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>40</height>
              </size>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_26">
              <property name="spacing">
               <number>10</number>
              </property>
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QLabel" name="label_44">
                <property name="minimumSize">
                 <size>
                  <width>70</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="text">
                 <string>默认存储路径</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit_file_path">
                <property name="minimumSize">
                 <size>
                  <width>300</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>300</width>
                  <height>26</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true"/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_45">
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:#666;</string>
                </property>
                <property name="text">
                 <string>dat文件的默认存储路径</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_25">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <spacer name="verticalSpacer_6">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>178</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </widget>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
