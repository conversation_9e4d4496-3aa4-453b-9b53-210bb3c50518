#ifndef DATA_MANAGENT_H
#define DATA_MANAGENT_H

#include "common.h"
#include "ad_thread.h"
#include "deal_thread.h"
#include "event_thread.h"
#include "store_thread.h"
#include "full_store.h"
#include <QQueue>
#include <QMutex>


const int m_xPointNum = 1024;
const int total_channel_count = 48;

extern MyHANDLE pHandle[3];
extern AD_THREAD *pThread0,*pThread1,*pThread2;
extern QDateTime pktTime0, pktTime1, pktTime2;

extern deal_thread* dealthread;
extern event_thread* eventthread;
extern store_thread* storethread;
extern full_store*  fullstore0;
extern full_store*  fullstore1;
extern full_store*  fullstore2;
extern QQueue<double> datapool[];
extern bool nextad_event_flag[3];
extern bool event_coming;
extern QDateTime pktTime[3];
extern double event_next_data[48][1024];
extern double datapool_tosave[48][1024];

extern QVector<double> recive_data[3];
extern QMutex recive_data_mutex[3];  // 为每个数据数组添加互斥锁

class data_managent
{
public:
    data_managent();
};

void data_and_thread_init();

#endif // DATA_MANAGENT_H
