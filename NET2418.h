﻿#ifndef _net2418_H_
#define _net2418_H_

#include <windows.h>

#define XC_DRIVER_VERSION       1.00// 当前DLL版本
//注意，当前.h罗列的功能可能比net2418卡支持的功能要多，用户可参考说明书
/*	net2418 DLL更改記錄
	v1.0.0:2024年8月7日08:59:30
	首次发布
*/

//定义指令
enum OrderCode
{
//【IOType指令类型
	#define XC_ioPUT_CONFIG                 1000	//配置卡参数
	#define XC_ioGET_CONFIG                 1001	//获取卡参数
	#define XC_ASSIST_ORDER	                1002	//辅助类指令

	// AD
	#define XC_ioSTART_STREAM               202		//启动AD流
	#define XC_ioSTOP_STREAM                203		//停止AD流
	#define XC_ioGET_STREAM_DATA			204		//获取AD流数据

	#define XC_ioGET_STREAM_LENGTH			205		//获取AD流长度
	#define XC_ioPUT_STREAM_BUFFER			206		//配置上位机为AD流开辟缓冲区大小，默认50M
	#define XC_ioPUT_STREAM_DATAPACKAGE		214		//获取AD流原始数据包

	#define XC_ioPUT_KEEPALIVE				210		//配置keepalive参数
	#define XC_ioPUT_STREAM_TIME			211		//配置是否随AD数据返回时间戳
	#define XC_ioGET_STREAM_ERROR			212		//获取AD流收取过程错误信息

	// RTC时间
	#define XC_ioRST_RTC					300		//校正板卡RTC时间
	#define XC_ioGET_RTC					301		//获取板卡RTC时间

	//memory
	#define XC_ioGET_MEM					401		//硬件512字节存储空间读
	#define XC_ioSET_MEM					402		//硬件512字节存储空间写
//】

//【Channel指令类型
	// 卡基本参数
	#define XC_ctETHERNET_LOCALIP			1800	//设置选择本地IP
	#define XC_chDEVICE_IP                  0		//设备IP
	#define XC_chDEVICE_NETMASK             1		//设备子网掩码
	#define XC_chDEVICE_GATE				2		//设备网关
	#define XC_chDEVICE_MAC					3		//设备MAC
	#define XC_chDEVICE_DHCP				4		//设备DHCP  目前未实现
	#define XC_chHARDWARE_VERSION           10		//设备硬件版本号
	#define XC_chPORT_NUMBER                13		//主机tcp数据传输通路端口号
	#define XC_chUDPPORT_NUMBER             43		//主机udp数据传输通路端口号
	#define XC_chSTREAM_STATUS              14		//数据传输通路状态
	#define XC_chFIRMWARE_VERSION           11		//固件版本(arm)
	#define XC_chSERIAL_NUMBER              15		//序列号
	#define XC_chPRODUCTID                  8		//产品ID 2418
	#define XC_chDEVICE_EFFECT				9		//网络配置生效指令
	#define XC_chUDPORDER_PORT				44		//配置本地udp指令端口，默认16666

	// AD参数
	#define XC_chSTREAM_COMMUNICATION_TIMEOUT	21	//配置AD等待通信完成的时长(ms)
	#define XC_ioPUT_AIN_RANGE					2000//配置AD增益
	#define XC_chSTREAM_SCAN_FREQUENCY			4000//配置采样率频率，单位 KHz，范围0.1KHz~102.4KHz
	#define XC_chSTREAM_Channel					4008//配置所有通道选择情况
	#define XC_ioPUT_ConstantVolSource			4106//配置恒流源及所有通道交直流
	#define XC_chSTREAM_ADJ						4200//配置是否使用校准，校准详情参照说明书


	// RTC时间
	#define XC_ioRST_RTC_AUTO				0		//自动更新rtc时间
	#define XC_ioRST_RTC_MANUAL				1		//通过pValue参数手动配置rtc时间

	//memory
	#define XC_ioMEM_Double             0		//通过double指针读写存储空间
	#define XC_ioMEM_Long				1		//通过long指针读写存储空间

	// 授时参数
	#define XC_ioTIME_GPS_NTP				290		//gps或ntp授时
	#define XC_ioTIME_getGPS				291		//获得gps授时
	#define XC_ioTIME_getNTP				292		//获得ntp授时

	#define XC_ioTIME_PTP					295		//配置及获取ptp授时方式，0-ptp主，1-ptp从，2-关

	// 卡本地存储参数
	#define XC_ioSAVE_CONFIG				2999		//配置及获取存储参数
	#define XC_ioSAVE_LOCATION				3000		//配置及获取存储位置，0-SD，1-U盘，2-内部存储
	#define XC_ioSAVE_SDCard				3001		//配置及获取SD卡工作状态
	#define XC_ioSAVE_USB					3002		//配置及获取U盘工作状态
	#define XC_ioSAVE_Internal				3003		//配置及获取内部存储工作状态
	
	//4G
	#define XC_io4G							30		//获得4G模块状态

	//GPS
	#define XC_ioGPS_GetLoc					400		//获得GPS定位


	//辅助指令，即非板卡相关指令，用于帮助用户处理难处理的数据
	#define XC_ASSIST_TimeStamp				300		//格林威治时间戳转换时间字符串

//】

//【Value指令类型
	// 授时参数
	#define XC_ioTIME_chooseGPS				0		//gps授时优先
	#define XC_ioTIME_chooseNTP				1		//ntp授时优先

	//AD恒流源及交直流
	#define XC_CVS					0     // 开启恒流源，交流
	#define XC_DC					1     // 关闭恒流源，直流
	#define XC_AC					2     // 关闭恒流源，交流
	#define XC_CVSAC				3     // 开启恒流源，直流 

	//AD校准
	#define XC_NonADJ				0     // 不使用校准
	#define XC_ADJ					1     // 使用校准

	//AD时间戳
	#define XC_NonStreamTime		0     // 不使用时间戳
	#define XC_StreamTime			1     // 使用时间戳

//】

//【x1指令类型

//】
};

//定义错误
enum ErrorCode
{
	#define DAQE_NOERROR								0//无错误
	#define DAQE_REQUEST_NOT_PROCESSED					17 //请求没有处理
	#define ERROR_NO_MORE_DATA_AVAILABLE				18//全部结果均已返回
	#define DAQE_SCRATCH_ERROR							19//未查询到GetResult所需结果
	#define DAQE_DATA_BUFFER_OVERFLOW					20//数据缓冲区溢出
	#define DAQE_DAC_CONFIG_ERROR						73//DA参数输入错误
	#define DAQE_DAC_ERROR								74//DA参数设置错误
	#define DAQE_SOCKET_ERROR							75//Socket创建错误失败
	#define DAQE_SHT_SERIAL_RESET						55//设备重置失败
	#define DAQE_STREAM_TIMEOUT							58//流等待超时
	#define DAQE_UNKNOWN_ERROR							1001//未知错误
	#define DAQE_INVALID_HANDLE							1003//无效句柄
	#define DAQE_DEVICE_NOT_OPEN						1004//设备未打开
	#define DAQE_DAQDEVICE_NOT_FOUND					1007//未找到设备
	#define DAQE_COMM_FAILURE							1008//通讯错误
	#define DAQE_COMM_TIMEOUT							1011//通讯超时
	#define DAQE_INVALID_CONNECTION_TYPE				1013//当前模式无此功能
	#define DAQE_CONFIG_ERROR							1014//参数错误
	#define DAQE_COMM_FAILURE_DONE						1015//通讯错误，已完成指令对应的本地操作
	#define DAQE_TCP_NOT_CREATE							1016//TCP未创建
	#define DAQE_TCPBUFFER_ERROR						1017//TCP数据错误
	#define DAQE_AD_ERROR								1018//采集步骤错误

};


#ifdef  __cplusplus
	extern "C"
{
#endif

	/*
	*@brief		罗列当前可用设备
	*@param[out] NumFound:		指针，返回设备数量
	*@param[out] ID:			指针，指向序列号数组，数组大小最大为当前网络环境可找寻到的卡数
	*@param[out] Address:		指针，指向拨码设备IP数组，数组大小最大为当前网络环境可找寻到的卡数
	*@return	如果调用成功，返回0；如果调用失败，返回错误代码。
	*@note	按照找到设备的顺序，设备拨码ID和设备IP分别填在数组的相应元素上
	*/
	int WINAPI ListAll(long *pNumFound, long *pIDs, double *pAddresses);

	/*
	*@brief	打开指定物理设备
	*@param[in]	Address:	卡IP地址，举例"************"。注意这是一个字符串。
	*@param[in]	FirstFound:	如果为true，则忽略Address并找到第一个可用的net2418设备。
	*@param[out]pHandle:	此设备上用于后续函数的句柄，如果失败，则为NULL
	*@return	如果打开成功，返回0，pHandle返回打开设备的句柄；如果打开失败，返回错误代码。
	*@note 使用设备前必须调用
	*/
	int WINAPI OpenDAQDevice(const char *pAddress, long FirstFound, HANDLE *pHandle);

	/*
	*@brief	AD数据传输通路控制
	*@param[in] Handle:		OpenDAQDevice返回的设备句柄
	*@param[in] ADControl:	传输通路控制，0-关闭，1-开启
	*@param[in] AType:		通路类型，0-TCP，1-UDP
	*@param[in][out] portNO:指针，指向上位机端口号，0-随机端口，并返回实际使用端口号，其他数-指定端口
	*@return	如果调用成功，返回0；如果调用失败，返回错误代码。
	*/
	int WINAPI eControlADAccess(HANDLE Handle, long ADControl, long AType, long* portNO);

	/*
	*@brief	获取函数
	直接调用则将执行AddRequest、Go和GetResult三个函数
	*@param[in] Handle:		OpenDAQDevice返回的设备句柄
	*@param[in]IOType:		获取操作请求的类型
	*@param[in]Channel:		IOType操作通道
	*@param[out]pValue:		指向接收数据值的指针
	*@param[in]px1:			指向某些IOType使用的可选参数的指针
	*@return	如果调用成功，返回0；如果调用失败，返回错误代码。
	*/
	int WINAPI eGet(HANDLE Handle, long IOType, long Channel, double *pValue, LONG *px1);

	/*
	*@brief	配置函数
	直接调用则将执行AddRequest、Go和GetResult三个函数
	*@param[in] Handle:		OpenDAQDevice返回的设备句柄
	*@param[in]IOType:		配置操作请求的类型
	*@param[in]Channel:		IOType操作通道
	*@param[in]pValue:		指向配置数据值的指针
	*@param[in]px1:			指向某些IOType使用的可选参数的指针
	*@return	如果调用成功，返回0；如果调用失败，返回错误代码。
	*/
	int WINAPI ePut(HANDLE Handle, long IOType, long Channel, double *pValue, LONG *px1);
	
	/*
	*@brief	设备重置
	*@param[in] Handle:		OpenDAQDevice返回的设备句柄
	*@param[in] Mode:		配置重置类型。0=功能重置，1=网络配置恢复出厂配置，2=FPGA重置,3=AD重启
	*@return	如果调用成功，返回0；如果调用失败，返回错误代码。
	*/
	int WINAPI ResetDAQDevice(HANDLE Handle, long Mode);
	
	/*
	*@brief	根据错误代码获取字符串格式的含义
	*@param[in] ErrorCode:		错误代码
	*@param[out] String:		返回错误代码所代表的含义,应指向长度不小于256的缓存区
	*/
	void _stdcall ErrorToString(int ErrorCode, char *pString);

	/*
	*@brief	获取dll版本
	*@return	返回dll版本。
	*/
	double _stdcall GetDriverVersion(void);
	
	/*
	*@brief	添加操作请求
	*@param[in] Handle:		OpenDAQDevice返回的设备句柄
	*@param[in]IOType:		操作请求的类型（见上文定义）
	*@param[in]Channel:		IOType操作通道
	*@param[in]Value:		为输出通道传递的值
	*@param[in]x1:			某些IOType使用的可选参数
	*@param[in]UserData:	只随请求一起传递的数据
	*@return	如果调用成功，返回0；如果调用失败，返回错误代码。
	*/
	int WINAPI AddRequest(HANDLE Handle, long IOType, long Channel, double Value, LONGLONG x1, double UserData);

	/*
	*@brief	运行所有已被添加的操作请求
	* @return	如果调用成功，返回0；如果调用失败，返回错误代码。
	*/
	int WINAPI Go();

	/*
	*@brief	获取操作请求结果
	*@param[in] Handle:		OpenDAQDevice返回的设备句柄
	*@param[in]IOType:		操作请求的类型
	*@param[in]Channel:		IOType操作通道
	*@param[out]pValue:		指向接收数据值的指针
	*@return	如果调用成功，返回0；如果调用失败，返回错误代码。
	*/
	int WINAPI GetResult(HANDLE Handle, long IOType, long Channel, double *pValue);

	/*
	*@brief	关闭指定设备
	*@param[in] Handle:		OpenDAQDevice返回的设备句柄
	*@return	如果调用成功，返回0；如果调用失败，返回错误代码。
	*/
	int WINAPI CloseDAQDevice(HANDLE Handle);

#ifdef  __cplusplus

	} // extern C
#endif

#endif  /* _net2418_H_ */
