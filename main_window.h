#ifndef MAIN_WINDOW_H
#define MAIN_WINDOW_H

#include"project_list.h"
#include"project_create.h"
#include"sensor_list.h"

#include"monitor.h"
#include"record.h"

#include "user_list.h"
#include "user_create.h"
#include "user_edit.h"

#include"setting.h"
#include "user_main.h"
#include"more.h"

#include <QWidget>
#include <QStackedWidget>
#include <QMenuBar>
#include <QMainWindow>

namespace Ui {
class main_window;
}



class main_window : public QMainWindow
{
    Q_OBJECT

public:
    explicit main_window(QWidget *parent = nullptr);
    ~main_window();

    enum ui_index
    {
        project_list_index = 0,
        project_create_index,
        project_edit_index,

        monitor_index,
        record_index,

        user_list_index,
        user_create_index,
        user_edit_index,

        setting,
        more
    };

    Project_list* project_list_ui;
    class project_create* project_create_ui;
    sensor_list* sensor_list_ui;

    monitor* monitor_ui;
    Record*  record_ui;

    user_list* user_list_ui;
    user_create* user_create_ui;
    user_edit* user_edit_ui;

    Setting* setting_ui;
    More* more_ui;

    //菜单栏
    QMenuBar* menu_bar;

    //一级菜单
    QMenu* project_manage_menu;
    QMenu* monitor_and_record_menu;
    QMenu* user_manage_menu;
    QMenu* setting_menu;

    QStackedWidget *stackedWidget;

    void first_level_menu_init();
    void sencond_level_menu_init();



private slots:


//    void on_toolButton_project_clicked();

//    void on_toolButton_setting_clicked();

//    void on_toolButton_user_clicked();

//    void on_toolButton_record_clicked();

//    void on_toolButton_more_clicked();

//    void on_toolButton_monitor_clicked();

private:
    Ui::main_window *ui;
};

#endif // MAIN_WINDOW_H
