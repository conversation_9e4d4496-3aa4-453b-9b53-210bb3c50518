#include "project_list.h"
#include "ui_project_list.h"
#include "project_create.h"
#include "project_edit.h"
#include "message.h"
#include "sensor_list.h"

#include <QSqlQuery>
#include <QSqlError>
#include <QDebug>
#include <QTableWidget>
#include <QMessageBox>
#include <QPushButton>
#include <QSqlDatabase>

Project_list::Project_list(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::Project_list)
{
    ui->setupUi(this);

    this->ui->tableWidget->setColumnWidth(0, 100);
    this->ui->tableWidget->setColumnWidth(1, 100);
    this->ui->tableWidget->setColumnWidth(2, 700);
    this->ui->tableWidget->setColumnWidth(3, 400);
    this->ui->tableWidget->setColumnWidth(4, 150);
    this->ui->tableWidget->setColumnWidth(5, 600);
    this->ui->tableWidget->setColumnWidth(6, 100);

    this->load_data(nullptr);

    QString style ="QTableWidget {"
                    "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f9f9f9, stop:1 #f0f0f0);"
                    "    gridline-color: #e0e0e0;"
                    "    border: 1px solid #d0d0d0;"
                    "    border-radius: 4px;"
                    "}"
                    "QTableWidget::item {"
                    "    color: #333;"
                    "    background-color: white;"
                    "    border: none;"
                    "    padding: 6px;"
                    "}"
                    "QTableWidget::item:alternate {"
                    "    background-color: #f9f9f9;" // 交替行背景
                    "}"
                    "QTableWidget::item:hover {"
                    "    background-color: #f0f7ff;" // 鼠标悬停背景
                    "}"
                    "QTableWidget::item:selected {"
                    "    background-color: #c2e0ff;"
                    "    color: #000;"
                    "}"
                    "QHeaderView::section {"
                    "    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #e6f2ff, stop:1 #d9e6ff);"
                    "    color: #222;"
                    "    border: 1px solid #bbd6ff;"
                    "    border-bottom: 2px solid #a3c6ff;"
                    "    padding: 6px 12px;"
                    "    font-weight: bold;"
                    "    text-align: center;"
                    "}"
                    "QHeaderView::section:horizontal {"
                    "    border-right: 2px solid #a3c6ff;" // 水平表头右侧边框
                    "}"
                    "QHeaderView::section:vertical {"
                    "    border-right: 2px solid #a3c6ff;" // 垂直表头下边框
                    "}";
     this->ui->tableWidget->setStyleSheet(style);

}

Project_list::~Project_list()
{
    delete ui;
}

void Project_list::on_pushButton_search_clicked()
{
    if(!this->ui->lineEdit_name->text().isEmpty())
    {
        this->load_data(this->ui->lineEdit_name->text());
    }
}

void Project_list::on_pushButton_reload_clicked()
{
    this->ui->lineEdit_name->clear();
    this->load_data(nullptr);
}

void Project_list::load_data(QString queryName)
{
    this->ui->tableWidget->blockSignals(true);
    this->ui->tableWidget->setRowCount(0);

    QSqlQuery query;
    QString sql = "SELECT id, name, file_path, company, status, create_time FROM top_project";
    if (!queryName.isEmpty())
    {
        sql += " WHERE name LIKE '%" + queryName + "%'";
    }
    if (!query.exec(sql))
    {
        qDebug() << "Query error:" << query.lastError().text() << __LINE__;
        return;
    }

    int row = 0;
    while (query.next())
    {
        this->ui->tableWidget->insertRow(row);
        this->ui->tableWidget->setRowHeight(row, 100);
        for (int col = 0; col < this->ui->tableWidget->columnCount(); ++col)
        {
            if (col == this->ui->tableWidget->columnCount() - 3)
            {
                int status = query.value("status").toInt();
                QPushButton* button = new QPushButton();
                if (status == 1)
                {   
                    button->setText("使用中");
                    button->setStyleSheet(
                        "color: green;"
                        "background-color: white;"
                    );
                }
                else
                {
                    button->setText("已关闭");
                    button->setStyleSheet(
                        "color: red;"
                        "background-color: white;"
                    );
                }
                connect(button, &QPushButton::clicked, this, [this, row](){
                    QMessageBox(nullptr);
                    int project_id = this->ui->tableWidget->item(row, 0)->text().toInt();
                    QSqlQuery query;
                    QString sql = "SELECT status FROM top_project WHERE id = " + QString::number(project_id) + ";";
                    qDebug() << sql << __LINE__ << __FILE__;
                    if (!query.exec(sql))
                    {
                        qDebug() << "Query error:" << query.lastError().text() << __LINE__ << __FILE__;
                        return;
                    }
                    query.next();
                    if(query.value("status").toInt() == 1)
                    {
                        query.exec("UPDATE top_project SET status = 0 WHERE id = " + QString::number(project_id) + ";");
                    }
                    else
                    {
                        query.exec("UPDATE top_project SET status = 1 WHERE id = " + QString::number(project_id) + ";");
                    }

                    this->load_data(nullptr);
                });
                this->ui->tableWidget->setCellWidget(row, col, button);
            }
            else if(col == this->ui->tableWidget->columnCount() - 1)
            {
                QPushButton* button = new QPushButton("删除");
                this->ui->tableWidget->setCellWidget(row, col, button);
                connect(button, &QPushButton::clicked, this, [this, row](){
                    int project_id = this->ui->tableWidget->item(row, 0)->text().toInt();
                    QSqlQuery query;
                    QString sql = "DELETE FROM top_project WHERE id = " + QString::number(project_id) + ";";
                    if (!query.exec(sql))
                    {
                        qDebug() << "Query error:" << query.lastError() << __LINE__ << __FILE__;
                        return;
                    }

                    this->load_data(nullptr);
                });
            }
            else
            {
                QTableWidgetItem *item;
                item = new QTableWidgetItem(query.value(col).toString());
                item->setTextAlignment(Qt::AlignHCenter | Qt::AlignVCenter);
                this->ui->tableWidget->setItem(row, col, item);
            } 
        }
        ++row;
    }

    this->ui->tableWidget->blockSignals(false);
}

void Project_list::on_tableWidget_clicked(const QModelIndex &index)
{
    this->data = this->ui->tableWidget->item(index.row(), 0)->text();
}

void Project_list::on_tableWidget_doubleClicked(const QModelIndex &index)
{

}

void Project_list::on_tableWidget_itemChanged(QTableWidgetItem *item)
{

    QString new_data = item->text();
    QString data_id = this->ui->tableWidget->item(item->row(), 0)->text();
    QString data_id2 = this->ui->tableWidget->item(0, item->column())->text();

    //QString sql = "UPDATE users SET email = '<EMAIL>' WHERE user_id = 1001";
    qDebug() << data_id2 << __LINE__;
}
