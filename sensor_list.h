#ifndef SENSOR_LIST_H
#define SENSOR_LIST_H

#include <QWidget>
#include <QTableWidgetItem>
#include "message.h"


namespace Ui {
class sensor_list;
}

class sensor_list : public QWidget
{
private:
    Q_OBJECT

public:
    explicit sensor_list(QWidget *parent = nullptr);
    ~sensor_list();

    void load_data_sensor(QString IP);

    void load_data_project();

signals:
    void save_project_and_sensor(QString new_IP, QString old_IP);

private slots:
    void on_pushButton_clicked();

    void on_pushButton_reload_clicked();

    void on_comboBox_device_choose_currentTextChanged(const QString &arg1);

    void on_comboBox_project_currentTextChanged(const QString &arg1);

    void on_pushButton_save_clicked();

    void on_tableWidget_device_IP_clicked(const QModelIndex &index);

    void on_tableWidget_device_IP_currentItemChanged(QTableWidgetItem *current, QTableWidgetItem *previous);

    void on_tableWidget_device_IP_itemChanged(QTableWidgetItem *item);

    void on_tableWidget_device_IP_doubleClicked(const QModelIndex &index);

private:
    Ui::sensor_list *ui;


};

#endif // SENSOR_LIST_H
