#ifndef MONITOR_H
#define MONITOR_H

#include <QWidget>
#include <QVBoxLayout>

#include "message.h"
#include "ad_thread.h"
#include "qcustomplot.h"

namespace Ui {
class monitor;
}

class monitor : public QWidget
{
    Q_OBJECT

public:
    explicit monitor(QWidget *parent = nullptr);
    ~monitor();

    void overview_init();

    void focus_init();


private slots:
    void on_pushButton_start_clicked();

    void on_pushButton_model_change_clicked();

    void on_comboBox_device_currentTextChanged(const QString &arg1);

private:
    Ui::monitor *ui;

    //当前获取来自哪个线程（板卡）的数据
    QVector<double>* data_source;

    QTimer *timer_update_overview;
    QTimer *timer_update_focus;

    QVBoxLayout* verticalLayout_channel_chose;
    QVBoxLayout* verticalLayout_focus;

    QCustomPlot *customPlot[16];

    void update_plot();

    QCPGraph* line[16];
};

#endif // MONITOR_H
