#include "user_create.h"
#include "ui_user_create.h"
#include "user_list.h"
#include "message.h"

#include <QSqlQuery>
#include <QSqlError>
#include <QDebug>
#include <QTableWidget>
#include <QMessageBox>
#include <QTime>

user_create::user_create(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::user_create)
{
    ui->setupUi(this);

    QString styleSheet = R"(
    /* 主窗口整体样式 */
    QMainWindow {
        background-color: #f5f7fa; /* 浅灰蓝色背景，营造清爽氛围 */
        font-size: 15px
    }

    /* 中心部件，作为基础容器 */
    QWidget#centralwidget {
        background-color: transparent; /* 透明，让 stackedWidget 等自己控制背景 */
    }

    /* 栈式窗口，用于页面切换的容器 */
    QStackedWidget {
        background-color: white;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 10px;
        margin: 8px; /* 与主窗口边缘保持间距 */
    }

    /* 栈式窗口里的页面，可给不同页面差异化设置，这里统一先简单处理 */
    QWidget#page, QWidget#page_2 {
        background-color: white;
        border-radius: 6px;
        padding: 12px;
    }

    /* 若页面里有其他控件（如按钮、标签等，可提前预设通用样式，后续叠加） */
    QLabel {
        color: #333333;
        font-size: 13px;
    }
    QPushButton {
        background-color: #409eff; /* 经典主题蓝色 */
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 16px;
        margin: 4px;
    }
    QPushButton:hover {
        background-color: #66b1ff; /* 悬停变浅蓝 */
    }
    QPushButton:pressed {
        background-color: #337ab7; /* 按下加深 */
    }

    )";
    this->setObjectName("user_create");
    this->setStyleSheet(styleSheet);
}

user_create::~user_create()
{
    delete ui;
}

void user_create::on_pushButton_save_clicked()
{
    QSqlQuery query;
    QString sql = "SELECT username FROM top_user WHERE username = '" + this->ui->lineEdit_username->text() + "';";
    if (!query.exec(sql))
    {
        qDebug() << "Query error:" << query.lastError().text() << __LINE__;
        return;
    }
    if(!query.next() && this->ui->lineEdit_password->text() == this->ui->lineEdit_double_password->text())
    {
        qDebug() << "INSERT INTO top_user (username, password, realname, status, create_time) "
                    "VALUES (:name, :password, :realname, :status, :create_time);" << __LINE__;

       query.prepare("INSERT INTO top_user (username, password, realname, status, create_time) "
                  "VALUES (:username, :password, :realname, :status, :create_time);");

       query.bindValue(":username", this->ui->lineEdit_username->text());
       query.bindValue(":password", this->ui->lineEdit_password->text());
       query.bindValue(":realname", this->ui->lineEdit_group->text());
       query.bindValue(":status", 1);
       query.bindValue(":create_time", QTime::currentTime().toString("hh,mm,ss"));

       if (!query.exec())
       {
           qDebug() << "Query error:" << query.lastError().text() << __LINE__;
           return;
       }

       this->ui->lineEdit_username->setText(nullptr) ;
       this->ui->lineEdit_password->setText(nullptr) ;
       this->ui->lineEdit_double_password->setText(nullptr) ;
       this->ui->lineEdit_group->setText(nullptr);

       QMessageBox::information(this, "提示:", "加入成功");
       emit reload(nullptr);
    }
    else if(this->ui->lineEdit_password->text() != this->ui->lineEdit_double_password->text())
    {
         QMessageBox::information(this, "加入失败:", "两次输入密码不一致");
    }
    else
    {
        QMessageBox::information(this, "加入失败:", "用户名重复");
    }
}

void user_create::on_pushButton_cancel_clicked()
{
    this->close();
}
