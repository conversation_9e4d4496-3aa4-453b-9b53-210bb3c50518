<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>user_list</class>
 <widget class="QWidget" name="user_list">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>900</width>
    <height>500</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>用户列表</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_4">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="widget_main" native="true">
     <property name="styleSheet">
      <string notr="true">background:#fff;</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <property name="spacing">
       <number>10</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QWidget" name="widget_button_box" native="true">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>30</height>
         </size>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <property name="spacing">
          <number>10</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QPushButton" name="pushButton_add">
           <property name="minimumSize">
            <size>
             <width>80</width>
             <height>24</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{
	background:#1890FF;
	border:none;
	color:#fafafa;
	border-radius:2px;
}
QPushButton:hover{
	background:#1281E8;
}</string>
           </property>
           <property name="text">
            <string>新增用户</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_edit">
           <property name="minimumSize">
            <size>
             <width>80</width>
             <height>24</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{
	background:#ccc;
	border:none;
	color:#000;
	border-radius:2px;
}
QPushButton:hover{
	background:#bbb;
}</string>
           </property>
           <property name="text">
            <string>编辑用户</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_delete">
           <property name="minimumSize">
            <size>
             <width>80</width>
             <height>24</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{
	background:#ccc;
	border:none;
	color:#000;
	border-radius:2px;
}
QPushButton:hover{
	background:#bbb;
}</string>
           </property>
           <property name="text">
            <string>删除用户</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>94</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEdit_username">
           <property name="minimumSize">
            <size>
             <width>240</width>
             <height>26</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>240</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">background:none;
border:1px solid #bbb;
border-radius:2px;
color:#333;</string>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="placeholderText">
            <string>请输入账号</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_search">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>24</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{
	background:#1890FF;
	border:none;
	color:#fafafa;
	border-radius:2px;
}
QPushButton:hover{
	background:#1281E8;
}</string>
           </property>
           <property name="text">
            <string>查询</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="widget_table" native="true">
        <layout class="QHBoxLayout" name="horizontalLayout">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QTableWidget" name="tableWidget">
           <property name="acceptDrops">
            <bool>false</bool>
           </property>
           <property name="layoutDirection">
            <enum>Qt::LeftToRight</enum>
           </property>
           <property name="autoFillBackground">
            <bool>true</bool>
           </property>
           <property name="styleSheet">
            <string notr="true">QTableWidget {
	border:1px solid #ddd;
}
QHeaderView {
    background:transparent;
}
QHeaderView::section{
	height:26px;
    color:#111;
    background:#eee;
	border: none;
	border-right:1px solid #ddd;
	border-bottom:1px solid #ddd;
}
QTableWidget::item {
	border:none;
	border-bottom:1px solid #EEF1F7;
}
QTableWidget::item::selected {
	color:#fff;     
	background:#1890FF;
}</string>
           </property>
           <property name="textElideMode">
            <enum>Qt::ElideLeft</enum>
           </property>
           <property name="verticalScrollMode">
            <enum>QAbstractItemView::ScrollPerItem</enum>
           </property>
           <attribute name="horizontalHeaderCascadingSectionResizes">
            <bool>false</bool>
           </attribute>
           <attribute name="horizontalHeaderDefaultSectionSize">
            <number>150</number>
           </attribute>
           <attribute name="horizontalHeaderMinimumSectionSize">
            <number>0</number>
           </attribute>
           <attribute name="horizontalHeaderShowSortIndicator" stdset="0">
            <bool>false</bool>
           </attribute>
           <attribute name="horizontalHeaderStretchLastSection">
            <bool>true</bool>
           </attribute>
           <attribute name="verticalHeaderDefaultSectionSize">
            <number>30</number>
           </attribute>
           <attribute name="verticalHeaderMinimumSectionSize">
            <number>25</number>
           </attribute>
           <attribute name="verticalHeaderStretchLastSection">
            <bool>false</bool>
           </attribute>
           <column>
            <property name="text">
             <string>账号</string>
            </property>
            <property name="font">
             <font>
              <family>Microsoft YaHei UI</family>
             </font>
            </property>
           </column>
           <column>
            <property name="text">
             <string>姓名</string>
            </property>
            <property name="font">
             <font>
              <family>Microsoft YaHei UI</family>
             </font>
            </property>
           </column>
           <column>
            <property name="text">
             <string>用户组</string>
            </property>
            <property name="font">
             <font>
              <family>Microsoft YaHei UI</family>
             </font>
            </property>
           </column>
           <column>
            <property name="text">
             <string>用户状态</string>
            </property>
            <property name="font">
             <font>
              <family>Microsoft YaHei UI</family>
             </font>
            </property>
           </column>
           <column>
            <property name="text">
             <string>创建时间</string>
            </property>
            <property name="font">
             <font>
              <family>Microsoft YaHei UI</family>
             </font>
            </property>
           </column>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
