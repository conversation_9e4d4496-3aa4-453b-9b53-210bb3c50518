#ifndef FFT_PROCESSER_H
#define FFT_PROCESSER_H

#include <QVector>
#include <complex>
#include "kiss_fftr.h"

class fft_processer
{
public:
    explicit fft_processer(int fftSize = 1024, double sampleRate = 44100.0);
    ~fft_processer();

    // 执行FFT变换
    QVector<std::complex<double>> performFFT(const QVector<double>& input);

    // 执行IFFT变换
    QVector<double> performIFFT(const QVector<std::complex<double>>& spectrum);

    // 计算幅度谱
    static QVector<double> calculateMagnitude(const QVector<std::complex<double>>& spectrum);

    // 计算相位谱
    static QVector<double> calculatePhase(const QVector<std::complex<double>>& spectrum);

    // 获取频率轴数据
    QVector<double> getFrequencyAxis() const;

    // 获取FFT大小和采样率
    int getFFTSize() const;
    double getSampleRate() const;

private:
    int m_fftSize;
    double m_sampleRate;
    kiss_fftr_cfg m_fftConfig;  // 正向FFT配置
    kiss_fftr_cfg m_ifftConfig; // 逆向FFT配置
};

#endif // FFT_PROCESSER_H
