#include "mainwindow.h"
#include "ui_mainwindow.h"
#include "message.h"

//extern database* db;

MainWindow::MainWindow(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::MainWindow)
{
    //db->connect_database();

    ui->setupUi(this);
    this->windowProject= new Project_list();
    this->windowUser=new User_main();
    this->windowRecord=new Record();
    this->windowMonitor=new Monitor();
    this->windowSetting= new Setting();
    this->windowMore=new More();

    this->ui->stackedWidget->insertWidget(0,this->windowProject);
    this->ui->stackedWidget->insertWidget(1,this->windowMonitor);
    this->ui->stackedWidget->insertWidget(2,this->windowRecord);
    this->ui->stackedWidget->insertWidget(3,this->windowUser);
    this->ui->stackedWidget->insertWidget(4,this->windowMore);
    this->ui->stackedWidget->insertWidget(5,this->windowSetting);
    //this->ui->stackedWidget->setCurrentIndex(0);
    on_toolButton_project_clicked();
}

MainWindow::~MainWindow()
{
    delete ui;
}

void MainWindow::on_toolButton_project_clicked()
{    

//    this->ui->toolButton_project->setStyleSheet("QToolButton{background:#1890FF;	border:none;color:#e6e6e6;}QToolButton:hover{	background:#1890FF;}");
//    this->ui->toolButton_monitor->setStyleSheet("QToolButton{background:none; border:none;color:#e6e6e6;}QToolButton:hover{	background:#1890FF;}");
//    this->ui->toolButton_record->setStyleSheet("QToolButton{background:none; border:none;color:#e6e6e6;}QToolButton:hover{	background:#1890FF;}");
//    this->ui->toolButton_more->setStyleSheet("QToolButton{background:none; border:none;color:#e6e6e6;}QToolButton:hover{	background:#1890FF;}");
//    this->ui->toolButton_user->setStyleSheet("QToolButton{background:none; border:none;color:#e6e6e6;}QToolButton:hover{	background:#1890FF;}");
//    this->ui->toolButton_setting->setStyleSheet("QToolButton{background:none; border:none;color:#e6e6e6;}QToolButton:hover{	background:#1890FF;}");
 this->ui->stackedWidget->setCurrentIndex(0);
}

void MainWindow::on_toolButton_monitor_clicked()
{

//    this->ui->toolButton_monitor->setStyleSheet("QToolButton{background:#1890FF;	border:none;color:#e6e6e6;}QToolButton:hover{	background:#1890FF;}");
//    this->ui->toolButton_project->setStyleSheet("background:none; border:none;color:#e6e6e6;");
//    this->ui->toolButton_record->setStyleSheet("background:none; border:none;color:#e6e6e6;");
//    this->ui->toolButton_more->setStyleSheet("background:none; border:none;color:#e6e6e6;");
//    this->ui->toolButton_user->setStyleSheet("background:none; border:none;color:#e6e6e6;");
    this->ui->toolButton_setting->setStyleSheet("background:none;border:none;color:#e6e6e6;");
   this->ui->stackedWidget->setCurrentIndex(1);
}

void MainWindow::on_toolButton_record_clicked()
{
    this->ui->stackedWidget->setCurrentIndex(2);
//    this->ui->toolButton_record->setStyleSheet("QToolButton{background:#1890FF;	border:none;color:#e6e6e6;}QToolButton:hover{	background:#1890FF;}");
//    this->ui->toolButton_project->setStyleSheet("background:none; border:none;color:#e6e6e6;");
//    this->ui->toolButton_monitor->setStyleSheet("background:none; border:none;color:#e6e6e6;");
//    this->ui->toolButton_more->setStyleSheet("background:none; border:none;color:#e6e6e6;");
//    this->ui->toolButton_user->setStyleSheet("background:none; border:none;color:#e6e6e6;");
//    this->ui->toolButton_setting->setStyleSheet("background:none;border:none;color:#e6e6e6;");
}

void MainWindow::on_toolButton_user_clicked()
{
    this->ui->stackedWidget->setCurrentIndex(3);

//    this->ui->toolButton_user->setStyleSheet("QToolButton{background:#1890FF;	border:none;color:#e6e6e6;}QToolButton:hover{	background:#1890FF;}");
//    this->ui->toolButton_project->setStyleSheet("background:none; border:none;color:#e6e6e6;");
//    this->ui->toolButton_monitor->setStyleSheet("background:none; border:none;color:#e6e6e6;");
//    this->ui->toolButton_more->setStyleSheet("background:none; border:none;color:#e6e6e6;");
//    this->ui->toolButton_record->setStyleSheet("background:none; border:none;color:#e6e6e6;");
//    this->ui->toolButton_setting->setStyleSheet("background:none;border:none;color:#e6e6e6;");
}

void MainWindow::on_toolButton_more_clicked()
{
     this->ui->stackedWidget->setCurrentIndex(4);
//    this->ui->toolButton_more->setStyleSheet("QToolButton{background:#1890FF;	border:none;color:#e6e6e6;}QToolButton:hover{	background:#1890FF;}");
//    this->ui->toolButton_project->setStyleSheet("background:none; border:none;color:#e6e6e6;");
//    this->ui->toolButton_monitor->setStyleSheet("background:none; border:none;color:#e6e6e6;");
//    this->ui->toolButton_record->setStyleSheet("background:none; border:none;color:#e6e6e6;");
//    this->ui->toolButton_user->setStyleSheet("background:none; border:none;color:#e6e6e6;");
//    this->ui->toolButton_setting->setStyleSheet("background:none; border:none;color:#e6e6e6;");
}

void MainWindow::on_toolButton_setting_clicked()
{
    this->ui->stackedWidget->setCurrentIndex(5);
//    this->ui->toolButton_setting->setStyleSheet("QToolButton{background:#1890FF;	border:none;color:#e6e6e6;}QToolButton:hover{	background:#1890FF;}");
//    this->ui->toolButton_project->setStyleSheet("background:none; border:none;color:#e6e6e6;");
//    this->ui->toolButton_monitor->setStyleSheet("background:none; border:none;color:#e6e6e6;");
//    this->ui->toolButton_more->setStyleSheet("background:none; border:none;color:#e6e6e6;");
//    this->ui->toolButton_user->setStyleSheet("background:none; border:none;color:#e6e6e6;");
//    this->ui->toolButton_record->setStyleSheet("background:none; border:none;color:#e6e6e6;");
}
