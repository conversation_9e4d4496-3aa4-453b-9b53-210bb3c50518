#ifndef NLENOSPEED_H
#define NLENOSPEED_H

#include <iostream>
#include <vector>
#include <cmath>
#include <utility>

using namespace std;

struct FuncResult
{
    vector<double> x;
    vector<double> y;
    double z;
    FuncResult(const vector<double>& x_, const vector<double>& y_, double z_)
        : x(x_), y(y_), z(z_) {}
};

class NLENoSpeed
{
private:
    vector<double> xZuoBiao;
    vector<double> yZuoBiao;
    vector<double> zZuoBiao;
    vector<double> timeArray;

public:
    NLENoSpeed(const vector<double>& x, const vector<double>& y,
               const vector<double>& z, const vector<double>& t)
        : xZuoBiao(x), yZuoBiao(y), zZuoBiao(z), timeArray(t) {}

    pair<bool, vector<double>> GetRootsetGrad(int n, vector<double> x, int nMaxIt, double eps);

private:
    FuncResult func(const vector<double>& x, const vector<double>& y);

};


#endif // NLENOSPEED_H
