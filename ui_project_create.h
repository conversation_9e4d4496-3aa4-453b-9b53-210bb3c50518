/********************************************************************************
** Form generated from reading UI file 'project_create.ui'
**
** Created by: Qt User Interface Compiler version 5.9.8
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_PROJECT_CREATE_H
#define UI_PROJECT_CREATE_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_project_create
{
public:
    QVBoxLayout *verticalLayout_3;
    QWidget *widget_main;
    QVBoxLayout *verticalLayout;
    QGroupBox *groupBox_project;
    QVBoxLayout *verticalLayout_2;
    QWidget *widget;
    QHBoxLayout *horizontalLayout_3;
    QLabel *label;
    QLabel *label_name;
    QLineEdit *lineEdit_name;
    QWidget *widget_5;
    QHBoxLayout *horizontalLayout_6;
    QLabel *label_details;
    QTextEdit *textEdit_description;
    QWidget *widget_4;
    QHBoxLayout *horizontalLayout_4;
    QLabel *label_2;
    QLabel *label_path;
    QLineEdit *lineEdit_path;
    QPushButton *pushButton_path;
    QSpacerItem *horizontalSpacer_7;
    QLabel *label_speed;
    QLineEdit *lineEdit_speed;
    QSpacerItem *horizontalSpacer_6;
    QLabel *label_supervisor;
    QLabel *label_speed_unit;
    QLineEdit *lineEdit_company;
    QSpacerItem *horizontalSpacer_9;
    QLabel *label_operator;
    QLineEdit *lineEdit_operator;
    QGroupBox *groupBox_sensor;
    QVBoxLayout *verticalLayout_4;
    QWidget *widget_6;
    QHBoxLayout *horizontalLayout_7;
    QLabel *label_4;
    QLineEdit *lineEdit_divice_number;
    QSpacerItem *horizontalSpacer_4;
    QLabel *label_3;
    QComboBox *comboBox_device_choose;
    QSpacerItem *horizontalSpacer_5;
    QLabel *label_5;
    QLineEdit *lineEdit_device_IP;
    QWidget *widget_7;
    QHBoxLayout *horizontalLayout;
    QTableWidget *tableWidget_sensor_parameter;
    QWidget *widget_button_box;
    QHBoxLayout *horizontalLayout_2;
    QSpacerItem *horizontalSpacer_2;
    QPushButton *pushButton_save;
    QSpacerItem *horizontalSpacer;
    QPushButton *pushButton_cancel;
    QSpacerItem *horizontalSpacer_3;

    void setupUi(QWidget *project_create)
    {
        if (project_create->objectName().isEmpty())
            project_create->setObjectName(QStringLiteral("project_create"));
        project_create->resize(1046, 576);
        verticalLayout_3 = new QVBoxLayout(project_create);
        verticalLayout_3->setObjectName(QStringLiteral("verticalLayout_3"));
        widget_main = new QWidget(project_create);
        widget_main->setObjectName(QStringLiteral("widget_main"));
        widget_main->setBaseSize(QSize(968, 571));
        widget_main->setStyleSheet(QStringLiteral("background:#fff;"));
        verticalLayout = new QVBoxLayout(widget_main);
        verticalLayout->setObjectName(QStringLiteral("verticalLayout"));
        groupBox_project = new QGroupBox(widget_main);
        groupBox_project->setObjectName(QStringLiteral("groupBox_project"));
        groupBox_project->setMinimumSize(QSize(0, 200));
        groupBox_project->setMaximumSize(QSize(16777215, 200));
        QFont font;
        font.setFamily(QStringLiteral("Microsoft YaHei UI"));
        groupBox_project->setFont(font);
        verticalLayout_2 = new QVBoxLayout(groupBox_project);
        verticalLayout_2->setSpacing(0);
        verticalLayout_2->setObjectName(QStringLiteral("verticalLayout_2"));
        verticalLayout_2->setContentsMargins(6, 6, 6, 6);
        widget = new QWidget(groupBox_project);
        widget->setObjectName(QStringLiteral("widget"));
        widget->setMaximumSize(QSize(16777215, 40));
        horizontalLayout_3 = new QHBoxLayout(widget);
        horizontalLayout_3->setSpacing(0);
        horizontalLayout_3->setObjectName(QStringLiteral("horizontalLayout_3"));
        horizontalLayout_3->setContentsMargins(0, 0, 0, 0);
        label = new QLabel(widget);
        label->setObjectName(QStringLiteral("label"));
        label->setStyleSheet(QStringLiteral("color:red;"));

        horizontalLayout_3->addWidget(label);

        label_name = new QLabel(widget);
        label_name->setObjectName(QStringLiteral("label_name"));
        label_name->setMinimumSize(QSize(64, 0));
        QFont font1;
        font1.setFamily(QStringLiteral("Microsoft YaHei UI"));
        font1.setPointSize(9);
        label_name->setFont(font1);
        label_name->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_3->addWidget(label_name);

        lineEdit_name = new QLineEdit(widget);
        lineEdit_name->setObjectName(QStringLiteral("lineEdit_name"));
        lineEdit_name->setMinimumSize(QSize(0, 24));
        lineEdit_name->setFont(font);
        lineEdit_name->setStyleSheet(QLatin1String("background:none;\n"
"border:1px solid #bbb;\n"
"border-radius:3px;\n"
"color:#333;"));

        horizontalLayout_3->addWidget(lineEdit_name);


        verticalLayout_2->addWidget(widget);

        widget_5 = new QWidget(groupBox_project);
        widget_5->setObjectName(QStringLiteral("widget_5"));
        widget_5->setMaximumSize(QSize(1000000, 40));
        horizontalLayout_6 = new QHBoxLayout(widget_5);
        horizontalLayout_6->setSpacing(0);
        horizontalLayout_6->setObjectName(QStringLiteral("horizontalLayout_6"));
        horizontalLayout_6->setContentsMargins(0, 0, 0, 0);
        label_details = new QLabel(widget_5);
        label_details->setObjectName(QStringLiteral("label_details"));
        label_details->setMinimumSize(QSize(70, 0));
        label_details->setMaximumSize(QSize(16777215, 100));
        label_details->setFont(font1);
        label_details->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_6->addWidget(label_details);

        textEdit_description = new QTextEdit(widget_5);
        textEdit_description->setObjectName(QStringLiteral("textEdit_description"));
        textEdit_description->setMaximumSize(QSize(16777215, 40));
        textEdit_description->setFont(font);
        textEdit_description->setStyleSheet(QLatin1String("background:none;\n"
"border:1px solid #bbb;\n"
"border-radius:3px;\n"
"color:#333;"));

        horizontalLayout_6->addWidget(textEdit_description);


        verticalLayout_2->addWidget(widget_5);

        widget_4 = new QWidget(groupBox_project);
        widget_4->setObjectName(QStringLiteral("widget_4"));
        widget_4->setMinimumSize(QSize(0, 0));
        widget_4->setMaximumSize(QSize(16777215, 50));
        horizontalLayout_4 = new QHBoxLayout(widget_4);
        horizontalLayout_4->setObjectName(QStringLiteral("horizontalLayout_4"));
        label_2 = new QLabel(widget_4);
        label_2->setObjectName(QStringLiteral("label_2"));
        label_2->setStyleSheet(QStringLiteral("color:red;"));

        horizontalLayout_4->addWidget(label_2);

        label_path = new QLabel(widget_4);
        label_path->setObjectName(QStringLiteral("label_path"));
        label_path->setMinimumSize(QSize(64, 0));
        label_path->setFont(font1);
        label_path->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_4->addWidget(label_path);

        lineEdit_path = new QLineEdit(widget_4);
        lineEdit_path->setObjectName(QStringLiteral("lineEdit_path"));
        lineEdit_path->setMinimumSize(QSize(0, 24));
        lineEdit_path->setMaximumSize(QSize(200, 16777215));
        lineEdit_path->setFont(font);
        lineEdit_path->setLayoutDirection(Qt::LeftToRight);
        lineEdit_path->setStyleSheet(QLatin1String("background:none;\n"
"border:1px solid #bbb;\n"
"border-radius:3px;\n"
"color:#333;"));

        horizontalLayout_4->addWidget(lineEdit_path);

        pushButton_path = new QPushButton(widget_4);
        pushButton_path->setObjectName(QStringLiteral("pushButton_path"));
        pushButton_path->setMinimumSize(QSize(60, 24));
        pushButton_path->setMaximumSize(QSize(60, 200));
        pushButton_path->setFont(font1);
        pushButton_path->setLayoutDirection(Qt::LeftToRight);
        pushButton_path->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#1890FF;\n"
"	border:none;\n"
"	color:#fafafa;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#1281E8;\n"
"}"));
        pushButton_path->setIconSize(QSize(12, 12));

        horizontalLayout_4->addWidget(pushButton_path);

        horizontalSpacer_7 = new QSpacerItem(10, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer_7);

        label_speed = new QLabel(widget_4);
        label_speed->setObjectName(QStringLiteral("label_speed"));
        label_speed->setMinimumSize(QSize(75, 0));
        label_speed->setFont(font1);
        label_speed->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_4->addWidget(label_speed);

        lineEdit_speed = new QLineEdit(widget_4);
        lineEdit_speed->setObjectName(QStringLiteral("lineEdit_speed"));
        lineEdit_speed->setMinimumSize(QSize(0, 24));
        lineEdit_speed->setMaximumSize(QSize(16777215, 16777215));
        lineEdit_speed->setFont(font);
        lineEdit_speed->setStyleSheet(QLatin1String("background:none;\n"
"border:1px solid #bbb;\n"
"border-radius:3px;\n"
"color:#333;"));

        horizontalLayout_4->addWidget(lineEdit_speed);

        horizontalSpacer_6 = new QSpacerItem(60, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer_6);

        label_supervisor = new QLabel(widget_4);
        label_supervisor->setObjectName(QStringLiteral("label_supervisor"));
        label_supervisor->setMinimumSize(QSize(70, 0));
        label_supervisor->setMaximumSize(QSize(16777215, 16777215));
        label_supervisor->setFont(font1);
        label_supervisor->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_4->addWidget(label_supervisor);

        label_speed_unit = new QLabel(widget_4);
        label_speed_unit->setObjectName(QStringLiteral("label_speed_unit"));
        label_speed_unit->setFont(font1);
        label_speed_unit->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_4->addWidget(label_speed_unit);

        lineEdit_company = new QLineEdit(widget_4);
        lineEdit_company->setObjectName(QStringLiteral("lineEdit_company"));
        lineEdit_company->setMinimumSize(QSize(0, 24));
        lineEdit_company->setMaximumSize(QSize(16777215, 16777215));
        lineEdit_company->setFont(font);
        lineEdit_company->setStyleSheet(QLatin1String("background:none;\n"
"border:1px solid #bbb;\n"
"border-radius:3px;\n"
"color:#333;"));

        horizontalLayout_4->addWidget(lineEdit_company);

        horizontalSpacer_9 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer_9);

        label_operator = new QLabel(widget_4);
        label_operator->setObjectName(QStringLiteral("label_operator"));
        label_operator->setMinimumSize(QSize(75, 0));
        label_operator->setFont(font1);
        label_operator->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_4->addWidget(label_operator);

        lineEdit_operator = new QLineEdit(widget_4);
        lineEdit_operator->setObjectName(QStringLiteral("lineEdit_operator"));
        lineEdit_operator->setEnabled(false);
        lineEdit_operator->setMinimumSize(QSize(200, 24));
        lineEdit_operator->setMaximumSize(QSize(176, 16777215));
        lineEdit_operator->setFont(font);
        lineEdit_operator->setStyleSheet(QLatin1String("background:none;\n"
"border:1px solid #bbb;\n"
"border-radius:3px;\n"
"color:#333;"));

        horizontalLayout_4->addWidget(lineEdit_operator);


        verticalLayout_2->addWidget(widget_4);


        verticalLayout->addWidget(groupBox_project);

        groupBox_sensor = new QGroupBox(widget_main);
        groupBox_sensor->setObjectName(QStringLiteral("groupBox_sensor"));
        groupBox_sensor->setMaximumSize(QSize(16777215, 16777215));
        groupBox_sensor->setFont(font);
        verticalLayout_4 = new QVBoxLayout(groupBox_sensor);
        verticalLayout_4->setObjectName(QStringLiteral("verticalLayout_4"));
        widget_6 = new QWidget(groupBox_sensor);
        widget_6->setObjectName(QStringLiteral("widget_6"));
        horizontalLayout_7 = new QHBoxLayout(widget_6);
        horizontalLayout_7->setObjectName(QStringLiteral("horizontalLayout_7"));
        label_4 = new QLabel(widget_6);
        label_4->setObjectName(QStringLiteral("label_4"));
        label_4->setFont(font);

        horizontalLayout_7->addWidget(label_4);

        lineEdit_divice_number = new QLineEdit(widget_6);
        lineEdit_divice_number->setObjectName(QStringLiteral("lineEdit_divice_number"));
        lineEdit_divice_number->setFont(font1);

        horizontalLayout_7->addWidget(lineEdit_divice_number);

        horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_7->addItem(horizontalSpacer_4);

        label_3 = new QLabel(widget_6);
        label_3->setObjectName(QStringLiteral("label_3"));
        label_3->setFont(font);

        horizontalLayout_7->addWidget(label_3);

        comboBox_device_choose = new QComboBox(widget_6);
        comboBox_device_choose->setObjectName(QStringLiteral("comboBox_device_choose"));
        comboBox_device_choose->setFont(font);

        horizontalLayout_7->addWidget(comboBox_device_choose);

        horizontalSpacer_5 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_7->addItem(horizontalSpacer_5);

        label_5 = new QLabel(widget_6);
        label_5->setObjectName(QStringLiteral("label_5"));
        QFont font2;
        font2.setFamily(QStringLiteral("Microsoft YaHei"));
        label_5->setFont(font2);

        horizontalLayout_7->addWidget(label_5);

        lineEdit_device_IP = new QLineEdit(widget_6);
        lineEdit_device_IP->setObjectName(QStringLiteral("lineEdit_device_IP"));
        lineEdit_device_IP->setFont(font);

        horizontalLayout_7->addWidget(lineEdit_device_IP);


        verticalLayout_4->addWidget(widget_6);

        widget_7 = new QWidget(groupBox_sensor);
        widget_7->setObjectName(QStringLiteral("widget_7"));
        widget_7->setMaximumSize(QSize(16777215, 16777215));
        horizontalLayout = new QHBoxLayout(widget_7);
        horizontalLayout->setObjectName(QStringLiteral("horizontalLayout"));
        tableWidget_sensor_parameter = new QTableWidget(widget_7);
        if (tableWidget_sensor_parameter->columnCount() < 13)
            tableWidget_sensor_parameter->setColumnCount(13);
        QFont font3;
        font3.setPointSize(9);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        __qtablewidgetitem->setFont(font3);
        tableWidget_sensor_parameter->setHorizontalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        tableWidget_sensor_parameter->setHorizontalHeaderItem(1, __qtablewidgetitem1);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        __qtablewidgetitem2->setFont(font3);
        tableWidget_sensor_parameter->setHorizontalHeaderItem(2, __qtablewidgetitem2);
        QTableWidgetItem *__qtablewidgetitem3 = new QTableWidgetItem();
        __qtablewidgetitem3->setFont(font3);
        tableWidget_sensor_parameter->setHorizontalHeaderItem(3, __qtablewidgetitem3);
        QTableWidgetItem *__qtablewidgetitem4 = new QTableWidgetItem();
        __qtablewidgetitem4->setFont(font3);
        tableWidget_sensor_parameter->setHorizontalHeaderItem(4, __qtablewidgetitem4);
        QTableWidgetItem *__qtablewidgetitem5 = new QTableWidgetItem();
        __qtablewidgetitem5->setFont(font3);
        tableWidget_sensor_parameter->setHorizontalHeaderItem(5, __qtablewidgetitem5);
        QTableWidgetItem *__qtablewidgetitem6 = new QTableWidgetItem();
        __qtablewidgetitem6->setFont(font3);
        tableWidget_sensor_parameter->setHorizontalHeaderItem(6, __qtablewidgetitem6);
        QTableWidgetItem *__qtablewidgetitem7 = new QTableWidgetItem();
        __qtablewidgetitem7->setFont(font3);
        tableWidget_sensor_parameter->setHorizontalHeaderItem(7, __qtablewidgetitem7);
        QTableWidgetItem *__qtablewidgetitem8 = new QTableWidgetItem();
        __qtablewidgetitem8->setFont(font3);
        tableWidget_sensor_parameter->setHorizontalHeaderItem(8, __qtablewidgetitem8);
        QTableWidgetItem *__qtablewidgetitem9 = new QTableWidgetItem();
        __qtablewidgetitem9->setFont(font3);
        tableWidget_sensor_parameter->setHorizontalHeaderItem(9, __qtablewidgetitem9);
        QTableWidgetItem *__qtablewidgetitem10 = new QTableWidgetItem();
        __qtablewidgetitem10->setFont(font3);
        tableWidget_sensor_parameter->setHorizontalHeaderItem(10, __qtablewidgetitem10);
        QTableWidgetItem *__qtablewidgetitem11 = new QTableWidgetItem();
        __qtablewidgetitem11->setFont(font3);
        tableWidget_sensor_parameter->setHorizontalHeaderItem(11, __qtablewidgetitem11);
        QTableWidgetItem *__qtablewidgetitem12 = new QTableWidgetItem();
        __qtablewidgetitem12->setFont(font3);
        tableWidget_sensor_parameter->setHorizontalHeaderItem(12, __qtablewidgetitem12);
        tableWidget_sensor_parameter->setObjectName(QStringLiteral("tableWidget_sensor_parameter"));

        horizontalLayout->addWidget(tableWidget_sensor_parameter);


        verticalLayout_4->addWidget(widget_7);


        verticalLayout->addWidget(groupBox_sensor);

        widget_button_box = new QWidget(widget_main);
        widget_button_box->setObjectName(QStringLiteral("widget_button_box"));
        widget_button_box->setMinimumSize(QSize(0, 40));
        widget_button_box->setMaximumSize(QSize(16777215, 50));
        horizontalLayout_2 = new QHBoxLayout(widget_button_box);
        horizontalLayout_2->setSpacing(0);
        horizontalLayout_2->setObjectName(QStringLiteral("horizontalLayout_2"));
        horizontalLayout_2->setContentsMargins(0, 0, 0, 0);
        horizontalSpacer_2 = new QSpacerItem(127, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer_2);

        pushButton_save = new QPushButton(widget_button_box);
        pushButton_save->setObjectName(QStringLiteral("pushButton_save"));
        pushButton_save->setMinimumSize(QSize(80, 28));
        pushButton_save->setFont(font1);
        pushButton_save->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#1890FF;\n"
"	border:none;\n"
"	color:#fafafa;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#1281E8;\n"
"}"));

        horizontalLayout_2->addWidget(pushButton_save);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Minimum, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer);

        pushButton_cancel = new QPushButton(widget_button_box);
        pushButton_cancel->setObjectName(QStringLiteral("pushButton_cancel"));
        pushButton_cancel->setMinimumSize(QSize(80, 28));
        pushButton_cancel->setFont(font1);
        pushButton_cancel->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#ccc;\n"
"	border:none;\n"
"	color:#000;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#bbb;\n"
"}"));

        horizontalLayout_2->addWidget(pushButton_cancel);

        horizontalSpacer_3 = new QSpacerItem(127, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer_3);


        verticalLayout->addWidget(widget_button_box);


        verticalLayout_3->addWidget(widget_main);


        retranslateUi(project_create);

        QMetaObject::connectSlotsByName(project_create);
    } // setupUi

    void retranslateUi(QWidget *project_create)
    {
        project_create->setWindowTitle(QApplication::translate("project_create", "Form", Q_NULLPTR));
        groupBox_project->setTitle(QApplication::translate("project_create", "\351\241\271\347\233\256\344\277\241\346\201\257", Q_NULLPTR));
        label->setText(QApplication::translate("project_create", "*", Q_NULLPTR));
        label_name->setText(QApplication::translate("project_create", "\351\241\271\347\233\256\345\220\215\347\247\260\357\274\232", Q_NULLPTR));
        lineEdit_name->setPlaceholderText(QApplication::translate("project_create", "\350\257\267\350\276\223\345\205\245\351\241\271\347\233\256\345\220\215\347\247\260", Q_NULLPTR));
        label_details->setText(QApplication::translate("project_create", "\351\241\271\347\233\256\346\217\217\350\277\260\357\274\232", Q_NULLPTR));
        textEdit_description->setHtml(QApplication::translate("project_create", "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n"
"<html><head><meta name=\"qrichtext\" content=\"1\" /><style type=\"text/css\">\n"
"p, li { white-space: pre-wrap; }\n"
"</style></head><body style=\" font-family:'Microsoft YaHei UI'; font-size:9pt; font-weight:400; font-style:normal;\">\n"
"<p style=\"-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><br /></p></body></html>", Q_NULLPTR));
        label_2->setText(QApplication::translate("project_create", "*", Q_NULLPTR));
        label_path->setText(QApplication::translate("project_create", "\345\255\230\345\202\250\350\267\257\345\276\204\357\274\232", Q_NULLPTR));
        lineEdit_path->setPlaceholderText(QApplication::translate("project_create", "\350\257\267\351\200\211\346\213\251\345\255\230\345\202\250\350\267\257\345\276\204", Q_NULLPTR));
        pushButton_path->setText(QApplication::translate("project_create", "\351\200\211\346\213\251", Q_NULLPTR));
        label_speed->setText(QApplication::translate("project_create", "\351\200\237\345\272\246(\347\261\263/\347\247\222)\357\274\232", Q_NULLPTR));
        lineEdit_speed->setPlaceholderText(QApplication::translate("project_create", "\350\257\267\350\276\223\345\205\245\351\200\237\345\272\246", Q_NULLPTR));
        label_supervisor->setText(QApplication::translate("project_create", "\347\233\221\345\210\266\345\215\225\344\275\215\357\274\232", Q_NULLPTR));
        label_speed_unit->setText(QString());
        lineEdit_company->setPlaceholderText(QApplication::translate("project_create", "\350\257\267\350\276\223\345\205\245\347\233\221\345\210\266\345\215\225\344\275\215", Q_NULLPTR));
        label_operator->setText(QApplication::translate("project_create", "\346\223\215\344\275\234\344\272\272\345\221\230\357\274\232", Q_NULLPTR));
        lineEdit_operator->setPlaceholderText(QApplication::translate("project_create", "\350\257\267\350\276\223\345\205\245\346\223\215\344\275\234\344\272\272\345\221\230", Q_NULLPTR));
        groupBox_sensor->setTitle(QApplication::translate("project_create", "\346\243\200\346\263\242\345\231\250\345\217\202\346\225\260", Q_NULLPTR));
        label_4->setText(QApplication::translate("project_create", "\346\235\277\345\215\241\346\225\260\351\207\217\357\274\232", Q_NULLPTR));
        label_3->setText(QApplication::translate("project_create", " \346\235\277\345\215\241\351\200\211\346\213\251\357\274\232", Q_NULLPTR));
        label_5->setText(QApplication::translate("project_create", " \346\235\277\345\215\241IP\357\274\232", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem = tableWidget_sensor_parameter->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QApplication::translate("project_create", "\351\200\232\351\201\223\345\217\267", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem1 = tableWidget_sensor_parameter->horizontalHeaderItem(1);
        ___qtablewidgetitem1->setText(QApplication::translate("project_create", "\351\207\207\346\240\267\347\216\207", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem2 = tableWidget_sensor_parameter->horizontalHeaderItem(2);
        ___qtablewidgetitem2->setText(QApplication::translate("project_create", "\351\200\232\351\201\223\344\275\277\347\224\250\347\212\266\346\200\201", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem3 = tableWidget_sensor_parameter->horizontalHeaderItem(3);
        ___qtablewidgetitem3->setText(QApplication::translate("project_create", "\346\201\222\346\265\201\347\233\264\346\265\201\347\224\265\346\272\220", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem4 = tableWidget_sensor_parameter->horizontalHeaderItem(4);
        ___qtablewidgetitem4->setText(QApplication::translate("project_create", "AD\346\234\200\345\244\247\347\255\211\345\276\205\346\227\266\351\227\264", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem5 = tableWidget_sensor_parameter->horizontalHeaderItem(5);
        ___qtablewidgetitem5->setText(QApplication::translate("project_create", "\347\274\223\345\206\262\345\214\272\345\244\247\345\260\217", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem6 = tableWidget_sensor_parameter->horizontalHeaderItem(6);
        ___qtablewidgetitem6->setText(QApplication::translate("project_create", "\346\230\257\345\220\246\344\275\277\347\224\250\346\240\241\345\207\206", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem7 = tableWidget_sensor_parameter->horizontalHeaderItem(7);
        ___qtablewidgetitem7->setText(QApplication::translate("project_create", "\346\230\257\345\220\246\350\277\224\345\233\236\346\227\266\351\227\264\346\210\263", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem8 = tableWidget_sensor_parameter->horizontalHeaderItem(8);
        ___qtablewidgetitem8->setText(QApplication::translate("project_create", "\344\274\240\350\276\223\351\200\232\350\267\257(TCP/UDP)", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem9 = tableWidget_sensor_parameter->horizontalHeaderItem(9);
        ___qtablewidgetitem9->setText(QApplication::translate("project_create", "AD\345\220\257\345\212\250\346\226\271\345\274\217", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem10 = tableWidget_sensor_parameter->horizontalHeaderItem(10);
        ___qtablewidgetitem10->setText(QApplication::translate("project_create", "X\345\235\220\346\240\207", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem11 = tableWidget_sensor_parameter->horizontalHeaderItem(11);
        ___qtablewidgetitem11->setText(QApplication::translate("project_create", "Y\345\235\220\346\240\207", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem12 = tableWidget_sensor_parameter->horizontalHeaderItem(12);
        ___qtablewidgetitem12->setText(QApplication::translate("project_create", "Z\345\235\220\346\240\207", Q_NULLPTR));
        pushButton_save->setText(QApplication::translate("project_create", "\347\241\256\345\256\232", Q_NULLPTR));
        pushButton_cancel->setText(QApplication::translate("project_create", "\345\217\226\346\266\210", Q_NULLPTR));
    } // retranslateUi

};

namespace Ui {
    class project_create: public Ui_project_create {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_PROJECT_CREATE_H
