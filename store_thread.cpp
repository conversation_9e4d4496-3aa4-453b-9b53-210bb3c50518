#include "store_thread.h"
#include "data_managent.h"

#include<QDateTime>
#include<QDir>
#include<QFile>
#include <QCoreApplication>
#include <QMutex>

store_thread::store_thread()
{

}

void store_thread::store()
{
//    QDate date;
//    QTime time;
//    QString Path;
//    QString subPath;
//    QString filename;
//    QString strtime;

//    date = QDate::currentDate();
//    time = QTime::currentTime();

//    strtime=date.toString("yyyy-MM-dd")+" "+time.toString("hh:mm:ss");
//    Path=QCoreApplication::applicationDirPath()+"/Data/"+date.toString("yyyy-MM");

//    QDir dir;

//    if(!dir.exists(Path))
//    {
//        dir.mkpath(Path);
//    }
//    dir.cd(Path);
//    subPath=Path + "/" + date.toString("dd");
//    if(!dir.exists(subPath))
//    {
//        dir.mkpath(subPath);
//    }
//    dir.cd(subPath);
    //将datapool_tosave写入文件
    //QMutexLocker locker(&storeMutex);

    // 1) 构造当日文件路径：Data/YYYY/MM/DD.txt
    QDateTime nowUtc = QDateTime::currentDateTimeUtc();
    QDate date = nowUtc.date();
    QString baseDir = QCoreApplication::applicationDirPath() + "/Data";
    QString yearDir  = baseDir + "/" + date.toString("yyyy");
    QString monDir   = yearDir  + "/" + date.toString("MM");
    QString filePath = monDir   + "/" + date.toString("dd") + ".txt";

    QDir dir;
    if (!dir.exists(monDir) && !dir.mkpath(monDir)) {
        qWarning() << "无法创建目录：" << monDir;
        return;
    }

    // 2) 打开文件，追加写入
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text)) {
        qWarning() << "无法打开文件：" << filePath;
        return;
    }
    QTextStream out(&file);

    // 3) 写入事件记录头：UTC 时间
    QString utcStamp = nowUtc.toString(Qt::ISODate) + "Z";
    out << "===== Event UTC: " << utcStamp << " =====\n";

    // 4) 对每个通道写入：前200点和后824点
    int preCount  = qMin(200, m_xPointNum);
    int postCount = qMin(824, m_xPointNum - preCount);

    for (int ch = 0; ch < total_channel_count; ++ch) {
        out << "[Channel " << ch << "]\n";

        // 前置数据
        out << "Pre-event (" << preCount << " samples):\n";
        for (int i = 0; i < preCount; ++i) {
            out << datapool_tosave[ch][i];
            if (i + 1 < preCount) out << ' ';
        }
        out << "\n";

        // 后续数据
        out << "Post-event (" << postCount << " samples):\n";
        for (int i = 0; i < postCount; ++i) {
            out << datapool_tosave[ch][preCount + i];
            if (i + 1 < postCount) out << ' ';
        }
        out << "\n\n";
    }

    file.close();
    qDebug() << "地震事件已保存至：" << filePath;
}
