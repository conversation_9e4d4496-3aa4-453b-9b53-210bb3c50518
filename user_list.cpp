#include "user_list.h"
#include "ui_user_list.h"
#include "user_create.h"
#include "user_edit.h"
#include "message.h"

#include <QSqlQuery>
#include <QSqlError>
#include <QDebug>
#include <QTableWidget>

user_list::user_list(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::user_list)
{
    ui->setupUi(this);
    this->load_data(nullptr);

}

void user_list::load_data(QString queryName)
{
    this->ui->tableWidget->setRowCount(0);
    QSqlQuery query;
    QString sql = "SELECT id, username, realname, status, create_time FROM top_user";
    if (!queryName.isEmpty())
    {
        sql += " WHERE username LIKE '%" + queryName + "%'";
    }
    if (!query.exec(sql))
    {
        qDebug() << "Query error:" << query.lastError().text() << __LINE__;
        return;
    }

    int row = 0;
    while (query.next())
    {
        this->ui->tableWidget->insertRow(row);
        for (int col = 0; col < 5; ++col)
        {
            QTableWidgetItem *item;
            if (col == 3)
            {
                int status = query.value(col).toInt();
                if (status == 1)
                {
                    item = new QTableWidgetItem("Enabled");
                    item->setForeground(Qt::green);
                }
                else
                {
                    item = new QTableWidgetItem("Disabled");
                    item->setForeground(Qt::red);
                }
                 this->ui->tableWidget->setColumnWidth(col, 80);
            }
            else
            {
                item = new QTableWidgetItem(query.value(col).toString());
            }
            item->setTextAlignment(Qt::AlignHCenter | Qt::AlignVCenter);
             this->ui->tableWidget->setItem(row, col, item);
        }
        ++row;
    }


}

void user_list::on_pushButton_add_clicked()
{
    user_create* user_create_ui = new user_create();
    user_create_ui->show();
    connect(user_create_ui, &user_create::reload, this, &user_list::load_data);
}

void user_list::on_pushButton_edit_clicked()
{
    user_edit* user_edit_ui = new user_edit();
    user_edit_ui->show();
    connect(user_edit_ui, &user_edit::reload, this, &user_list::load_data);
}

void user_list::on_pushButton_delete_clicked()
{
    QSqlQuery query;
    QString sql = "DELETE FROM top_user WHERE id = " + this->data + ";";
    if (!query.exec(sql))
    {
        qDebug() << "Query error:" << query.lastError().text() << __LINE__ << __FILE__;
        return;
    }
    this->load_data(nullptr);
}

void user_list::on_pushButton_search_clicked()
{
    this->load_data(this->ui->lineEdit_username->text());
}

void user_list::on_tableWidget_activated(const QModelIndex &index)
{

}

user_list::~user_list()
{
    delete ui;
}


void user_list::on_tableWidget_clicked(const QModelIndex &index)
{
     this->data = this->ui->tableWidget->item(index.row(), 0)->text();
}
