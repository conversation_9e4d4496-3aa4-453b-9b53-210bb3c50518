/********************************************************************************
** Form generated from reading UI file 'setting.ui'
**
** Created by: Qt User Interface Compiler version 5.9.8
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_SETTING_H
#define UI_SETTING_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QRadioButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_Setting
{
public:
    QHBoxLayout *horizontalLayout;
    QWidget *widget;
    QHBoxLayout *horizontalLayout_2;
    QTabWidget *tabWidget;
    QWidget *tab_system;
    QHBoxLayout *horizontalLayout_3;
    QWidget *widget_system;
    QVBoxLayout *verticalLayout_2;
    QWidget *horizontalWidget;
    QHBoxLayout *horizontalLayout_7;
    QSpacerItem *horizontalSpacer_3;
    QLineEdit *lineEdit_system_name;
    QLabel *label;
    QWidget *widget_8;
    QHBoxLayout *horizontalLayout_12;
    QLabel *label_8;
    QLineEdit *lineEdit_machine_code;
    QSpacerItem *horizontalSpacer_8;
    QWidget *widget_9;
    QHBoxLayout *horizontalLayout_13;
    QLabel *label_9;
    QLineEdit *lineEdit_serial;
    QSpacerItem *horizontalSpacer_9;
    QWidget *widget_10;
    QHBoxLayout *horizontalLayout_11;
    QLabel *label_10;
    QLineEdit *lineEdit_expire_time;
    QSpacerItem *horizontalSpacer_10;
    QWidget *horizontalWidget1;
    QHBoxLayout *horizontalLayout_34;
    QLabel *label_49;
    QLineEdit *lineEdit_company;
    QSpacerItem *horizontalSpacer_31;
    QWidget *horizontalWidget_2;
    QHBoxLayout *horizontalLayout_35;
    QLabel *label_50;
    QLineEdit *lineEdit_phone;
    QSpacerItem *horizontalSpacer_33;
    QWidget *horizontalWidget2;
    QHBoxLayout *horizontalLayout_36;
    QLabel *label_51;
    QLineEdit *lineEdit_version;
    QSpacerItem *horizontalSpacer_32;
    QSpacerItem *verticalSpacer_2;
    QWidget *tab_tcp;
    QWidget *widget_tcp;
    QVBoxLayout *verticalLayout_3;
    QWidget *widget_4;
    QHBoxLayout *horizontalLayout_4;
    QLabel *label_2;
    QLineEdit *lineEdit_server_port;
    QLabel *label_5;
    QSpacerItem *horizontalSpacer_4;
    QWidget *widget_5;
    QHBoxLayout *horizontalLayout_5;
    QLabel *label_3;
    QLineEdit *lineEdit_client_port;
    QLabel *label_6;
    QSpacerItem *horizontalSpacer_5;
    QWidget *widget_6;
    QHBoxLayout *horizontalLayout_6;
    QLabel *label_4;
    QLineEdit *lineEdit_client_delay;
    QLabel *label_7;
    QSpacerItem *horizontalSpacer_6;
    QSpacerItem *verticalSpacer;
    QWidget *tab_device;
    QTableWidget *tableWidget_device_config;
    QPushButton *pushButton_save;
    QComboBox *comboBox_cardID;
    QWidget *tab_event;
    QWidget *widget_event;
    QVBoxLayout *verticalLayout_5;
    QWidget *widget_11;
    QHBoxLayout *horizontalLayout_29;
    QLabel *label_12;
    QLineEdit *lineEdit_threshold;
    QLabel *label_13;
    QSpacerItem *horizontalSpacer_11;
    QWidget *widget_28;
    QHBoxLayout *horizontalLayout_30;
    QLabel *label_14;
    QLineEdit *lineEdit_effective;
    QLabel *label_15;
    QSpacerItem *horizontalSpacer_29;
    QWidget *widget_29;
    QHBoxLayout *horizontalLayout_31;
    QLabel *label_16;
    QLineEdit *lineEdit_record_length;
    QLabel *label_17;
    QSpacerItem *horizontalSpacer_30;
    QSpacerItem *verticalSpacer_3;
    QWidget *tab_db;
    QWidget *widget_db;
    QVBoxLayout *verticalLayout_6;
    QWidget *widget_18;
    QHBoxLayout *horizontalLayout_19;
    QLabel *label_30;
    QWidget *widget_26;
    QHBoxLayout *horizontalLayout_28;
    QRadioButton *radioButton_db_type_1;
    QRadioButton *radioButton_db_type_2;
    QRadioButton *radioButton_db_type_0;
    QSpacerItem *horizontalSpacer_27;
    QLabel *label_31;
    QSpacerItem *horizontalSpacer_18;
    QWidget *widget_19;
    QHBoxLayout *horizontalLayout_20;
    QLabel *label_32;
    QLineEdit *lineEdit_db_host;
    QLabel *label_33;
    QSpacerItem *horizontalSpacer_19;
    QWidget *widget_20;
    QHBoxLayout *horizontalLayout_21;
    QLabel *label_34;
    QLineEdit *lineEdit_db_port;
    QLabel *label_35;
    QSpacerItem *horizontalSpacer_20;
    QWidget *widget_21;
    QHBoxLayout *horizontalLayout_22;
    QLabel *label_36;
    QLineEdit *lineEdit_db_name;
    QLabel *label_37;
    QSpacerItem *horizontalSpacer_21;
    QWidget *widget_22;
    QHBoxLayout *horizontalLayout_23;
    QLabel *label_38;
    QLineEdit *lineEdit_db_user;
    QLabel *label_39;
    QSpacerItem *horizontalSpacer_22;
    QWidget *widget_23;
    QHBoxLayout *horizontalLayout_24;
    QLabel *label_40;
    QLineEdit *lineEdit_db_pwd;
    QLabel *label_41;
    QSpacerItem *horizontalSpacer_23;
    QSpacerItem *verticalSpacer_5;
    QWidget *tab_file;
    QWidget *widget_file;
    QVBoxLayout *verticalLayout_7;
    QWidget *widget_24;
    QHBoxLayout *horizontalLayout_25;
    QLabel *label_42;
    QWidget *widget_3;
    QHBoxLayout *horizontalLayout_27;
    QRadioButton *radioButton_enable_file_1;
    QRadioButton *radioButton_enable_file_0;
    QSpacerItem *horizontalSpacer_26;
    QLabel *label_43;
    QSpacerItem *horizontalSpacer_24;
    QWidget *widget_25;
    QHBoxLayout *horizontalLayout_26;
    QLabel *label_44;
    QLineEdit *lineEdit_file_path;
    QLabel *label_45;
    QSpacerItem *horizontalSpacer_25;
    QSpacerItem *verticalSpacer_6;

    void setupUi(QWidget *Setting)
    {
        if (Setting->objectName().isEmpty())
            Setting->setObjectName(QStringLiteral("Setting"));
        Setting->resize(900, 500);
        Setting->setMinimumSize(QSize(0, 0));
        horizontalLayout = new QHBoxLayout(Setting);
        horizontalLayout->setObjectName(QStringLiteral("horizontalLayout"));
        widget = new QWidget(Setting);
        widget->setObjectName(QStringLiteral("widget"));
        widget->setMinimumSize(QSize(0, 0));
        widget->setStyleSheet(QLatin1String("QWidget{\n"
"	background:#fff;\n"
"}\n"
"QLineEdit{\n"
"	border:1px solid #bbb;\n"
"	border-radius:2px;\n"
"	color:#333;\n"
"}\n"
"QLineEdit:disabled{\n"
"	background:#f3f3f3;\n"
"}"));
        horizontalLayout_2 = new QHBoxLayout(widget);
        horizontalLayout_2->setObjectName(QStringLiteral("horizontalLayout_2"));
        tabWidget = new QTabWidget(widget);
        tabWidget->setObjectName(QStringLiteral("tabWidget"));
        QFont font;
        font.setFamily(QStringLiteral("Microsoft YaHei UI"));
        tabWidget->setFont(font);
        tabWidget->setLayoutDirection(Qt::LeftToRight);
        tabWidget->setTabShape(QTabWidget::Rounded);
        tabWidget->setElideMode(Qt::ElideNone);
        tab_system = new QWidget();
        tab_system->setObjectName(QStringLiteral("tab_system"));
        horizontalLayout_3 = new QHBoxLayout(tab_system);
        horizontalLayout_3->setSpacing(0);
        horizontalLayout_3->setObjectName(QStringLiteral("horizontalLayout_3"));
        horizontalLayout_3->setContentsMargins(0, 0, 0, 0);
        widget_system = new QWidget(tab_system);
        widget_system->setObjectName(QStringLiteral("widget_system"));
        verticalLayout_2 = new QVBoxLayout(widget_system);
        verticalLayout_2->setSpacing(0);
        verticalLayout_2->setObjectName(QStringLiteral("verticalLayout_2"));
        verticalLayout_2->setContentsMargins(20, 20, 0, 0);
        horizontalWidget = new QWidget(widget_system);
        horizontalWidget->setObjectName(QStringLiteral("horizontalWidget"));
        horizontalWidget->setMinimumSize(QSize(0, 40));
        horizontalWidget->setLayoutDirection(Qt::RightToLeft);
        horizontalLayout_7 = new QHBoxLayout(horizontalWidget);
        horizontalLayout_7->setSpacing(10);
        horizontalLayout_7->setObjectName(QStringLiteral("horizontalLayout_7"));
        horizontalLayout_7->setContentsMargins(0, 0, 0, 0);
        horizontalSpacer_3 = new QSpacerItem(471, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_7->addItem(horizontalSpacer_3);

        lineEdit_system_name = new QLineEdit(horizontalWidget);
        lineEdit_system_name->setObjectName(QStringLiteral("lineEdit_system_name"));
        lineEdit_system_name->setEnabled(false);
        lineEdit_system_name->setMinimumSize(QSize(300, 26));
        lineEdit_system_name->setMaximumSize(QSize(300, 26));
        lineEdit_system_name->setFont(font);
        lineEdit_system_name->setLayoutDirection(Qt::LeftToRight);
        lineEdit_system_name->setReadOnly(false);

        horizontalLayout_7->addWidget(lineEdit_system_name);

        label = new QLabel(horizontalWidget);
        label->setObjectName(QStringLiteral("label"));
        label->setMinimumSize(QSize(60, 0));
        label->setFont(font);

        horizontalLayout_7->addWidget(label);


        verticalLayout_2->addWidget(horizontalWidget);

        widget_8 = new QWidget(widget_system);
        widget_8->setObjectName(QStringLiteral("widget_8"));
        widget_8->setMinimumSize(QSize(0, 40));
        horizontalLayout_12 = new QHBoxLayout(widget_8);
        horizontalLayout_12->setSpacing(10);
        horizontalLayout_12->setObjectName(QStringLiteral("horizontalLayout_12"));
        horizontalLayout_12->setContentsMargins(0, 0, 0, 0);
        label_8 = new QLabel(widget_8);
        label_8->setObjectName(QStringLiteral("label_8"));
        label_8->setMinimumSize(QSize(60, 0));
        label_8->setFont(font);

        horizontalLayout_12->addWidget(label_8);

        lineEdit_machine_code = new QLineEdit(widget_8);
        lineEdit_machine_code->setObjectName(QStringLiteral("lineEdit_machine_code"));
        lineEdit_machine_code->setEnabled(false);
        lineEdit_machine_code->setMinimumSize(QSize(300, 26));
        lineEdit_machine_code->setMaximumSize(QSize(300, 26));
        lineEdit_machine_code->setFont(font);
        lineEdit_machine_code->setLayoutDirection(Qt::LeftToRight);
        lineEdit_machine_code->setStyleSheet(QStringLiteral(""));
        lineEdit_machine_code->setReadOnly(false);

        horizontalLayout_12->addWidget(lineEdit_machine_code);

        horizontalSpacer_8 = new QSpacerItem(471, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_12->addItem(horizontalSpacer_8);


        verticalLayout_2->addWidget(widget_8);

        widget_9 = new QWidget(widget_system);
        widget_9->setObjectName(QStringLiteral("widget_9"));
        widget_9->setMinimumSize(QSize(0, 40));
        horizontalLayout_13 = new QHBoxLayout(widget_9);
        horizontalLayout_13->setSpacing(10);
        horizontalLayout_13->setObjectName(QStringLiteral("horizontalLayout_13"));
        horizontalLayout_13->setContentsMargins(0, 0, 0, 0);
        label_9 = new QLabel(widget_9);
        label_9->setObjectName(QStringLiteral("label_9"));
        label_9->setMinimumSize(QSize(60, 0));
        label_9->setFont(font);

        horizontalLayout_13->addWidget(label_9);

        lineEdit_serial = new QLineEdit(widget_9);
        lineEdit_serial->setObjectName(QStringLiteral("lineEdit_serial"));
        lineEdit_serial->setEnabled(false);
        lineEdit_serial->setMinimumSize(QSize(300, 26));
        lineEdit_serial->setMaximumSize(QSize(300, 26));
        lineEdit_serial->setFont(font);
        lineEdit_serial->setStyleSheet(QStringLiteral(""));
        lineEdit_serial->setReadOnly(false);

        horizontalLayout_13->addWidget(lineEdit_serial);

        horizontalSpacer_9 = new QSpacerItem(471, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_13->addItem(horizontalSpacer_9);


        verticalLayout_2->addWidget(widget_9);

        widget_10 = new QWidget(widget_system);
        widget_10->setObjectName(QStringLiteral("widget_10"));
        widget_10->setMinimumSize(QSize(0, 40));
        horizontalLayout_11 = new QHBoxLayout(widget_10);
        horizontalLayout_11->setSpacing(10);
        horizontalLayout_11->setObjectName(QStringLiteral("horizontalLayout_11"));
        horizontalLayout_11->setContentsMargins(0, 0, 0, 0);
        label_10 = new QLabel(widget_10);
        label_10->setObjectName(QStringLiteral("label_10"));
        label_10->setMinimumSize(QSize(60, 0));
        label_10->setFont(font);

        horizontalLayout_11->addWidget(label_10);

        lineEdit_expire_time = new QLineEdit(widget_10);
        lineEdit_expire_time->setObjectName(QStringLiteral("lineEdit_expire_time"));
        lineEdit_expire_time->setEnabled(false);
        lineEdit_expire_time->setMinimumSize(QSize(300, 26));
        lineEdit_expire_time->setMaximumSize(QSize(300, 26));
        lineEdit_expire_time->setFont(font);
        lineEdit_expire_time->setStyleSheet(QStringLiteral(""));
        lineEdit_expire_time->setReadOnly(false);

        horizontalLayout_11->addWidget(lineEdit_expire_time);

        horizontalSpacer_10 = new QSpacerItem(471, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_11->addItem(horizontalSpacer_10);


        verticalLayout_2->addWidget(widget_10);

        horizontalWidget1 = new QWidget(widget_system);
        horizontalWidget1->setObjectName(QStringLiteral("horizontalWidget1"));
        horizontalWidget1->setMinimumSize(QSize(0, 40));
        horizontalLayout_34 = new QHBoxLayout(horizontalWidget1);
        horizontalLayout_34->setSpacing(10);
        horizontalLayout_34->setObjectName(QStringLiteral("horizontalLayout_34"));
        horizontalLayout_34->setContentsMargins(0, 0, 0, 0);
        label_49 = new QLabel(horizontalWidget1);
        label_49->setObjectName(QStringLiteral("label_49"));
        label_49->setMinimumSize(QSize(60, 0));
        label_49->setFont(font);

        horizontalLayout_34->addWidget(label_49);

        lineEdit_company = new QLineEdit(horizontalWidget1);
        lineEdit_company->setObjectName(QStringLiteral("lineEdit_company"));
        lineEdit_company->setEnabled(false);
        lineEdit_company->setMinimumSize(QSize(300, 26));
        lineEdit_company->setMaximumSize(QSize(300, 26));
        lineEdit_company->setFont(font);

        horizontalLayout_34->addWidget(lineEdit_company);

        horizontalSpacer_31 = new QSpacerItem(471, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_34->addItem(horizontalSpacer_31);


        verticalLayout_2->addWidget(horizontalWidget1);

        horizontalWidget_2 = new QWidget(widget_system);
        horizontalWidget_2->setObjectName(QStringLiteral("horizontalWidget_2"));
        horizontalWidget_2->setMinimumSize(QSize(0, 40));
        horizontalLayout_35 = new QHBoxLayout(horizontalWidget_2);
        horizontalLayout_35->setSpacing(10);
        horizontalLayout_35->setObjectName(QStringLiteral("horizontalLayout_35"));
        horizontalLayout_35->setContentsMargins(0, 0, 0, 0);
        label_50 = new QLabel(horizontalWidget_2);
        label_50->setObjectName(QStringLiteral("label_50"));
        label_50->setMinimumSize(QSize(60, 0));
        label_50->setFont(font);

        horizontalLayout_35->addWidget(label_50);

        lineEdit_phone = new QLineEdit(horizontalWidget_2);
        lineEdit_phone->setObjectName(QStringLiteral("lineEdit_phone"));
        lineEdit_phone->setEnabled(false);
        lineEdit_phone->setMinimumSize(QSize(300, 26));
        lineEdit_phone->setMaximumSize(QSize(300, 26));
        lineEdit_phone->setFont(font);

        horizontalLayout_35->addWidget(lineEdit_phone);

        horizontalSpacer_33 = new QSpacerItem(471, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_35->addItem(horizontalSpacer_33);


        verticalLayout_2->addWidget(horizontalWidget_2);

        horizontalWidget2 = new QWidget(widget_system);
        horizontalWidget2->setObjectName(QStringLiteral("horizontalWidget2"));
        horizontalWidget2->setMinimumSize(QSize(0, 40));
        horizontalLayout_36 = new QHBoxLayout(horizontalWidget2);
        horizontalLayout_36->setSpacing(10);
        horizontalLayout_36->setObjectName(QStringLiteral("horizontalLayout_36"));
        horizontalLayout_36->setContentsMargins(0, 0, 0, 0);
        label_51 = new QLabel(horizontalWidget2);
        label_51->setObjectName(QStringLiteral("label_51"));
        label_51->setMinimumSize(QSize(60, 0));
        label_51->setFont(font);

        horizontalLayout_36->addWidget(label_51);

        lineEdit_version = new QLineEdit(horizontalWidget2);
        lineEdit_version->setObjectName(QStringLiteral("lineEdit_version"));
        lineEdit_version->setEnabled(false);
        lineEdit_version->setMinimumSize(QSize(300, 26));
        lineEdit_version->setMaximumSize(QSize(300, 26));
        lineEdit_version->setFont(font);

        horizontalLayout_36->addWidget(lineEdit_version);

        horizontalSpacer_32 = new QSpacerItem(471, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_36->addItem(horizontalSpacer_32);


        verticalLayout_2->addWidget(horizontalWidget2);

        verticalSpacer_2 = new QSpacerItem(20, 150, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_2->addItem(verticalSpacer_2);


        horizontalLayout_3->addWidget(widget_system);

        tabWidget->addTab(tab_system, QString());
        tab_tcp = new QWidget();
        tab_tcp->setObjectName(QStringLiteral("tab_tcp"));
        widget_tcp = new QWidget(tab_tcp);
        widget_tcp->setObjectName(QStringLiteral("widget_tcp"));
        widget_tcp->setGeometry(QRect(0, 0, 871, 401));
        widget_tcp->setStyleSheet(QStringLiteral(""));
        verticalLayout_3 = new QVBoxLayout(widget_tcp);
        verticalLayout_3->setSpacing(0);
        verticalLayout_3->setObjectName(QStringLiteral("verticalLayout_3"));
        verticalLayout_3->setContentsMargins(20, 20, 0, 0);
        widget_4 = new QWidget(widget_tcp);
        widget_4->setObjectName(QStringLiteral("widget_4"));
        widget_4->setMinimumSize(QSize(0, 40));
        horizontalLayout_4 = new QHBoxLayout(widget_4);
        horizontalLayout_4->setSpacing(10);
        horizontalLayout_4->setObjectName(QStringLiteral("horizontalLayout_4"));
        horizontalLayout_4->setContentsMargins(0, 0, 0, 0);
        label_2 = new QLabel(widget_4);
        label_2->setObjectName(QStringLiteral("label_2"));
        label_2->setMinimumSize(QSize(0, 0));
        label_2->setFont(font);

        horizontalLayout_4->addWidget(label_2);

        lineEdit_server_port = new QLineEdit(widget_4);
        lineEdit_server_port->setObjectName(QStringLiteral("lineEdit_server_port"));
        lineEdit_server_port->setMinimumSize(QSize(300, 0));
        lineEdit_server_port->setMaximumSize(QSize(300, 26));
        lineEdit_server_port->setFont(font);
        lineEdit_server_port->setStyleSheet(QStringLiteral(""));

        horizontalLayout_4->addWidget(lineEdit_server_port);

        label_5 = new QLabel(widget_4);
        label_5->setObjectName(QStringLiteral("label_5"));
        label_5->setFont(font);
        label_5->setStyleSheet(QStringLiteral("color:#666;"));

        horizontalLayout_4->addWidget(label_5);

        horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer_4);


        verticalLayout_3->addWidget(widget_4);

        widget_5 = new QWidget(widget_tcp);
        widget_5->setObjectName(QStringLiteral("widget_5"));
        widget_5->setMinimumSize(QSize(0, 40));
        horizontalLayout_5 = new QHBoxLayout(widget_5);
        horizontalLayout_5->setSpacing(10);
        horizontalLayout_5->setObjectName(QStringLiteral("horizontalLayout_5"));
        horizontalLayout_5->setContentsMargins(0, 0, 0, 0);
        label_3 = new QLabel(widget_5);
        label_3->setObjectName(QStringLiteral("label_3"));
        label_3->setMinimumSize(QSize(0, 0));
        label_3->setFont(font);

        horizontalLayout_5->addWidget(label_3);

        lineEdit_client_port = new QLineEdit(widget_5);
        lineEdit_client_port->setObjectName(QStringLiteral("lineEdit_client_port"));
        lineEdit_client_port->setMinimumSize(QSize(300, 0));
        lineEdit_client_port->setMaximumSize(QSize(300, 26));
        lineEdit_client_port->setFont(font);
        lineEdit_client_port->setStyleSheet(QStringLiteral(""));

        horizontalLayout_5->addWidget(lineEdit_client_port);

        label_6 = new QLabel(widget_5);
        label_6->setObjectName(QStringLiteral("label_6"));
        label_6->setFont(font);
        label_6->setStyleSheet(QStringLiteral("color:#666;"));

        horizontalLayout_5->addWidget(label_6);

        horizontalSpacer_5 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_5->addItem(horizontalSpacer_5);


        verticalLayout_3->addWidget(widget_5);

        widget_6 = new QWidget(widget_tcp);
        widget_6->setObjectName(QStringLiteral("widget_6"));
        widget_6->setMinimumSize(QSize(0, 40));
        horizontalLayout_6 = new QHBoxLayout(widget_6);
        horizontalLayout_6->setSpacing(10);
        horizontalLayout_6->setObjectName(QStringLiteral("horizontalLayout_6"));
        horizontalLayout_6->setContentsMargins(0, 0, 0, 0);
        label_4 = new QLabel(widget_6);
        label_4->setObjectName(QStringLiteral("label_4"));
        label_4->setMinimumSize(QSize(0, 0));
        label_4->setFont(font);

        horizontalLayout_6->addWidget(label_4);

        lineEdit_client_delay = new QLineEdit(widget_6);
        lineEdit_client_delay->setObjectName(QStringLiteral("lineEdit_client_delay"));
        lineEdit_client_delay->setMinimumSize(QSize(300, 0));
        lineEdit_client_delay->setMaximumSize(QSize(300, 26));
        lineEdit_client_delay->setFont(font);
        lineEdit_client_delay->setStyleSheet(QStringLiteral(""));

        horizontalLayout_6->addWidget(lineEdit_client_delay);

        label_7 = new QLabel(widget_6);
        label_7->setObjectName(QStringLiteral("label_7"));
        label_7->setFont(font);
        label_7->setStyleSheet(QStringLiteral("color:#666;"));

        horizontalLayout_6->addWidget(label_7);

        horizontalSpacer_6 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_6->addItem(horizontalSpacer_6);


        verticalLayout_3->addWidget(widget_6);

        verticalSpacer = new QSpacerItem(20, 178, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_3->addItem(verticalSpacer);

        tabWidget->addTab(tab_tcp, QString());
        tab_device = new QWidget();
        tab_device->setObjectName(QStringLiteral("tab_device"));
        tableWidget_device_config = new QTableWidget(tab_device);
        if (tableWidget_device_config->columnCount() < 14)
            tableWidget_device_config->setColumnCount(14);
        QFont font1;
        font1.setPointSize(12);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        __qtablewidgetitem->setFont(font1);
        tableWidget_device_config->setHorizontalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        __qtablewidgetitem1->setFont(font1);
        tableWidget_device_config->setHorizontalHeaderItem(1, __qtablewidgetitem1);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        __qtablewidgetitem2->setFont(font1);
        tableWidget_device_config->setHorizontalHeaderItem(2, __qtablewidgetitem2);
        QTableWidgetItem *__qtablewidgetitem3 = new QTableWidgetItem();
        __qtablewidgetitem3->setFont(font1);
        tableWidget_device_config->setHorizontalHeaderItem(3, __qtablewidgetitem3);
        QTableWidgetItem *__qtablewidgetitem4 = new QTableWidgetItem();
        __qtablewidgetitem4->setFont(font1);
        tableWidget_device_config->setHorizontalHeaderItem(4, __qtablewidgetitem4);
        QTableWidgetItem *__qtablewidgetitem5 = new QTableWidgetItem();
        __qtablewidgetitem5->setFont(font1);
        tableWidget_device_config->setHorizontalHeaderItem(5, __qtablewidgetitem5);
        QTableWidgetItem *__qtablewidgetitem6 = new QTableWidgetItem();
        __qtablewidgetitem6->setFont(font1);
        tableWidget_device_config->setHorizontalHeaderItem(6, __qtablewidgetitem6);
        QTableWidgetItem *__qtablewidgetitem7 = new QTableWidgetItem();
        __qtablewidgetitem7->setFont(font1);
        tableWidget_device_config->setHorizontalHeaderItem(7, __qtablewidgetitem7);
        QTableWidgetItem *__qtablewidgetitem8 = new QTableWidgetItem();
        __qtablewidgetitem8->setFont(font1);
        tableWidget_device_config->setHorizontalHeaderItem(8, __qtablewidgetitem8);
        QTableWidgetItem *__qtablewidgetitem9 = new QTableWidgetItem();
        __qtablewidgetitem9->setFont(font1);
        tableWidget_device_config->setHorizontalHeaderItem(9, __qtablewidgetitem9);
        QTableWidgetItem *__qtablewidgetitem10 = new QTableWidgetItem();
        __qtablewidgetitem10->setFont(font1);
        tableWidget_device_config->setHorizontalHeaderItem(10, __qtablewidgetitem10);
        QTableWidgetItem *__qtablewidgetitem11 = new QTableWidgetItem();
        __qtablewidgetitem11->setFont(font1);
        tableWidget_device_config->setHorizontalHeaderItem(11, __qtablewidgetitem11);
        QTableWidgetItem *__qtablewidgetitem12 = new QTableWidgetItem();
        __qtablewidgetitem12->setFont(font1);
        tableWidget_device_config->setHorizontalHeaderItem(12, __qtablewidgetitem12);
        QTableWidgetItem *__qtablewidgetitem13 = new QTableWidgetItem();
        __qtablewidgetitem13->setFont(font1);
        tableWidget_device_config->setHorizontalHeaderItem(13, __qtablewidgetitem13);
        tableWidget_device_config->setObjectName(QStringLiteral("tableWidget_device_config"));
        tableWidget_device_config->setGeometry(QRect(0, 0, 881, 411));
        pushButton_save = new QPushButton(tab_device);
        pushButton_save->setObjectName(QStringLiteral("pushButton_save"));
        pushButton_save->setGeometry(QRect(80, 420, 80, 21));
        comboBox_cardID = new QComboBox(tab_device);
        comboBox_cardID->setObjectName(QStringLiteral("comboBox_cardID"));
        comboBox_cardID->setGeometry(QRect(190, 420, 62, 22));
        tabWidget->addTab(tab_device, QString());
        tab_event = new QWidget();
        tab_event->setObjectName(QStringLiteral("tab_event"));
        tab_event->setFont(font);
        widget_event = new QWidget(tab_event);
        widget_event->setObjectName(QStringLiteral("widget_event"));
        widget_event->setGeometry(QRect(0, 0, 871, 401));
        widget_event->setStyleSheet(QStringLiteral(""));
        verticalLayout_5 = new QVBoxLayout(widget_event);
        verticalLayout_5->setSpacing(0);
        verticalLayout_5->setObjectName(QStringLiteral("verticalLayout_5"));
        verticalLayout_5->setContentsMargins(20, 20, 0, 0);
        widget_11 = new QWidget(widget_event);
        widget_11->setObjectName(QStringLiteral("widget_11"));
        widget_11->setMinimumSize(QSize(0, 40));
        horizontalLayout_29 = new QHBoxLayout(widget_11);
        horizontalLayout_29->setSpacing(10);
        horizontalLayout_29->setObjectName(QStringLiteral("horizontalLayout_29"));
        horizontalLayout_29->setContentsMargins(0, 0, 0, 0);
        label_12 = new QLabel(widget_11);
        label_12->setObjectName(QStringLiteral("label_12"));
        label_12->setMinimumSize(QSize(60, 0));
        label_12->setFont(font);

        horizontalLayout_29->addWidget(label_12);

        lineEdit_threshold = new QLineEdit(widget_11);
        lineEdit_threshold->setObjectName(QStringLiteral("lineEdit_threshold"));
        lineEdit_threshold->setMinimumSize(QSize(300, 0));
        lineEdit_threshold->setMaximumSize(QSize(300, 26));
        lineEdit_threshold->setFont(font);
        lineEdit_threshold->setStyleSheet(QStringLiteral(""));

        horizontalLayout_29->addWidget(lineEdit_threshold);

        label_13 = new QLabel(widget_11);
        label_13->setObjectName(QStringLiteral("label_13"));
        label_13->setFont(font);
        label_13->setStyleSheet(QStringLiteral("color:#666;"));

        horizontalLayout_29->addWidget(label_13);

        horizontalSpacer_11 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_29->addItem(horizontalSpacer_11);


        verticalLayout_5->addWidget(widget_11);

        widget_28 = new QWidget(widget_event);
        widget_28->setObjectName(QStringLiteral("widget_28"));
        widget_28->setMinimumSize(QSize(0, 40));
        horizontalLayout_30 = new QHBoxLayout(widget_28);
        horizontalLayout_30->setSpacing(10);
        horizontalLayout_30->setObjectName(QStringLiteral("horizontalLayout_30"));
        horizontalLayout_30->setContentsMargins(0, 0, 0, 0);
        label_14 = new QLabel(widget_28);
        label_14->setObjectName(QStringLiteral("label_14"));
        label_14->setMinimumSize(QSize(60, 0));
        label_14->setFont(font);

        horizontalLayout_30->addWidget(label_14);

        lineEdit_effective = new QLineEdit(widget_28);
        lineEdit_effective->setObjectName(QStringLiteral("lineEdit_effective"));
        lineEdit_effective->setMinimumSize(QSize(300, 0));
        lineEdit_effective->setMaximumSize(QSize(300, 26));
        lineEdit_effective->setFont(font);
        lineEdit_effective->setStyleSheet(QStringLiteral(""));

        horizontalLayout_30->addWidget(lineEdit_effective);

        label_15 = new QLabel(widget_28);
        label_15->setObjectName(QStringLiteral("label_15"));
        label_15->setFont(font);
        label_15->setStyleSheet(QStringLiteral("color:#666;"));

        horizontalLayout_30->addWidget(label_15);

        horizontalSpacer_29 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_30->addItem(horizontalSpacer_29);


        verticalLayout_5->addWidget(widget_28);

        widget_29 = new QWidget(widget_event);
        widget_29->setObjectName(QStringLiteral("widget_29"));
        widget_29->setMinimumSize(QSize(0, 40));
        horizontalLayout_31 = new QHBoxLayout(widget_29);
        horizontalLayout_31->setSpacing(10);
        horizontalLayout_31->setObjectName(QStringLiteral("horizontalLayout_31"));
        horizontalLayout_31->setContentsMargins(0, 0, 0, 0);
        label_16 = new QLabel(widget_29);
        label_16->setObjectName(QStringLiteral("label_16"));
        label_16->setMinimumSize(QSize(60, 0));
        label_16->setFont(font);

        horizontalLayout_31->addWidget(label_16);

        lineEdit_record_length = new QLineEdit(widget_29);
        lineEdit_record_length->setObjectName(QStringLiteral("lineEdit_record_length"));
        lineEdit_record_length->setMinimumSize(QSize(300, 0));
        lineEdit_record_length->setMaximumSize(QSize(300, 26));
        lineEdit_record_length->setFont(font);
        lineEdit_record_length->setStyleSheet(QStringLiteral(""));

        horizontalLayout_31->addWidget(lineEdit_record_length);

        label_17 = new QLabel(widget_29);
        label_17->setObjectName(QStringLiteral("label_17"));
        label_17->setFont(font);
        label_17->setStyleSheet(QStringLiteral("color:#666;"));

        horizontalLayout_31->addWidget(label_17);

        horizontalSpacer_30 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_31->addItem(horizontalSpacer_30);


        verticalLayout_5->addWidget(widget_29);

        verticalSpacer_3 = new QSpacerItem(20, 178, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_5->addItem(verticalSpacer_3);

        tabWidget->addTab(tab_event, QString());
        tab_db = new QWidget();
        tab_db->setObjectName(QStringLiteral("tab_db"));
        widget_db = new QWidget(tab_db);
        widget_db->setObjectName(QStringLiteral("widget_db"));
        widget_db->setGeometry(QRect(0, 0, 620, 498));
        widget_db->setStyleSheet(QStringLiteral(""));
        verticalLayout_6 = new QVBoxLayout(widget_db);
        verticalLayout_6->setSpacing(0);
        verticalLayout_6->setObjectName(QStringLiteral("verticalLayout_6"));
        verticalLayout_6->setContentsMargins(20, 20, 0, 0);
        widget_18 = new QWidget(widget_db);
        widget_18->setObjectName(QStringLiteral("widget_18"));
        widget_18->setMinimumSize(QSize(0, 40));
        horizontalLayout_19 = new QHBoxLayout(widget_18);
        horizontalLayout_19->setSpacing(10);
        horizontalLayout_19->setObjectName(QStringLiteral("horizontalLayout_19"));
        horizontalLayout_19->setContentsMargins(0, 0, 0, 0);
        label_30 = new QLabel(widget_18);
        label_30->setObjectName(QStringLiteral("label_30"));
        label_30->setMinimumSize(QSize(60, 0));
        label_30->setFont(font);

        horizontalLayout_19->addWidget(label_30);

        widget_26 = new QWidget(widget_18);
        widget_26->setObjectName(QStringLiteral("widget_26"));
        widget_26->setMinimumSize(QSize(300, 0));
        widget_26->setMaximumSize(QSize(300, 16777215));
        horizontalLayout_28 = new QHBoxLayout(widget_26);
        horizontalLayout_28->setSpacing(20);
        horizontalLayout_28->setObjectName(QStringLiteral("horizontalLayout_28"));
        horizontalLayout_28->setContentsMargins(0, 0, 0, 0);
        radioButton_db_type_1 = new QRadioButton(widget_26);
        radioButton_db_type_1->setObjectName(QStringLiteral("radioButton_db_type_1"));
        radioButton_db_type_1->setMaximumSize(QSize(100, 16777215));
        radioButton_db_type_1->setFont(font);
        radioButton_db_type_1->setChecked(false);

        horizontalLayout_28->addWidget(radioButton_db_type_1);

        radioButton_db_type_2 = new QRadioButton(widget_26);
        radioButton_db_type_2->setObjectName(QStringLiteral("radioButton_db_type_2"));
        radioButton_db_type_2->setMaximumSize(QSize(100, 16777215));
        radioButton_db_type_2->setFont(font);

        horizontalLayout_28->addWidget(radioButton_db_type_2);

        radioButton_db_type_0 = new QRadioButton(widget_26);
        radioButton_db_type_0->setObjectName(QStringLiteral("radioButton_db_type_0"));
        radioButton_db_type_0->setMaximumSize(QSize(100, 16777215));
        radioButton_db_type_0->setFont(font);
        radioButton_db_type_0->setChecked(true);

        horizontalLayout_28->addWidget(radioButton_db_type_0);

        horizontalSpacer_27 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_28->addItem(horizontalSpacer_27);


        horizontalLayout_19->addWidget(widget_26);

        label_31 = new QLabel(widget_18);
        label_31->setObjectName(QStringLiteral("label_31"));
        label_31->setFont(font);
        label_31->setStyleSheet(QStringLiteral("color:#666;"));

        horizontalLayout_19->addWidget(label_31);

        horizontalSpacer_18 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_19->addItem(horizontalSpacer_18);


        verticalLayout_6->addWidget(widget_18);

        widget_19 = new QWidget(widget_db);
        widget_19->setObjectName(QStringLiteral("widget_19"));
        widget_19->setMinimumSize(QSize(0, 40));
        horizontalLayout_20 = new QHBoxLayout(widget_19);
        horizontalLayout_20->setSpacing(10);
        horizontalLayout_20->setObjectName(QStringLiteral("horizontalLayout_20"));
        horizontalLayout_20->setContentsMargins(0, 0, 0, 0);
        label_32 = new QLabel(widget_19);
        label_32->setObjectName(QStringLiteral("label_32"));
        label_32->setMinimumSize(QSize(60, 0));
        label_32->setFont(font);

        horizontalLayout_20->addWidget(label_32);

        lineEdit_db_host = new QLineEdit(widget_19);
        lineEdit_db_host->setObjectName(QStringLiteral("lineEdit_db_host"));
        lineEdit_db_host->setMinimumSize(QSize(300, 0));
        lineEdit_db_host->setMaximumSize(QSize(300, 26));
        lineEdit_db_host->setFont(font);
        lineEdit_db_host->setStyleSheet(QStringLiteral(""));

        horizontalLayout_20->addWidget(lineEdit_db_host);

        label_33 = new QLabel(widget_19);
        label_33->setObjectName(QStringLiteral("label_33"));
        label_33->setFont(font);
        label_33->setStyleSheet(QStringLiteral("color:#666;"));

        horizontalLayout_20->addWidget(label_33);

        horizontalSpacer_19 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_20->addItem(horizontalSpacer_19);


        verticalLayout_6->addWidget(widget_19);

        widget_20 = new QWidget(widget_db);
        widget_20->setObjectName(QStringLiteral("widget_20"));
        widget_20->setMinimumSize(QSize(0, 40));
        horizontalLayout_21 = new QHBoxLayout(widget_20);
        horizontalLayout_21->setSpacing(10);
        horizontalLayout_21->setObjectName(QStringLiteral("horizontalLayout_21"));
        horizontalLayout_21->setContentsMargins(0, 0, 0, 0);
        label_34 = new QLabel(widget_20);
        label_34->setObjectName(QStringLiteral("label_34"));
        label_34->setMinimumSize(QSize(60, 0));
        label_34->setFont(font);

        horizontalLayout_21->addWidget(label_34);

        lineEdit_db_port = new QLineEdit(widget_20);
        lineEdit_db_port->setObjectName(QStringLiteral("lineEdit_db_port"));
        lineEdit_db_port->setMinimumSize(QSize(300, 0));
        lineEdit_db_port->setMaximumSize(QSize(300, 26));
        lineEdit_db_port->setFont(font);
        lineEdit_db_port->setStyleSheet(QStringLiteral(""));

        horizontalLayout_21->addWidget(lineEdit_db_port);

        label_35 = new QLabel(widget_20);
        label_35->setObjectName(QStringLiteral("label_35"));
        label_35->setFont(font);
        label_35->setStyleSheet(QStringLiteral("color:#666;"));

        horizontalLayout_21->addWidget(label_35);

        horizontalSpacer_20 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_21->addItem(horizontalSpacer_20);


        verticalLayout_6->addWidget(widget_20);

        widget_21 = new QWidget(widget_db);
        widget_21->setObjectName(QStringLiteral("widget_21"));
        widget_21->setMinimumSize(QSize(0, 40));
        horizontalLayout_22 = new QHBoxLayout(widget_21);
        horizontalLayout_22->setSpacing(10);
        horizontalLayout_22->setObjectName(QStringLiteral("horizontalLayout_22"));
        horizontalLayout_22->setContentsMargins(0, 0, 0, 0);
        label_36 = new QLabel(widget_21);
        label_36->setObjectName(QStringLiteral("label_36"));
        label_36->setMinimumSize(QSize(60, 0));
        label_36->setFont(font);

        horizontalLayout_22->addWidget(label_36);

        lineEdit_db_name = new QLineEdit(widget_21);
        lineEdit_db_name->setObjectName(QStringLiteral("lineEdit_db_name"));
        lineEdit_db_name->setMinimumSize(QSize(300, 0));
        lineEdit_db_name->setMaximumSize(QSize(300, 26));
        lineEdit_db_name->setFont(font);
        lineEdit_db_name->setStyleSheet(QStringLiteral(""));

        horizontalLayout_22->addWidget(lineEdit_db_name);

        label_37 = new QLabel(widget_21);
        label_37->setObjectName(QStringLiteral("label_37"));
        label_37->setFont(font);
        label_37->setStyleSheet(QStringLiteral("color:#666;"));

        horizontalLayout_22->addWidget(label_37);

        horizontalSpacer_21 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_22->addItem(horizontalSpacer_21);


        verticalLayout_6->addWidget(widget_21);

        widget_22 = new QWidget(widget_db);
        widget_22->setObjectName(QStringLiteral("widget_22"));
        widget_22->setMinimumSize(QSize(0, 40));
        horizontalLayout_23 = new QHBoxLayout(widget_22);
        horizontalLayout_23->setSpacing(10);
        horizontalLayout_23->setObjectName(QStringLiteral("horizontalLayout_23"));
        horizontalLayout_23->setContentsMargins(0, 0, 0, 0);
        label_38 = new QLabel(widget_22);
        label_38->setObjectName(QStringLiteral("label_38"));
        label_38->setMinimumSize(QSize(60, 0));
        label_38->setFont(font);

        horizontalLayout_23->addWidget(label_38);

        lineEdit_db_user = new QLineEdit(widget_22);
        lineEdit_db_user->setObjectName(QStringLiteral("lineEdit_db_user"));
        lineEdit_db_user->setMinimumSize(QSize(300, 0));
        lineEdit_db_user->setMaximumSize(QSize(300, 26));
        lineEdit_db_user->setFont(font);
        lineEdit_db_user->setStyleSheet(QStringLiteral(""));

        horizontalLayout_23->addWidget(lineEdit_db_user);

        label_39 = new QLabel(widget_22);
        label_39->setObjectName(QStringLiteral("label_39"));
        label_39->setFont(font);
        label_39->setStyleSheet(QStringLiteral("color:#666;"));

        horizontalLayout_23->addWidget(label_39);

        horizontalSpacer_22 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_23->addItem(horizontalSpacer_22);


        verticalLayout_6->addWidget(widget_22);

        widget_23 = new QWidget(widget_db);
        widget_23->setObjectName(QStringLiteral("widget_23"));
        widget_23->setMinimumSize(QSize(0, 40));
        horizontalLayout_24 = new QHBoxLayout(widget_23);
        horizontalLayout_24->setSpacing(10);
        horizontalLayout_24->setObjectName(QStringLiteral("horizontalLayout_24"));
        horizontalLayout_24->setContentsMargins(0, 0, 0, 0);
        label_40 = new QLabel(widget_23);
        label_40->setObjectName(QStringLiteral("label_40"));
        label_40->setMinimumSize(QSize(60, 0));
        label_40->setFont(font);

        horizontalLayout_24->addWidget(label_40);

        lineEdit_db_pwd = new QLineEdit(widget_23);
        lineEdit_db_pwd->setObjectName(QStringLiteral("lineEdit_db_pwd"));
        lineEdit_db_pwd->setMinimumSize(QSize(300, 0));
        lineEdit_db_pwd->setMaximumSize(QSize(300, 26));
        lineEdit_db_pwd->setFont(font);
        lineEdit_db_pwd->setStyleSheet(QStringLiteral(""));

        horizontalLayout_24->addWidget(lineEdit_db_pwd);

        label_41 = new QLabel(widget_23);
        label_41->setObjectName(QStringLiteral("label_41"));
        label_41->setFont(font);
        label_41->setStyleSheet(QStringLiteral("color:#666;"));

        horizontalLayout_24->addWidget(label_41);

        horizontalSpacer_23 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_24->addItem(horizontalSpacer_23);


        verticalLayout_6->addWidget(widget_23);

        verticalSpacer_5 = new QSpacerItem(20, 178, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_6->addItem(verticalSpacer_5);

        tabWidget->addTab(tab_db, QString());
        tab_file = new QWidget();
        tab_file->setObjectName(QStringLiteral("tab_file"));
        widget_file = new QWidget(tab_file);
        widget_file->setObjectName(QStringLiteral("widget_file"));
        widget_file->setGeometry(QRect(0, 0, 600, 298));
        widget_file->setStyleSheet(QStringLiteral(""));
        verticalLayout_7 = new QVBoxLayout(widget_file);
        verticalLayout_7->setSpacing(0);
        verticalLayout_7->setObjectName(QStringLiteral("verticalLayout_7"));
        verticalLayout_7->setContentsMargins(20, 20, 0, 0);
        widget_24 = new QWidget(widget_file);
        widget_24->setObjectName(QStringLiteral("widget_24"));
        widget_24->setMinimumSize(QSize(0, 40));
        horizontalLayout_25 = new QHBoxLayout(widget_24);
        horizontalLayout_25->setSpacing(10);
        horizontalLayout_25->setObjectName(QStringLiteral("horizontalLayout_25"));
        horizontalLayout_25->setContentsMargins(0, 0, 0, 0);
        label_42 = new QLabel(widget_24);
        label_42->setObjectName(QStringLiteral("label_42"));
        label_42->setMinimumSize(QSize(70, 0));
        label_42->setFont(font);

        horizontalLayout_25->addWidget(label_42);

        widget_3 = new QWidget(widget_24);
        widget_3->setObjectName(QStringLiteral("widget_3"));
        widget_3->setMinimumSize(QSize(300, 0));
        widget_3->setMaximumSize(QSize(300, 16777215));
        horizontalLayout_27 = new QHBoxLayout(widget_3);
        horizontalLayout_27->setSpacing(20);
        horizontalLayout_27->setObjectName(QStringLiteral("horizontalLayout_27"));
        horizontalLayout_27->setContentsMargins(0, 0, 0, 0);
        radioButton_enable_file_1 = new QRadioButton(widget_3);
        radioButton_enable_file_1->setObjectName(QStringLiteral("radioButton_enable_file_1"));
        radioButton_enable_file_1->setMaximumSize(QSize(60, 16777215));
        radioButton_enable_file_1->setFont(font);
        radioButton_enable_file_1->setChecked(true);

        horizontalLayout_27->addWidget(radioButton_enable_file_1);

        radioButton_enable_file_0 = new QRadioButton(widget_3);
        radioButton_enable_file_0->setObjectName(QStringLiteral("radioButton_enable_file_0"));
        radioButton_enable_file_0->setMaximumSize(QSize(60, 16777215));
        radioButton_enable_file_0->setFont(font);

        horizontalLayout_27->addWidget(radioButton_enable_file_0);

        horizontalSpacer_26 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_27->addItem(horizontalSpacer_26);


        horizontalLayout_25->addWidget(widget_3);

        label_43 = new QLabel(widget_24);
        label_43->setObjectName(QStringLiteral("label_43"));
        label_43->setFont(font);
        label_43->setStyleSheet(QStringLiteral("color:#666;"));

        horizontalLayout_25->addWidget(label_43);

        horizontalSpacer_24 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_25->addItem(horizontalSpacer_24);


        verticalLayout_7->addWidget(widget_24);

        widget_25 = new QWidget(widget_file);
        widget_25->setObjectName(QStringLiteral("widget_25"));
        widget_25->setMinimumSize(QSize(0, 40));
        horizontalLayout_26 = new QHBoxLayout(widget_25);
        horizontalLayout_26->setSpacing(10);
        horizontalLayout_26->setObjectName(QStringLiteral("horizontalLayout_26"));
        horizontalLayout_26->setContentsMargins(0, 0, 0, 0);
        label_44 = new QLabel(widget_25);
        label_44->setObjectName(QStringLiteral("label_44"));
        label_44->setMinimumSize(QSize(70, 0));
        label_44->setFont(font);

        horizontalLayout_26->addWidget(label_44);

        lineEdit_file_path = new QLineEdit(widget_25);
        lineEdit_file_path->setObjectName(QStringLiteral("lineEdit_file_path"));
        lineEdit_file_path->setMinimumSize(QSize(300, 0));
        lineEdit_file_path->setMaximumSize(QSize(300, 26));
        lineEdit_file_path->setFont(font);
        lineEdit_file_path->setStyleSheet(QStringLiteral(""));

        horizontalLayout_26->addWidget(lineEdit_file_path);

        label_45 = new QLabel(widget_25);
        label_45->setObjectName(QStringLiteral("label_45"));
        label_45->setFont(font);
        label_45->setStyleSheet(QStringLiteral("color:#666;"));

        horizontalLayout_26->addWidget(label_45);

        horizontalSpacer_25 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_26->addItem(horizontalSpacer_25);


        verticalLayout_7->addWidget(widget_25);

        verticalSpacer_6 = new QSpacerItem(20, 178, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_7->addItem(verticalSpacer_6);

        tabWidget->addTab(tab_file, QString());

        horizontalLayout_2->addWidget(tabWidget);


        horizontalLayout->addWidget(widget);


        retranslateUi(Setting);

        tabWidget->setCurrentIndex(2);


        QMetaObject::connectSlotsByName(Setting);
    } // setupUi

    void retranslateUi(QWidget *Setting)
    {
        Setting->setWindowTitle(QApplication::translate("Setting", "\347\263\273\347\273\237\351\205\215\347\275\256", Q_NULLPTR));
        label->setText(QApplication::translate("Setting", "\345\220\215\347\247\260", Q_NULLPTR));
        label_8->setText(QApplication::translate("Setting", "\346\234\272\345\231\250\347\240\201", Q_NULLPTR));
        label_9->setText(QApplication::translate("Setting", "\345\272\217\345\210\227\345\217\267", Q_NULLPTR));
        label_10->setText(QApplication::translate("Setting", "\350\277\207\346\234\237\346\227\266\351\227\264", Q_NULLPTR));
        label_49->setText(QApplication::translate("Setting", "\345\205\254\345\217\270", Q_NULLPTR));
        label_50->setText(QApplication::translate("Setting", "\350\201\224\347\263\273\347\224\265\350\257\235", Q_NULLPTR));
        label_51->setText(QApplication::translate("Setting", "\347\211\210\346\234\254\345\217\267", Q_NULLPTR));
        tabWidget->setTabText(tabWidget->indexOf(tab_system), QApplication::translate("Setting", "\347\263\273\347\273\237\344\277\241\346\201\257", Q_NULLPTR));
        label_2->setText(QApplication::translate("Setting", "TCP\346\234\215\345\212\241\347\253\257 \347\253\257\345\217\243", Q_NULLPTR));
        label_5->setText(QApplication::translate("Setting", "\347\224\250\344\272\216\346\216\245\345\217\227ASP\350\277\236\346\216\245\357\274\214\346\216\250\351\200\201\346\226\207\344\273\266\346\266\210\346\201\257", Q_NULLPTR));
        label_3->setText(QApplication::translate("Setting", "TCP\345\256\242\346\210\267\347\253\257 \347\253\257\345\217\243", Q_NULLPTR));
        label_6->setText(QApplication::translate("Setting", "\347\224\250\344\272\216\350\277\236\346\216\245\345\276\256\351\234\207\350\256\276\345\244\207\357\274\214\350\216\267\345\217\226\346\225\260\346\215\256", Q_NULLPTR));
        label_4->setText(QApplication::translate("Setting", "TCP\345\256\242\346\210\267\347\253\257 \345\277\203\350\267\263", Q_NULLPTR));
        label_7->setText(QApplication::translate("Setting", "\346\237\245\350\257\242\345\276\256\351\234\207\350\256\276\345\244\207\346\227\266\351\227\264\351\227\264\351\232\224/\346\257\253\347\247\222", Q_NULLPTR));
        tabWidget->setTabText(tabWidget->indexOf(tab_tcp), QApplication::translate("Setting", "TCP\346\234\215\345\212\241", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem = tableWidget_device_config->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QApplication::translate("Setting", "\351\200\232\351\201\223\345\217\267", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem1 = tableWidget_device_config->horizontalHeaderItem(1);
        ___qtablewidgetitem1->setText(QApplication::translate("Setting", "\351\207\207\346\240\267\347\216\207", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem2 = tableWidget_device_config->horizontalHeaderItem(2);
        ___qtablewidgetitem2->setText(QApplication::translate("Setting", "\345\242\236\347\233\212", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem3 = tableWidget_device_config->horizontalHeaderItem(3);
        ___qtablewidgetitem3->setText(QApplication::translate("Setting", "\351\200\232\351\201\223\344\275\277\347\224\250\347\212\266\346\200\201", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem4 = tableWidget_device_config->horizontalHeaderItem(4);
        ___qtablewidgetitem4->setText(QApplication::translate("Setting", "\350\267\263\347\272\277\351\200\211\346\213\251(\347\224\265\346\265\201/\347\224\265\345\216\213)", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem5 = tableWidget_device_config->horizontalHeaderItem(5);
        ___qtablewidgetitem5->setText(QApplication::translate("Setting", "AD\346\234\200\345\244\247\347\255\211\345\276\205\346\227\266\351\227\264", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem6 = tableWidget_device_config->horizontalHeaderItem(6);
        ___qtablewidgetitem6->setText(QApplication::translate("Setting", "\347\274\223\345\206\262\345\214\272\345\244\247\345\260\217", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem7 = tableWidget_device_config->horizontalHeaderItem(7);
        ___qtablewidgetitem7->setText(QApplication::translate("Setting", "\346\230\257\345\220\246\344\275\277\347\224\250\346\240\241\345\207\206", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem8 = tableWidget_device_config->horizontalHeaderItem(8);
        ___qtablewidgetitem8->setText(QApplication::translate("Setting", "\346\230\257\345\220\246\350\277\224\345\233\236\346\227\266\351\227\264\346\210\263", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem9 = tableWidget_device_config->horizontalHeaderItem(9);
        ___qtablewidgetitem9->setText(QApplication::translate("Setting", "\344\274\240\350\276\223\351\200\232\350\267\257(TCP/UDP)", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem10 = tableWidget_device_config->horizontalHeaderItem(10);
        ___qtablewidgetitem10->setText(QApplication::translate("Setting", "AD\345\220\257\345\212\250\346\226\271\345\274\217", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem11 = tableWidget_device_config->horizontalHeaderItem(11);
        ___qtablewidgetitem11->setText(QApplication::translate("Setting", "X\345\235\220\346\240\207", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem12 = tableWidget_device_config->horizontalHeaderItem(12);
        ___qtablewidgetitem12->setText(QApplication::translate("Setting", "Y\345\235\220\346\240\207", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem13 = tableWidget_device_config->horizontalHeaderItem(13);
        ___qtablewidgetitem13->setText(QApplication::translate("Setting", "Z\345\235\220\346\240\207", Q_NULLPTR));
        pushButton_save->setText(QApplication::translate("Setting", "\344\277\235\345\255\230", Q_NULLPTR));
        comboBox_cardID->clear();
        comboBox_cardID->insertItems(0, QStringList()
         << QApplication::translate("Setting", "0", Q_NULLPTR)
         << QApplication::translate("Setting", "1", Q_NULLPTR)
         << QApplication::translate("Setting", "2", Q_NULLPTR)
        );
        tabWidget->setTabText(tabWidget->indexOf(tab_device), QApplication::translate("Setting", "\350\256\276\345\244\207\351\205\215\347\275\256", Q_NULLPTR));
        label_12->setText(QApplication::translate("Setting", "\344\272\213\344\273\266\351\230\210\345\200\274", Q_NULLPTR));
        label_13->setText(QApplication::translate("Setting", "\346\225\260\345\200\274\350\266\212\344\275\216\357\274\214\347\201\265\346\225\217\345\272\246\350\266\212\351\253\230\357\274\214\350\214\203\345\233\2641.0~3.0", Q_NULLPTR));
        label_14->setText(QApplication::translate("Setting", "\346\234\211\346\225\210\351\201\223\346\225\260", Q_NULLPTR));
        label_15->setText(QApplication::translate("Setting", "\351\234\207\345\212\250\351\201\223\346\225\260\345\244\247\344\272\216\346\234\211\346\225\210\351\201\223\346\225\260\346\227\266\357\274\214\350\247\206\344\270\272\346\234\211\346\225\210\344\272\213\344\273\266\357\274\214\344\270\215\345\276\227\350\266\205\350\277\207DSU\350\277\236\346\216\245\347\232\204\346\243\200\346\263\242\345\231\250\346\225\260\351\207\217", Q_NULLPTR));
        label_16->setText(QApplication::translate("Setting", "\346\225\260\346\215\256\346\261\240\351\225\277\345\272\246", Q_NULLPTR));
        label_17->setText(QApplication::translate("Setting", "\344\272\213\344\273\266\346\225\260\346\215\256\346\261\240\351\234\200\346\224\266\351\233\206\347\232\204\346\225\260\346\215\256\351\225\277\345\272\246\357\274\214\350\214\203\345\233\264500~2000", Q_NULLPTR));
        tabWidget->setTabText(tabWidget->indexOf(tab_event), QApplication::translate("Setting", "\344\272\213\344\273\266\351\205\215\347\275\256", Q_NULLPTR));
        label_30->setText(QApplication::translate("Setting", "\346\234\254\345\234\260\346\225\260\346\215\256\345\272\223", Q_NULLPTR));
        radioButton_db_type_1->setText(QApplication::translate("Setting", "\345\220\257\347\224\250", Q_NULLPTR));
        radioButton_db_type_2->setText(QApplication::translate("Setting", "\346\227\266\345\272\217\346\225\260\346\215\256\345\272\223", Q_NULLPTR));
        radioButton_db_type_0->setText(QApplication::translate("Setting", "\345\201\234\347\224\250", Q_NULLPTR));
        label_31->setText(QApplication::translate("Setting", "\351\200\211\346\213\251\346\227\266\345\272\217\346\225\260\346\215\256\345\272\223\351\234\200\345\256\211\350\243\205\345\271\266\351\205\215\347\275\256\350\277\236\346\216\245\344\277\241\346\201\257", Q_NULLPTR));
        label_32->setText(QApplication::translate("Setting", "\344\270\273\346\234\272", Q_NULLPTR));
        label_33->setText(QApplication::translate("Setting", "\346\227\266\345\272\217\346\225\260\346\215\256\345\272\223\346\211\200\345\234\250\346\234\215\345\212\241\345\231\250\347\232\204IP", Q_NULLPTR));
        label_34->setText(QApplication::translate("Setting", "\347\253\257\345\217\243", Q_NULLPTR));
        label_35->setText(QString());
        label_36->setText(QApplication::translate("Setting", "\346\225\260\346\215\256\345\272\223", Q_NULLPTR));
        label_37->setText(QString());
        label_38->setText(QApplication::translate("Setting", "\347\224\250\346\210\267\345\220\215", Q_NULLPTR));
        label_39->setText(QString());
        label_40->setText(QApplication::translate("Setting", "\345\257\206\347\240\201", Q_NULLPTR));
        label_41->setText(QString());
        tabWidget->setTabText(tabWidget->indexOf(tab_db), QApplication::translate("Setting", "\346\225\260\346\215\256\345\272\223\351\205\215\347\275\256", Q_NULLPTR));
        label_42->setText(QApplication::translate("Setting", "\346\225\260\346\215\256\346\226\207\344\273\266\345\212\237\350\203\275", Q_NULLPTR));
        radioButton_enable_file_1->setText(QApplication::translate("Setting", "\345\220\257\347\224\250", Q_NULLPTR));
        radioButton_enable_file_0->setText(QApplication::translate("Setting", "\345\201\234\347\224\250", Q_NULLPTR));
        label_43->setText(QApplication::translate("Setting", "\344\277\235\345\255\230dat\346\226\207\344\273\266\357\274\214\344\276\233ASP\345\210\206\346\236\220", Q_NULLPTR));
        label_44->setText(QApplication::translate("Setting", "\351\273\230\350\256\244\345\255\230\345\202\250\350\267\257\345\276\204", Q_NULLPTR));
        label_45->setText(QApplication::translate("Setting", "dat\346\226\207\344\273\266\347\232\204\351\273\230\350\256\244\345\255\230\345\202\250\350\267\257\345\276\204", Q_NULLPTR));
        tabWidget->setTabText(tabWidget->indexOf(tab_file), QApplication::translate("Setting", "\346\225\260\346\215\256\346\226\207\344\273\266\351\205\215\347\275\256", Q_NULLPTR));
    } // retranslateUi

};

namespace Ui {
    class Setting: public Ui_Setting {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_SETTING_H
