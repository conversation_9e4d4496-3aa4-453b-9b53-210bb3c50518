<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1000</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>FFT变换可视化工具</string>
  </property>
  <widget class="QWidget" name="centralWidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QCustomPlot" name="timeDomainPlot" native="true"/>
    </item>
    <item>
     <widget class="QCustomPlot" name="freqDomainPlot" native="true"/>
    </item>
    <item>
     <widget class="QCustomPlot" name="ifftPlot" native="true"/>
    </item>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QPushButton" name="generateButton">
        <property name="text">
         <string>生成测试信号</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="fftButton">
        <property name="text">
         <string>执行FFT</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QCustomPlot</class>
   <extends>QWidget</extends>
   <header>qcustomplot.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
