<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>monitor</class>
 <widget class="QWidget" name="monitor">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>805</width>
    <height>623</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>实时监控</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3">
   <item>
    <widget class="QWidget" name="widget_monitor_box" native="true">
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>70</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">background:#fff;</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <item>
       <widget class="QWidget" name="widget_tool" native="true">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>40</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>40</height>
         </size>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_4">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>10</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>10</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QLabel" name="label_device">
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
            </font>
           </property>
           <property name="text">
            <string>选择设备：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="comboBox_device">
           <property name="minimumSize">
            <size>
             <width>150</width>
             <height>24</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
            </font>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_4">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Fixed</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>10</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="label_err_msg">
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">color:red;</string>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_model_change">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>24</height>
            </size>
           </property>
           <property name="text">
            <string>总览</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_3">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_reconnect">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>24</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{
	background:#ccc;
	border:none;
	color:#000;
	border-radius:2px;
}
QPushButton:hover{
	background:#bbb;
}</string>
           </property>
           <property name="text">
            <string>重连设备</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Fixed</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>10</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_start">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>24</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{
	background:#ccc;
	border:none;
	color:#000;
	border-radius:2px;
}
QPushButton:hover{
	background:#bbb;
}</string>
           </property>
           <property name="text">
            <string>开始采集</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_5">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Fixed</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>10</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_filter">
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>24</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{
	background:#ccc;
	border:none;
	color:#000;
	border-radius:2px;
}
QPushButton:hover{
	background:#bbb;
}</string>
           </property>
           <property name="text">
            <string>开启滤波</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="widget" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QWidget" name="widget_channel_chose" native="true">
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="widget_focus" native="true"/>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
