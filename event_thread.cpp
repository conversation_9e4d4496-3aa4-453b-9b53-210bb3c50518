// event_thread.cpp
#include "event_thread.h"
#include "data_managent.h"    // 包含 datapool、datapool_tosave、event_next_data、nextad_event_flag 等全局
#include <QDebug>

bool event_coming = false;  // 全局事件状态标志

event_thread::event_thread() {}

// 保存事件前 200 点 + 后续最多 824 点
void event_thread::event_front_data()
{
    if (cur < 200) {
        qDebug() << "cur < 200，跳过前向数据保存";
        return;
    }

    for (int ch = 0; ch < total_channel_count; ++ch) {
        // 前 200 个点
        for (int m = 0; m < 200; ++m) {
            datapool_tosave[ch][m] = datapool[ch].at(cur - 200 + m);
        }
        // 后续点数不超过 824，也不超过剩余
        int backCount = qMin(1224 - cur, 824);
        for (int y = 0; y < backCount; ++y) {
            datapool_tosave[ch][200 + y] = datapool[ch].at(cur + y);
        }
    }

    // 如果触发点太靠前（<=400），直接认为后续不用专门保存，直接存
    if (cur <= 400) {
        event_coming = false;
        for (int i = 0; i < 3; ++i)
            nextad_event_flag[i] = true;
        emit store();
    }
}

// 空的线程体，事件由外部 signal-slot 触发
void event_thread::run()
{
    // 通常不在这里循环，直接由 slot event() 触发检测
}

// STA/LTA 多通道事件检测
void event_thread::event_deal()
{
    if (event_coming) return;
   // qDebug() << "event_thread::event() 开始检测事件";
    const int totalChan = total_channel_count;
    const int staWin    = sa_count;
    const int ltaWin    = la_count;
    const double thr    = threshold;
    const int effCount  = event_effective;
    //qDebug() << "event_thread::event() 开始检测事件"<<totalChan;
    // 从 ltaWin 开始，保证 cur-k >= 0
    for (cur = ltaWin; cur < m_xPointNum; ++cur) {
        int triggeredChannels = 0;

        for (int ch = 0; ch < totalChan; ++ch) {
            double lsum = 0.0, ssum = 0.0;

            // 累加长短期平方和
            for (int k = 0; k < ltaWin; ++k) {
                int idx = cur - k;
                double v = datapool[ch].at(idx);
                double sq = v * v;
                lsum += sq;
                if (k < staWin) ssum += sq;
            }
    //      qDebug() <<lsum<<ssum<<__LINE__<<__FILE__;;
            double lta = lsum / ltaWin;
            if (lta <= 0) continue;  // 防止除零

            double sta = ssum / staWin;
            // qDebug() <<sta<<lta<<__LINE__<<__FILE__;;
            if ((sta / lta) > thr) {
            //   qDebug() << "检测异常";
                ++triggeredChannels;
            }
        }

        if (triggeredChannels >= effCount) {
           // qDebug() << "检测到事件! 通道数:" << triggeredChannels << "在样本点 cur=" << cur;
            event_coming = true;
            for (int i = 0; i < 3; ++i)
                nextad_event_flag[i] = false;
            event_front_data();
            return;
        }
    }
   // qDebug() << "未检测到事件";
}

// 保存事件后续数据
void event_thread::event_back_data(int index)
{
    int copyCount = cur - 400;
    if (copyCount <= 0) {
       // qDebug() << "event_back_data() copyCount <= 0，跳过";
        return;
    }

    // 保存后续数据
    for (int t = 0; t < channel_count; ++t) {
        int ch = index * channel_count + t;
        for (int i = 0; i < copyCount; ++i) {
            datapool_tosave[ch][200 + 824 + i] = event_next_data[ch][i];
        }
    }

    // **只要收到这一卡的回调，就复位并触发存储，不再等待其他卡**
    event_coming = false;
    for (int i = 0; i < 3; ++i)
        nextad_event_flag[i] = true;

    emit store();
}

