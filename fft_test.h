#ifndef FFT_TEST_H
#define FFT_TEST_H

#include <QMainWindow>
#include <QVector>
#include <qcustomplot.h>
#include "kiss_fftr.h"
#include "fft_processer.h"

QT_BEGIN_NAMESPACE
namespace Ui { class fft_test; }
QT_END_NAMESPACE

class fft_test : public QMainWindow
{
    Q_OBJECT


public:
    fft_test(QWidget *parent = nullptr);
    ~fft_test();
    QTimer *timer1;

private slots:
    void   update_plot();

private:
    Ui::fft_test *ui;
    int changeAxisflag5=1;
};
#endif // FFT_TEST_H
