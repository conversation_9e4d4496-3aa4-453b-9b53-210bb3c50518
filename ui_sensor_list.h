/********************************************************************************
** Form generated from reading UI file 'sensor_list.ui'
**
** Created by: Qt User Interface Compiler version 5.9.8
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_SENSOR_LIST_H
#define UI_SENSOR_LIST_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_sensor_list
{
public:
    QVBoxLayout *verticalLayout;
    QWidget *widget;
    QHBoxLayout *horizontalLayout;
    QLabel *label;
    QLineEdit *lineEdit_devic_count;
    QSpacerItem *horizontalSpacer;
    QLabel *label_3;
    QComboBox *comboBox_project;
    QSpacerItem *horizontalSpacer_2;
    QLabel *label_2;
    QComboBox *comboBox_device_choose;
    QSpacerItem *horizontalSpacer_3;
    QPushButton *pushButton_save;
    QPushButton *pushButton_reload;
    QWidget *widget_2;
    QHBoxLayout *horizontalLayout_2;
    QTableWidget *tableWidget_sensor;
    QTableWidget *tableWidget_device_IP;
    QTableWidget *tableWidget_chanel;

    void setupUi(QWidget *sensor_list)
    {
        if (sensor_list->objectName().isEmpty())
            sensor_list->setObjectName(QStringLiteral("sensor_list"));
        sensor_list->resize(1148, 914);
        QFont font;
        font.setPointSize(15);
        sensor_list->setFont(font);
        verticalLayout = new QVBoxLayout(sensor_list);
        verticalLayout->setObjectName(QStringLiteral("verticalLayout"));
        widget = new QWidget(sensor_list);
        widget->setObjectName(QStringLiteral("widget"));
        horizontalLayout = new QHBoxLayout(widget);
        horizontalLayout->setObjectName(QStringLiteral("horizontalLayout"));
        label = new QLabel(widget);
        label->setObjectName(QStringLiteral("label"));
        QFont font1;
        font1.setFamily(QStringLiteral("Microsoft YaHei UI"));
        font1.setPointSize(12);
        label->setFont(font1);

        horizontalLayout->addWidget(label);

        lineEdit_devic_count = new QLineEdit(widget);
        lineEdit_devic_count->setObjectName(QStringLiteral("lineEdit_devic_count"));
        lineEdit_devic_count->setMaximumSize(QSize(1000, 16777215));
        QFont font2;
        font2.setFamily(QStringLiteral("Microsoft YaHei UI"));
        font2.setPointSize(9);
        lineEdit_devic_count->setFont(font2);
        lineEdit_devic_count->setReadOnly(true);

        horizontalLayout->addWidget(lineEdit_devic_count);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer);

        label_3 = new QLabel(widget);
        label_3->setObjectName(QStringLiteral("label_3"));
        label_3->setFont(font1);

        horizontalLayout->addWidget(label_3);

        comboBox_project = new QComboBox(widget);
        comboBox_project->setObjectName(QStringLiteral("comboBox_project"));
        comboBox_project->setFont(font2);

        horizontalLayout->addWidget(comboBox_project);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_2);

        label_2 = new QLabel(widget);
        label_2->setObjectName(QStringLiteral("label_2"));
        label_2->setFont(font1);

        horizontalLayout->addWidget(label_2);

        comboBox_device_choose = new QComboBox(widget);
        comboBox_device_choose->setObjectName(QStringLiteral("comboBox_device_choose"));
        comboBox_device_choose->setFont(font2);

        horizontalLayout->addWidget(comboBox_device_choose);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_3);

        pushButton_save = new QPushButton(widget);
        pushButton_save->setObjectName(QStringLiteral("pushButton_save"));
        pushButton_save->setFont(font1);

        horizontalLayout->addWidget(pushButton_save);

        pushButton_reload = new QPushButton(widget);
        pushButton_reload->setObjectName(QStringLiteral("pushButton_reload"));
        pushButton_reload->setFont(font1);

        horizontalLayout->addWidget(pushButton_reload);


        verticalLayout->addWidget(widget);

        widget_2 = new QWidget(sensor_list);
        widget_2->setObjectName(QStringLiteral("widget_2"));
        horizontalLayout_2 = new QHBoxLayout(widget_2);
        horizontalLayout_2->setObjectName(QStringLiteral("horizontalLayout_2"));
        tableWidget_sensor = new QTableWidget(widget_2);
        if (tableWidget_sensor->columnCount() < 4)
            tableWidget_sensor->setColumnCount(4);
        QFont font3;
        font3.setPointSize(9);
        font3.setKerning(true);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        __qtablewidgetitem->setFont(font3);
        tableWidget_sensor->setHorizontalHeaderItem(0, __qtablewidgetitem);
        QFont font4;
        font4.setPointSize(9);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        __qtablewidgetitem1->setFont(font4);
        tableWidget_sensor->setHorizontalHeaderItem(1, __qtablewidgetitem1);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        __qtablewidgetitem2->setFont(font4);
        tableWidget_sensor->setHorizontalHeaderItem(2, __qtablewidgetitem2);
        QTableWidgetItem *__qtablewidgetitem3 = new QTableWidgetItem();
        __qtablewidgetitem3->setFont(font4);
        tableWidget_sensor->setHorizontalHeaderItem(3, __qtablewidgetitem3);
        tableWidget_sensor->setObjectName(QStringLiteral("tableWidget_sensor"));
        tableWidget_sensor->setMaximumSize(QSize(375, 16777215));

        horizontalLayout_2->addWidget(tableWidget_sensor);

        tableWidget_device_IP = new QTableWidget(widget_2);
        if (tableWidget_device_IP->columnCount() < 1)
            tableWidget_device_IP->setColumnCount(1);
        QTableWidgetItem *__qtablewidgetitem4 = new QTableWidgetItem();
        tableWidget_device_IP->setHorizontalHeaderItem(0, __qtablewidgetitem4);
        tableWidget_device_IP->setObjectName(QStringLiteral("tableWidget_device_IP"));
        tableWidget_device_IP->setMaximumSize(QSize(180, 16777215));

        horizontalLayout_2->addWidget(tableWidget_device_IP);

        tableWidget_chanel = new QTableWidget(widget_2);
        if (tableWidget_chanel->columnCount() < 13)
            tableWidget_chanel->setColumnCount(13);
        QTableWidgetItem *__qtablewidgetitem5 = new QTableWidgetItem();
        __qtablewidgetitem5->setFont(font4);
        tableWidget_chanel->setHorizontalHeaderItem(0, __qtablewidgetitem5);
        QTableWidgetItem *__qtablewidgetitem6 = new QTableWidgetItem();
        tableWidget_chanel->setHorizontalHeaderItem(1, __qtablewidgetitem6);
        QTableWidgetItem *__qtablewidgetitem7 = new QTableWidgetItem();
        __qtablewidgetitem7->setFont(font4);
        tableWidget_chanel->setHorizontalHeaderItem(2, __qtablewidgetitem7);
        QTableWidgetItem *__qtablewidgetitem8 = new QTableWidgetItem();
        __qtablewidgetitem8->setFont(font4);
        tableWidget_chanel->setHorizontalHeaderItem(3, __qtablewidgetitem8);
        QTableWidgetItem *__qtablewidgetitem9 = new QTableWidgetItem();
        __qtablewidgetitem9->setFont(font4);
        tableWidget_chanel->setHorizontalHeaderItem(4, __qtablewidgetitem9);
        QTableWidgetItem *__qtablewidgetitem10 = new QTableWidgetItem();
        __qtablewidgetitem10->setFont(font4);
        tableWidget_chanel->setHorizontalHeaderItem(5, __qtablewidgetitem10);
        QTableWidgetItem *__qtablewidgetitem11 = new QTableWidgetItem();
        __qtablewidgetitem11->setFont(font4);
        tableWidget_chanel->setHorizontalHeaderItem(6, __qtablewidgetitem11);
        QTableWidgetItem *__qtablewidgetitem12 = new QTableWidgetItem();
        __qtablewidgetitem12->setFont(font4);
        tableWidget_chanel->setHorizontalHeaderItem(7, __qtablewidgetitem12);
        QTableWidgetItem *__qtablewidgetitem13 = new QTableWidgetItem();
        __qtablewidgetitem13->setFont(font4);
        tableWidget_chanel->setHorizontalHeaderItem(8, __qtablewidgetitem13);
        QTableWidgetItem *__qtablewidgetitem14 = new QTableWidgetItem();
        __qtablewidgetitem14->setFont(font4);
        tableWidget_chanel->setHorizontalHeaderItem(9, __qtablewidgetitem14);
        QTableWidgetItem *__qtablewidgetitem15 = new QTableWidgetItem();
        __qtablewidgetitem15->setFont(font4);
        tableWidget_chanel->setHorizontalHeaderItem(10, __qtablewidgetitem15);
        QTableWidgetItem *__qtablewidgetitem16 = new QTableWidgetItem();
        __qtablewidgetitem16->setFont(font4);
        tableWidget_chanel->setHorizontalHeaderItem(11, __qtablewidgetitem16);
        QTableWidgetItem *__qtablewidgetitem17 = new QTableWidgetItem();
        __qtablewidgetitem17->setFont(font4);
        tableWidget_chanel->setHorizontalHeaderItem(12, __qtablewidgetitem17);
        tableWidget_chanel->setObjectName(QStringLiteral("tableWidget_chanel"));

        horizontalLayout_2->addWidget(tableWidget_chanel);


        verticalLayout->addWidget(widget_2);


        retranslateUi(sensor_list);

        QMetaObject::connectSlotsByName(sensor_list);
    } // setupUi

    void retranslateUi(QWidget *sensor_list)
    {
        sensor_list->setWindowTitle(QApplication::translate("sensor_list", "Form", Q_NULLPTR));
        label->setText(QApplication::translate("sensor_list", "\350\256\276\345\244\207\346\225\260", Q_NULLPTR));
        label_3->setText(QApplication::translate("sensor_list", "\351\200\211\346\213\251\345\267\245\347\250\213", Q_NULLPTR));
        label_2->setText(QApplication::translate("sensor_list", "\351\200\211\346\213\251\346\243\200\346\263\242\345\231\250", Q_NULLPTR));
        pushButton_save->setText(QApplication::translate("sensor_list", "\344\277\235\345\255\230", Q_NULLPTR));
        pushButton_reload->setText(QApplication::translate("sensor_list", "\345\210\267\346\226\260", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem = tableWidget_sensor->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QApplication::translate("sensor_list", "\345\267\245\347\250\213\345\217\267", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem1 = tableWidget_sensor->horizontalHeaderItem(1);
        ___qtablewidgetitem1->setText(QApplication::translate("sensor_list", "\350\256\276\345\244\207\345\217\267", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem2 = tableWidget_sensor->horizontalHeaderItem(2);
        ___qtablewidgetitem2->setText(QApplication::translate("sensor_list", "\347\224\250\346\210\267id", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem3 = tableWidget_sensor->horizontalHeaderItem(3);
        ___qtablewidgetitem3->setText(QApplication::translate("sensor_list", "\351\200\232\351\201\223\346\225\260", Q_NULLPTR));
#ifndef QT_NO_TOOLTIP
        tableWidget_sensor->setToolTip(QApplication::translate("sensor_list", "<html><head/><body><p><br/></p></body></html>", Q_NULLPTR));
#endif // QT_NO_TOOLTIP
        QTableWidgetItem *___qtablewidgetitem4 = tableWidget_device_IP->horizontalHeaderItem(0);
        ___qtablewidgetitem4->setText(QApplication::translate("sensor_list", "\345\275\223\345\211\215\345\267\245\347\250\213\350\256\276\345\244\207IP", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem5 = tableWidget_chanel->horizontalHeaderItem(0);
        ___qtablewidgetitem5->setText(QApplication::translate("sensor_list", "\351\200\232\351\201\223\345\217\267", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem6 = tableWidget_chanel->horizontalHeaderItem(1);
        ___qtablewidgetitem6->setText(QApplication::translate("sensor_list", "\351\207\207\346\240\267\347\216\207", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem7 = tableWidget_chanel->horizontalHeaderItem(2);
        ___qtablewidgetitem7->setText(QApplication::translate("sensor_list", "\351\200\232\351\201\223\344\275\277\347\224\250\347\212\266\346\200\201", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem8 = tableWidget_chanel->horizontalHeaderItem(3);
        ___qtablewidgetitem8->setText(QApplication::translate("sensor_list", "\346\201\222\346\265\201\347\233\264\346\265\201\347\224\265\346\272\220", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem9 = tableWidget_chanel->horizontalHeaderItem(4);
        ___qtablewidgetitem9->setText(QApplication::translate("sensor_list", "AD\346\234\200\345\244\247\347\255\211\345\276\205\346\227\266\351\227\264", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem10 = tableWidget_chanel->horizontalHeaderItem(5);
        ___qtablewidgetitem10->setText(QApplication::translate("sensor_list", "\347\274\223\345\206\262\345\214\272\345\244\247\345\260\217", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem11 = tableWidget_chanel->horizontalHeaderItem(6);
        ___qtablewidgetitem11->setText(QApplication::translate("sensor_list", "\346\230\257\345\220\246\344\275\277\347\224\250\346\240\241\345\207\206", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem12 = tableWidget_chanel->horizontalHeaderItem(7);
        ___qtablewidgetitem12->setText(QApplication::translate("sensor_list", "\346\230\257\345\220\246\350\277\224\345\233\236\346\227\266\351\227\264\346\210\263", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem13 = tableWidget_chanel->horizontalHeaderItem(8);
        ___qtablewidgetitem13->setText(QApplication::translate("sensor_list", "\344\274\240\350\276\223\351\200\232\350\267\257(TCP/UDP)", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem14 = tableWidget_chanel->horizontalHeaderItem(9);
        ___qtablewidgetitem14->setText(QApplication::translate("sensor_list", "AD\345\220\257\345\212\250\346\226\271\345\274\217", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem15 = tableWidget_chanel->horizontalHeaderItem(10);
        ___qtablewidgetitem15->setText(QApplication::translate("sensor_list", "X\345\235\220\346\240\207", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem16 = tableWidget_chanel->horizontalHeaderItem(11);
        ___qtablewidgetitem16->setText(QApplication::translate("sensor_list", "Y\345\235\220\346\240\207", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem17 = tableWidget_chanel->horizontalHeaderItem(12);
        ___qtablewidgetitem17->setText(QApplication::translate("sensor_list", "Z\345\235\220\346\240\207", Q_NULLPTR));
    } // retranslateUi

};

namespace Ui {
    class sensor_list: public Ui_sensor_list {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_SENSOR_LIST_H
