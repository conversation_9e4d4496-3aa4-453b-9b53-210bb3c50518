# 多线程数据存储问题修复说明

## 问题描述
当开启三个线程进行数据存储时，只有一个线程能够成功存储数据，其他线程存储失败。

## 根本原因分析

### 1. 全局数组竞争条件
- **问题**: `recive_data[3]` 全局数组被多个线程同时访问
- **影响**: AD_THREAD 写入数据的同时，full_store 在读取，导致数据不一致

### 2. 文件操作线程安全问题
- **问题**: 多个 full_store 实例同时进行文件操作，没有同步机制
- **影响**: 文件创建、写入操作可能相互干扰

### 3. 文件指针管理错误
- **问题**: `createFile()` 中的文件指针检查逻辑错误
- **代码**: `if(file==NULL) { file->close(); }` - 空指针调用close()
- **影响**: 程序崩溃或文件句柄泄露

### 4. 数据传递不一致
- **问题**: 信号传递局部变量，但接收方读取全局数组
- **影响**: 数据不匹配，存储内容错误

## 修复方案

### 1. 添加互斥锁保护 (full_store.h/cpp)
```cpp
// 添加文件操作互斥锁
QMutex fileMutex;

// 在关键操作中使用锁
QMutexLocker locker(&fileMutex);
```

### 2. 修复文件指针管理 (full_store.cpp)
```cpp
// 修复前
if(file==NULL) {         
    file->close();  // 错误：空指针调用
}

// 修复后
if(file != nullptr) {         
    file->close();
    delete file;
    file = nullptr;
}
```

### 3. 统一数据传递方式 (full_store.cpp, deal_thread.cpp)
```cpp
// 修复前：使用全局数组
channeldata[m][i] = recive_data[index][s];

// 修复后：使用信号参数
if(s < data.size()) {
    channeldata[m][i] = data[s];
} else {
    channeldata[m][i] = 0.0;
}
```

### 4. 添加数据访问同步 (ad_thread.cpp)
```cpp
// 保护全局数组访问
{
    QMutexLocker locker(&recive_data_mutex[this->index]);
    iResult = eGet(pHandle[this->index], XC_ioGET_STREAM_DATA, 0, 
                   recive_data[this->index].data(), &x1);
}
```

### 5. 添加资源清理 (full_store.h/cpp)
```cpp
// 添加析构函数确保资源正确释放
~full_store() {
    QMutexLocker locker(&fileMutex);
    if(timer1) { timer1->stop(); delete timer1; }
    if(file) { file->close(); delete file; }
}
```

## 修改的文件列表

1. **full_store.h** - 添加互斥锁和析构函数声明
2. **full_store.cpp** - 修复文件管理、添加线程同步、使用信号参数
3. **ad_thread.cpp** - 添加数据访问同步、正确传递数据
4. **deal_thread.cpp** - 使用信号参数而非全局数组
5. **data_managent.h** - 添加互斥锁声明
6. **data_managent.cpp** - 定义互斥锁数组

## 预期效果

修复后，三个线程应该能够：
1. **并发安全**: 无数据竞争，无文件冲突
2. **数据完整**: 每个线程都能正确存储完整数据
3. **资源管理**: 正确管理文件句柄，无内存泄露
4. **稳定运行**: 长时间运行无崩溃

## 测试建议

1. 运行 `test_multithread_storage.cpp` 进行基本功能测试
2. 检查 `D:/Data` 目录下是否生成三个线程的数据文件
3. 验证文件内容是否完整且格式正确
4. 长时间运行测试稳定性

## 注意事项

1. 确保所有相关的 .cpp 文件都重新编译
2. 如果使用 Qt Creator，建议清理并重新构建项目
3. 监控系统资源使用情况，确保无内存泄露
4. 根据实际硬件性能调整线程休眠时间
