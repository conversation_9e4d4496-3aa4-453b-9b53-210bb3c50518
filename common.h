﻿#ifndef COMMON_H
#define COMMON_H

#include <QColor>
#include <QHostAddress>
#include <QHostInfo>
#include <QString>
#include <QStringList>
#include <QNetworkInterface>

#if (defined(WIN32) || defined(_WIN32) || defined(__WIN32__) || defined(__NT__))
    typedef Qt::HANDLE MyHANDLE;
#elif (defined(__linux__) || defined(__linux))
    typedef int MyHANDLE;
#endif

namespace XC_COMMON {
//获取主机所有IP地址
QList<QHostAddress> GetLocalHostAddress();

//将点十进制ip数值转化为字符串并返回
QString IPV4IntegerToString(quint32 ip);

//将ip字符串转化为点十进制数值并返回
quint32 IPV4StringToInteger(const QString& ip);

//将ip字符串转化为4字节字符数组并返回
uchar* IPV4StringToChar4(const QString& ip);

//将MAC点十进制转化为字符串并返回
QString MACIntegerToString(quint64 mac);

//将MAC字符串转化为点十进制数值并返回
quint64 MACStringToInteger(const QString& mac);

}

//背景颜色 CSS
namespace SHEET_STYLE{

const QString back_yellow {"background:rgb(240, 240, 15)"};
const QString back_blue {"background:rgb(8, 169, 255)"};
const QString back_grey {"background:rgb(131, 131, 131)"};
const QString back_red {"background:rgb(255, 0, 0)"};
const QString back_green {"background:rgb(25, 230, 50)"};

}

//RGB颜色 QColor
namespace QRGB_COLOR {

const QColor qColor_list[32] = {
    QColor(237,87,54), QColor(240,0,86),
    QColor(157,41,51), QColor(255,76,0),
    QColor(255,45,81), QColor(239,122,130),
    QColor(255,241,67), QColor(255,117,0),  //8

    QColor(255,199,115), QColor(179,92,68),
    QColor(168,132,98), QColor(96,40,30),
    QColor(130,113,0), QColor(174,112,0),
    QColor(149,85,57), QColor(211,177,12),  //16

    QColor(137,108,57), QColor(189,221,34),
    QColor(14,184,58), QColor(12,137,24),
    QColor(61,225,173), QColor(187,205,197),
    QColor(66,76,80), QColor(0,229,0),      //24

    QColor(158,208,72), QColor(123,207,166),
    QColor(68,206,246), QColor(6,82,121),
    QColor(112,243,255), QColor(75,92,196),
    QColor(86,0,79), QColor(114,94,130),    //32
};
}

#endif // COMMON_H
