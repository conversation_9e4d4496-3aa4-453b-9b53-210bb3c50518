<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>user_edit</class>
 <widget class="QWidget" name="user_edit">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>用户管理</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_2">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="widget_main" native="true">
     <property name="font">
      <font>
       <family>Microsoft YaHei UI</family>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">background:#fff;</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>97</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QWidget" name="widget_user_box" native="true">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>300</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Microsoft YaHei UI</family>
         </font>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout">
         <property name="spacing">
          <number>10</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <spacer name="verticalSpacer">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>42</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QWidget" name="widget_username" native="true">
           <layout class="QHBoxLayout" name="horizontalLayout_6">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QLabel" name="label">
              <property name="styleSheet">
               <string notr="true">color:red;</string>
              </property>
              <property name="text">
               <string>*</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="label_username">
              <property name="minimumSize">
               <size>
                <width>40</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color:#333;</string>
              </property>
              <property name="text">
               <string>账号：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="lineEdit_username">
              <property name="enabled">
               <bool>true</bool>
              </property>
              <property name="minimumSize">
               <size>
                <width>200</width>
                <height>30</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background:none;
border:1px solid #bbb;
border-radius:2px;
color:#333;</string>
              </property>
              <property name="dragEnabled">
               <bool>false</bool>
              </property>
              <property name="placeholderText">
               <string>请输入账号</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QWidget" name="widget_password" native="true">
           <layout class="QHBoxLayout" name="horizontalLayout_4">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QLabel" name="label_2">
              <property name="styleSheet">
               <string notr="true">color:red;</string>
              </property>
              <property name="text">
               <string>*</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="label_password">
              <property name="minimumSize">
               <size>
                <width>40</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color:#333;</string>
              </property>
              <property name="text">
               <string>密码：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="lineEdit_password">
              <property name="minimumSize">
               <size>
                <width>200</width>
                <height>30</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background:none;
border:1px solid #bbb;
border-radius:2px;
color:#333;</string>
              </property>
              <property name="echoMode">
               <enum>QLineEdit::Password</enum>
              </property>
              <property name="placeholderText">
               <string>请输入密码</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QWidget" name="widget_realname" native="true">
           <layout class="QHBoxLayout" name="horizontalLayout_5">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QLabel" name="label_3">
              <property name="styleSheet">
               <string notr="true">color:red;</string>
              </property>
              <property name="text">
               <string>*</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="label_realname">
              <property name="minimumSize">
               <size>
                <width>40</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color:#333;</string>
              </property>
              <property name="text">
               <string>姓名：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="lineEdit_realname">
              <property name="minimumSize">
               <size>
                <width>200</width>
                <height>30</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background:none;
border:1px solid #bbb;
border-radius:2px;
color:#333;</string>
              </property>
              <property name="placeholderText">
               <string>请输入姓名</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QWidget" name="widget_status_box" native="true">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>30</height>
            </size>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_3">
            <property name="spacing">
             <number>10</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <spacer name="horizontalSpacer_4">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeType">
               <enum>QSizePolicy::Fixed</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QRadioButton" name="radioButton_enable">
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color:#333;</string>
              </property>
              <property name="text">
               <string>启用</string>
              </property>
              <property name="checked">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QRadioButton" name="radioButton_disable">
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color:#333;</string>
              </property>
              <property name="text">
               <string>停用</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_3">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>73</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QWidget" name="widget_btn" native="true">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_7">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <spacer name="horizontalSpacer_5">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeType">
               <enum>QSizePolicy::Fixed</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton_save">
              <property name="minimumSize">
               <size>
                <width>200</width>
                <height>30</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton{
	background:#1890FF;
	border:none;
	color:#fafafa;
	border-radius:2px;
}
QPushButton:hover{
	background:#1281E8;
}</string>
              </property>
              <property name="text">
               <string>保 存</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer_2">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>42</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>97</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
