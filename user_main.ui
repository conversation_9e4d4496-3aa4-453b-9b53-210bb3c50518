<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>User_main</class>
 <widget class="QWidget" name="User_main">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>900</width>
    <height>500</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>用户中心</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_3">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="widget_main" native="true">
     <property name="styleSheet">
      <string notr="true">background:#fff;</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <property name="spacing">
       <number>10</number>
      </property>
      <property name="leftMargin">
       <number>10</number>
      </property>
      <property name="topMargin">
       <number>10</number>
      </property>
      <property name="rightMargin">
       <number>10</number>
      </property>
      <property name="bottomMargin">
       <number>10</number>
      </property>
      <item>
       <widget class="QWidget" name="widget_tab_box" native="true">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>30</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">background:#f0f0f0;</string>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <property name="spacing">
          <number>10</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QToolButton" name="toolButton_user_list">
           <property name="minimumSize">
            <size>
             <width>80</width>
             <height>24</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>10</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QToolButton{
	background:none;
	border:none;
	color:#1281E8;
}</string>
           </property>
           <property name="text">
            <string>用户管理</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QToolButton" name="toolButton_reset">
           <property name="minimumSize">
            <size>
             <width>80</width>
             <height>24</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>10</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QToolButton{
	background:none;
	border:none;
	color:#666;
}
QToolButton:hover{
	color:#1281E8;
}</string>
           </property>
           <property name="text">
            <string>重置密码</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>769</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="label_user">
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
            </font>
           </property>
           <property name="text">
            <string>登录用户：</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Fixed</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>10</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_logout">
           <property name="minimumSize">
            <size>
             <width>80</width>
             <height>24</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>9</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{
	background:#ccc;
	border:none;
	color:#000;
	border-radius:2px;
}
QPushButton:hover{
	background:#bbb;
}</string>
           </property>
           <property name="text">
            <string>退出登录</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="widget_con" native="true">
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QStackedWidget" name="stackedWidget_main">
           <property name="currentIndex">
            <number>0</number>
           </property>
           <widget class="QWidget" name="page_1">
            <layout class="QHBoxLayout" name="horizontalLayout_5">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QWidget" name="widget_monitor_box" native="true"/>
             </item>
            </layout>
           </widget>
           <widget class="QWidget" name="page_2"/>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
