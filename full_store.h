#ifndef FULL_STORE_H
#define FULL_STORE_H

#include<QThread>
#include<QTimer>
#include<QFile>
#include<QTextStream>
#include<QMutex>
//Q_DECLARE_METATYPE(QVector<double>);

class full_store : public QThread
{
       Q_OBJECT
public:
            full_store(int index);
            ~full_store();  // 添加析构函数
            QTimer*  timer1;
            QFile* file;
            QTextStream stream;
            int  index;
            QMutex fileMutex;  // 添加文件操作互斥锁
public slots:
        void createFile();
        void store_recive_data(QVector<double> data, int index, long x);

};

#endif // FULL_STORE_H
