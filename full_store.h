#ifndef FULL_STORE_H
#define FULL_STORE_H

#include<QThread>
#include<QTimer>
#include<QFile>
#include<QTextStream>
//Q_DECLARE_METATYPE(QVector<double>);

class full_store : public QThread
{
       Q_OBJECT
public:
            full_store(int index);
            QTimer*  timer1;
            QFile* file;
            QTextStream stream;
            int  index;
public slots:
        void createFile();
        void store_recive_data(QVector<double> data, int index, long x);

};

#endif // FULL_STORE_H
