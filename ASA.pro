#-------------------------------------------------
#
# Project created by QtCreator 2025-04-26T19:15:21
#
#-------------------------------------------------

QT       += core gui network concurrent
QT += core gui sql
QT += printsupport
QT += charts

CONFIG += c++17
CONFIG += c++14
QMAKE_CXXFLAGS += -std=c++1z
greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

TARGET = ASA
TEMPLATE = app

# The following define makes your compiler emit warnings if you use
# any feature of Qt which has been marked as deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

# You can also make your code fail to compile if you use deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

CONFIG += c++11

SOURCES += \
        main.cpp \
    project_list.cpp \
    record.cpp \
    user_main.cpp \
    setting.cpp \
    more.cpp \
    common.cpp \
    project_create.cpp \
    database.cpp \
    project_edit.cpp \
    user_edit.cpp \
    user_list.cpp \
    reset_password.cpp \
    user_create.cpp \
    sensor_list.cpp \
    message.cpp \
    nlenospeed.cpp \
    main_window.cpp \
    ad_thread.cpp \
    fft_test.cpp \
    kiss_fft.c \
    kiss_fftr.c \
    fft_processer.cpp \
    qcustomplot.cpp \
    device_configuration.cpp \
    monitor.cpp \
    data_managent.cpp \
    deal_thread.cpp \
    event_thread.cpp \
    store_thread.cpp \
    full_store.cpp



HEADERS += \
    project_list.h \
    record.h \
    user_main.h \
    setting.h \
    more.h \
    net3212.h \
    common.h \
    project_create.h \
    database.h \
    message.h \
    project_edit.h \
    user_edit.h \
    user_list.h \
    reset_password.h \
    user_create.h \
    sensor_list.h \
    nlenospeed.h \
    main_window.h \
    ad_thread.h \
    fft_test.h \
    kiss_fft.h \
    kiss_fft_log.h \
    kiss_fftr.h \
    fft_processer.h \
    qcustomplot.h \
    device_configuration.h \
    NET2418.h \
    monitor.h \
    data_managent.h \
    deal_thread.h \
    event_thread.h \
    store_thread.h \
    full_store.h


FORMS += \
    project_list.ui \
    record.ui \
    user_main.ui \
    setting.ui \
    more.ui \
    project_create.ui \
    project_edit.ui \
    user_edit.ui \
    user_list.ui \
    reset_password.ui \
    user_create.ui \
    sensor_list.ui \
    main_window.ui \
    fft_test.ui \
    monitor.ui




INCLUDEPATH += $$PWD/release
DEPENDPATH += $$PWD/release

INCLUDEPATH += $$PWD/exinc

win32:CONFIG(release, debug|release): LIBS += -L$$PWD/./release/ -lnet2418c
else:win32:CONFIG(debug, debug|release): LIBS += -L$$PWD/./debug/ -lnet2418c

INCLUDEPATH += $$PWD/release
DEPENDPATH += $$PWD/release
