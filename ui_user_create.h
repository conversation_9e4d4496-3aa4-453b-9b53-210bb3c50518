/********************************************************************************
** Form generated from reading UI file 'user_create.ui'
**
** Created by: Qt User Interface Compiler version 5.9.8
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_USER_CREATE_H
#define UI_USER_CREATE_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_user_create
{
public:
    QPushButton *pushButton_save;
    QPushButton *pushButton_cancel;
    QLineEdit *lineEdit_username;
    QLineEdit *lineEdit_password;
    QLineEdit *lineEdit_double_password;
    QLineEdit *lineEdit_group;

    void setupUi(QWidget *user_create)
    {
        if (user_create->objectName().isEmpty())
            user_create->setObjectName(QStringLiteral("user_create"));
        user_create->resize(604, 425);
        pushButton_save = new QPushButton(user_create);
        pushButton_save->setObjectName(QStringLiteral("pushButton_save"));
        pushButton_save->setGeometry(QRect(120, 270, 111, 61));
        pushButton_cancel = new QPushButton(user_create);
        pushButton_cancel->setObjectName(QStringLiteral("pushButton_cancel"));
        pushButton_cancel->setGeometry(QRect(250, 270, 111, 61));
        lineEdit_username = new QLineEdit(user_create);
        lineEdit_username->setObjectName(QStringLiteral("lineEdit_username"));
        lineEdit_username->setGeometry(QRect(120, 110, 241, 31));
        lineEdit_password = new QLineEdit(user_create);
        lineEdit_password->setObjectName(QStringLiteral("lineEdit_password"));
        lineEdit_password->setGeometry(QRect(120, 150, 241, 31));
        lineEdit_double_password = new QLineEdit(user_create);
        lineEdit_double_password->setObjectName(QStringLiteral("lineEdit_double_password"));
        lineEdit_double_password->setGeometry(QRect(120, 190, 241, 31));
        lineEdit_group = new QLineEdit(user_create);
        lineEdit_group->setObjectName(QStringLiteral("lineEdit_group"));
        lineEdit_group->setGeometry(QRect(120, 230, 241, 31));

        retranslateUi(user_create);

        QMetaObject::connectSlotsByName(user_create);
    } // setupUi

    void retranslateUi(QWidget *user_create)
    {
        user_create->setWindowTitle(QApplication::translate("user_create", "Form", Q_NULLPTR));
        pushButton_save->setText(QApplication::translate("user_create", "\344\277\235\345\255\230", Q_NULLPTR));
        pushButton_cancel->setText(QApplication::translate("user_create", "\345\217\226\346\266\210", Q_NULLPTR));
    } // retranslateUi

};

namespace Ui {
    class user_create: public Ui_user_create {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_USER_CREATE_H
