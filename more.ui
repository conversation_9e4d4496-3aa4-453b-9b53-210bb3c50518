<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>More</class>
 <widget class="QWidget" name="More">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>955</width>
    <height>570</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="widget" native="true">
     <property name="styleSheet">
      <string notr="true">#widget {
	background: #fff;
}</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="spacing">
       <number>10</number>
      </property>
      <property name="leftMargin">
       <number>10</number>
      </property>
      <property name="topMargin">
       <number>10</number>
      </property>
      <property name="rightMargin">
       <number>10</number>
      </property>
      <property name="bottomMargin">
       <number>10</number>
      </property>
      <item>
       <widget class="QWidget" name="backup" native="true">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>60</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>60</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">#backup {
	background:#fafafa;
}</string>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <property name="spacing">
          <number>10</number>
         </property>
         <property name="leftMargin">
          <number>10</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>10</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QToolButton" name="toolButton_backup">
           <property name="minimumSize">
            <size>
             <width>80</width>
             <height>24</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>80</width>
             <height>24</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QToolButton{
	background:#1890FF;
	border:none;
	color:#fafafa;
	border-radius:2px;
}
QToolButton:hover{
	background:#1281E8;
}</string>
           </property>
           <property name="text">
            <string>数据备份</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label">
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">color:#666;</string>
           </property>
           <property name="text">
            <string>立即备份数据，数据生成在程序根目录/backup/下</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>543</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="widget_no_more" native="true">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>30</height>
         </size>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>423</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="label_2">
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">color:#999;</string>
           </property>
           <property name="text">
            <string>没有更多功能了</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_3">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>422</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <spacer name="verticalSpacer">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
