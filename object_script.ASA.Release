INPUT(
./release\main.o
./release\project_list.o
./release\record.o
./release\user_main.o
./release\setting.o
./release\more.o
./release\common.o
./release\project_create.o
./release\database.o
./release\project_edit.o
./release\user_edit.o
./release\user_list.o
./release\reset_password.o
./release\user_create.o
./release\sensor_list.o
./release\message.o
./release\nlenospeed.o
./release\main_window.o
./release\ad_thread.o
./release\fft_test.o
./release\kiss_fft.o
./release\kiss_fftr.o
./release\fft_processer.o
./release\qcustomplot.o
./release\device_configuration.o
./release\monitor.o
./release\data_managent.o
./release\deal_thread.o
./release\event_thread.o
./release\store_thread.o
./release\full_store.o
./release\moc_project_list.o
./release\moc_record.o
./release\moc_user_main.o
./release\moc_setting.o
./release\moc_more.o
./release\moc_project_create.o
./release\moc_project_edit.o
./release\moc_user_edit.o
./release\moc_user_list.o
./release\moc_reset_password.o
./release\moc_user_create.o
./release\moc_sensor_list.o
./release\moc_main_window.o
./release\moc_ad_thread.o
./release\moc_fft_test.o
./release\moc_qcustomplot.o
./release\moc_monitor.o
./release\moc_deal_thread.o
./release\moc_event_thread.o
./release\moc_store_thread.o
./release\moc_full_store.o
);
