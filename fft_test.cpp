#include "fft_test.h"
#include "ui_fft_test.h"
#include "fft_processer.h"
#include "qcustomplot.h"
#include "data_managent.h"

#include<QDateTime>
#include<QDebug>
#include <QVector>
#include <QDebug>
#include <cmath>

fft_test::fft_test(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::fft_test)
{
    ui->setupUi(this);
    timer1=new QTimer(this);
    //timer1->setTimerType(Qt::PreciseTimer);
    QObject::connect( timer1,SIGNAL(timeout()), this, SLOT(update_plot()));
    timer1->start(10);
    this->ui->widget->addGraph();
    this->ui->widget->graph(0)->setLineStyle(QCPGraph::lsLine);  // 折线连接
    this->ui->widget->xAxis->setRange(0, 1024);
    this->ui->widget->yAxis->setRange(-1, 1);
    this->ui->widget->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom);
    //--------------------------------------------------------------------------
    this->ui->widget_2->addGraph();
    this->ui->widget_2->graph(0)->setLineStyle(QCPGraph::lsLine);  // 折线连接
    this->ui->widget_2->xAxis->setRange(0, 1024);
    this->ui->widget_2->yAxis->setRange(-1, 1);
    this->ui->widget_2->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom);
    //--------------------------------------------------------------------------
    this->ui->widget_3->addGraph();
    this->ui->widget_3->graph(0)->setLineStyle(QCPGraph::lsLine);  // 折线连接
    this->ui->widget_3->xAxis->setRange(0, 1024);
    this->ui->widget_3->yAxis->setRange(-1, 1);
    this->ui->widget_3->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom);
    //--------------------------------------------------------------------------
    this->ui->widget_4->addGraph();
    this->ui->widget_4->graph(0)->setLineStyle(QCPGraph::lsLine);  // 折线连接
    this->ui->widget_4->xAxis->setRange(0, 1024);
    this->ui->widget_4->yAxis->setRange(-1, 1);
    this->ui->widget_4->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom);
    //connect(ui->widget,&QCustomPlot::mouseDoubleClick,this,[=]{changeAxisflag5=1;});
    //connect(ui->widget,&QCustomPlot::mouseWheel, this,[=]{changeAxisflag5=0;});
}

fft_test::~fft_test()
{

}

void fft_test::update_plot()
{
    QVector<double>  x1;
    QVector<double>  y1;

    QVector<double>  x2;
    QVector<double>  y2;

    QVector<double>  x3;
    QVector<double>  y3;

    QVector<double>  x4;
    QVector<double>  y4;

    QDateTime pktTime;
    for(int i = 0, index = 0; i < 16 * m_xPointNum+1; ++i)
    {
        if(i == 0)
        {
            pktTime = QDateTime::fromMSecsSinceEpoch(recive_data[0].at(0) * 1000);
            // qDebug()<<pktTime<<endl<<"net2418c_ad.cpp 576";
            continue;
        }
        // 通道号
        int chNo =index % 16;

        //轮次
        int round = index / 16;
        double dif_msec = round * (1 / 10000.00) * 1000;
        qint64 mSecs = pktTime.toMSecsSinceEpoch();

        // 在10Khz下，0.1ms采一个点，可能计算时间存在损失，尝试转为us
        double curT = (mSecs * 1000.0 + dif_msec * 1000.0) / 1000000.0;

        if(chNo==0)
        {
            x1.append(round);
            y1.append(recive_data[0].at(i));
        }

        if(chNo==2)
        {
            x2.append(round);
            y2.append(recive_data[0].at(i));
        }

        if(chNo==4)
        {
            x3.append(round);
            y3.append(recive_data[0].at(i));
        }

        if(chNo==6)
        {
            x4.append(round);
            y4.append(recive_data[0].at(i));
        }
        ++index;
    }

    this->ui->widget->graph(0)->data().clear();
    this->ui->widget->graph(0)->setData(x1,y1);
    this->ui->widget->replot();

    this->ui->widget_2->graph(0)->data().clear();
    this->ui->widget_2->graph(0)->setData(x2,y2);
    this->ui->widget_2->replot();

    this->ui->widget_3->graph(0)->data().clear();
    this->ui->widget_3->graph(0)->setData(x3,y3);
    this->ui->widget_3->replot();

    this->ui->widget_4->graph(0)->data().clear();
    this->ui->widget_4->graph(0)->setData(x4,y4);
    this->ui->widget_4->replot();
}

