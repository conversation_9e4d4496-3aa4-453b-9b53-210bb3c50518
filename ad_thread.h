#ifndef AD_THREAD_H
#define AD_THREAD_H

#include <QObject>
#include <QThread>
#include <QVariant>
#include <QMetaType>
#include <QVector>

#include "common.h"
#include "message.h"

//Q_DECLARE_METATYPE(QVector<double>);

class AD_THREAD : public QThread
{
    Q_OBJECT
public:
    explicit AD_THREAD(int index,QObject *parent = nullptr);

    bool threadStop;
    void run() override;

   // QVector<double> recvData;
   double channel_isEnable[channel_count];//通道是否启用
    int  index;
    int  enablechanelnum=0;

protected:

signals:
    //发送采集到的数据
    void send_recive_data(QVector<double> data, int index, long x);

private slots:
    void GetHandle(MyHANDLE handle);

    void recvThreadStop(bool stop);

private:
    QVariant sendData; // 信号传递的数据

};

#endif // AD_THREAD_H
