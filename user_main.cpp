#include "user_main.h"
#include "ui_user_main.h"
#include "user_edit.h"
#include "user_list.h"
#include "reset_password.h"

#include <QDebug>

User_main::User_main(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::User_main)
{
    ui->setupUi(this);

    this->ui->label_user->setText("test");

    this->user_list_ui = new user_list();
    this->user_edit_ui = new user_edit();
    this->reset_password_ui = new reset_password();

    this->ui->stackedWidget_main->insertWidget(0, this->user_list_ui);
    this->ui->stackedWidget_main->insertWidget(1,this->user_edit_ui);
    this->ui->stackedWidget_main->insertWidget(2, this->reset_password_ui);

    this->ui->stackedWidget_main->setCurrentWidget(this->user_list_ui);
}

User_main::~User_main()
{
    delete ui;
}

void User_main::on_toolButton_user_list_clicked()
{
   this->ui->stackedWidget_main->setCurrentIndex(0);
}

void User_main::on_toolButton_reset_clicked()
{
    this->ui->stackedWidget_main->setCurrentIndex(2);
}

void User_main::on_pushButton_logout_clicked()
{

}
