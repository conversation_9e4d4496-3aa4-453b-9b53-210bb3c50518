#include "main_window.h"
#include "record.h"
#include "monitor.h"
#include "setting.h"
#include "more.h"
#include "message.h"

#include"project_list.h"
#include"project_create.h"
#include"sensor_list.h"

#include "user_list.h"
#include "user_create.h"
#include "user_edit.h"

#include <QMenuBar>
#include <QMenu>
#include <QAction>
#include <QStackedWidget>
#include <QVBoxLayout>
#include <QToolButton>
#include <QDebug>
#include <QStackedWidget>
#include <QApplication>

main_window::main_window(QWidget *parent) :
    QMainWindow(parent)
{
    // 设置窗口标题
    this->setWindowTitle("NET2412微震采集");

    QWidget *centralWidget = new QWidget(this);
    this->setCentralWidget(centralWidget);

    QVBoxLayout *mainLayout = new QVBoxLayout(centralWidget);
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->setSpacing(0);

    stackedWidget = new QStackedWidget(centralWidget);
    mainLayout->addWidget(stackedWidget);

    this->project_list_ui = new Project_list();
    this->project_create_ui = new class project_create();
    this->sensor_list_ui = new sensor_list();

    this->record_ui = new Record();
    this->monitor_ui = new monitor();

    this->user_list_ui = new user_list();
    this->user_create_ui = new user_create();
    this->user_edit_ui = new user_edit();

    this->setting_ui = new Setting();
    this->more_ui = new More();

    stackedWidget->insertWidget(ui_index::project_list_index, this->project_list_ui);
    stackedWidget->insertWidget(ui_index::project_create_index, this->project_create_ui);
    stackedWidget->insertWidget(ui_index::project_edit_index, this->sensor_list_ui);

    stackedWidget->insertWidget(ui_index::monitor_index, this->monitor_ui);
    stackedWidget->insertWidget(ui_index::record_index, this->record_ui);

    stackedWidget->insertWidget(ui_index::user_list_index, this->user_list_ui);
    stackedWidget->insertWidget(ui_index::user_create_index, this->user_create_ui);
    stackedWidget->insertWidget(ui_index::user_edit_index, this->user_edit_ui);

    stackedWidget->insertWidget(ui_index::setting, this->setting_ui);
    stackedWidget->insertWidget(ui_index::more, this->more_ui);

    // 创建菜单栏
    this->menu_bar = new QMenuBar();
    this->setMenuBar(menu_bar);

    this->first_level_menu_init();
    this->sencond_level_menu_init();

    stackedWidget->setCurrentIndex(ui_index::project_list_index);

    QString styleSheet = R"(
    /* 主窗口整体样式 */
    QMainWindow {
        background-color: #f5f7fa; /* 浅灰蓝色背景，营造清爽氛围 */
    }

    /* 中心部件，作为基础容器 */
    QWidget#centralwidget {
        background-color: transparent; /* 透明，让 stackedWidget 等自己控制背景 */
    }

    /* 栈式窗口，用于页面切换的容器 */
    QStackedWidget {
        background-color: white;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 10px;
        margin: 8px; /* 与主窗口边缘保持间距 */
    }

    /* 栈式窗口里的页面，可给不同页面差异化设置，这里统一先简单处理 */
    QWidget#page, QWidget#page_2 {
        background-color: white;
        border-radius: 6px;
        padding: 12px;
    }

    /* 若页面里有其他控件（如按钮、标签等，可提前预设通用样式，后续叠加） */
    QLabel {
        color: #333333;
        font-size: 13px;
    }
    QPushButton {
        background-color: #409eff; /* 经典主题蓝色 */
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 16px;
        margin: 4px;
    }
    QPushButton:hover {
        background-color: #66b1ff; /* 悬停变浅蓝 */
    }
    QPushButton:pressed {
        background-color: #337ab7; /* 按下加深 */
    }

    )";
    this->setObjectName("main_window");
    this->setStyleSheet(styleSheet);

}

main_window::~main_window()
{
}

void main_window::first_level_menu_init()
{
    this->project_manage_menu = this->menu_bar->addMenu("工程管理");
    this->monitor_and_record_menu = this->menu_bar->addMenu("监控与回放");
    this->user_manage_menu = this->menu_bar->addMenu("用户管理");
    this->setting_menu = this->menu_bar->addMenu("设置");
}
void main_window::sencond_level_menu_init()
{
//==========================================================================================
    QAction *project_list = new QAction("工程列表");
    QAction *project_create = new QAction("创建工程");
    QAction *project_edit = new QAction("编辑工程");

    this->project_manage_menu->addAction(project_list);
    this->project_manage_menu->addAction(project_create);
    this->project_manage_menu->addAction(project_edit);

    connect(project_list, &QAction::triggered, [this]() {
        stackedWidget->setCurrentIndex(ui_index::project_list_index);
    });

    connect(project_create, &QAction::triggered, [this]() {
        stackedWidget->setCurrentIndex(ui_index::project_create_index);
    });

    connect(project_edit, &QAction::triggered, [this]() {
        stackedWidget->setCurrentIndex(ui_index::project_edit_index);
    });
//==========================================================================================
    QAction *monitor = new QAction("事件监控");
    QAction *record = new QAction("事件回放");

    this->monitor_and_record_menu->addAction(monitor);
    this->monitor_and_record_menu->addAction(record);

    connect(monitor, &QAction::triggered, [this]() {
        stackedWidget->setCurrentIndex(ui_index::monitor_index);
    });

    connect(record, &QAction::triggered, [this]() {
        stackedWidget->setCurrentIndex(ui_index::record_index);
    });
//==========================================================================================
    QAction *user_list = new QAction("用户列表");
    QAction *user_create = new QAction("创建用户");
    QAction *user_edit = new QAction("编辑用户");

    this->user_manage_menu->addAction(user_list);
    this->user_manage_menu->addAction(user_create);
    this->user_manage_menu->addAction(user_edit);

    connect(user_list, &QAction::triggered, [this]() {
        stackedWidget->setCurrentIndex(ui_index::user_list_index);
    });

    connect(user_create, &QAction::triggered, [this]() {
        stackedWidget->setCurrentIndex(ui_index::user_create_index);
    });

    connect(user_edit, &QAction::triggered, [this]() {
        stackedWidget->setCurrentIndex(ui_index::user_edit_index);
    });

//==========================================================================================
    QAction* quit = new QAction("退出");
    this->menu_bar->addAction(quit);
    connect(quit, &QAction::trigger, this, [](){
        QApplication::quit();
    });
//==========================================================================================
}



