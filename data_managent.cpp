#include "data_managent.h"
#include "message.h"

#include <QDateTime>
#include <QVariant>

MyHANDLE pHandle[3];

AD_THREAD *pThread0;
AD_THREAD *pThread1;
AD_THREAD *pThread2;

deal_thread* dealthread;//整理成datapool[totalchannelCount];
QQueue<double> datapool[total_channel_count];

double event_next_data[total_channel_count][m_xPointNum];

event_thread* eventthread;//整理成datapool_tosave
double datapool_tosave[48][1024];

store_thread* storethread;//存储datapool_tosave
full_store*  fullstore0;
full_store*  fullstore1;
full_store*  fullstore2;
QDateTime pktTime[3];

QDateTime pktTime0, pktTime1, pktTime2;

bool nextad_event_flag[3] = {1, 1, 1};


QVector<double> recive_data[3];

void data_and_thread_init()
{
    for(int i = 0; i < total_channel_count; i++)
    {
       datapool[i].reserve(1224);
       for (int j = 0; j < 1224; ++j)
       {
           datapool[i].enqueue(0.0);
       }
    }

    for(int i = 0; i < 3; i++)
    {
        pHandle[i] = MyHANDLE_init(sensor_config_message[i]);
        recive_data[i].resize(channel_count * m_xPointNum + 1);
    }

    pThread0 = new AD_THREAD(0);
    pThread1 = new AD_THREAD(1);
    pThread2 = new AD_THREAD(2);

    dealthread = new deal_thread();
    eventthread= new event_thread();
    storethread= new store_thread();

    fullstore0 =new full_store(0);
    fullstore1 =new full_store(1);
    fullstore2 =new full_store(2);
    QObject::connect(pThread0, &AD_THREAD::send_recive_data, dealthread, &deal_thread::deal);
    QObject::connect(pThread1, &AD_THREAD::send_recive_data, dealthread, &deal_thread::deal);
    QObject::connect(pThread2, &AD_THREAD::send_recive_data, dealthread, &deal_thread::deal);
    QObject::connect(pThread0, &AD_THREAD::send_recive_data, fullstore0, &full_store::store_recive_data);
    QObject::connect(pThread1, &AD_THREAD::send_recive_data, fullstore1, &full_store::store_recive_data);
    QObject::connect(pThread2, &AD_THREAD::send_recive_data, fullstore2, &full_store::store_recive_data);;
    QObject::connect(dealthread, &deal_thread::sendData, eventthread, &event_thread::event_deal);
    QObject::connect(dealthread , &deal_thread::sendeventData, eventthread, &event_thread::event_back_data);
    QObject::connect(eventthread ,&event_thread::store, storethread, &store_thread::store);

    pThread0->threadStop = false;
   pThread0->start();
    pThread1->threadStop = false;
   pThread1->start();
   pThread2->threadStop = false;
     pThread2->start();
    dealthread->start();
    eventthread->start();
    fullstore0->start();        fullstore1->start();    fullstore2->start();
}
data_managent::data_managent()
{

}
