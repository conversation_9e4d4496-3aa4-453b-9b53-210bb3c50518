#include "device_configuration.h"
#include "common.h"
#include "NET2418.h"
#include "message.h"

device_configuration::device_configuration()
{

}

MyHANDLE device_configuration::MyHANDLE_init(sensor_config* config)
{
    long    x1 = 8;
    //QString cardIP="************";
    int iResult = 0;
    bool useFirst = false;

    MyHANDLE pHandle; // 设备句柄

    iResult = OpenDAQDevice(config->IP.toStdString().c_str(), useFirst, &pHandle);

    double ipValue = XC_COMMON::IPV4StringToInteger("************");
//==========================================================================================================
    iResult = ePut(pHandle, XC_ioPUT_CONFIG, XC_ctETHERNET_LOCALIP, &ipValue, &x1);//设置本地IP
    if(iResult)
    {
        qDebug()<<"设置IP失败！返回码：" + QString::number(iResult) << __LINE__ << __FILE__;
    }
//==========================================================================================================
    iResult = ePut(pHandle, XC_ioPUT_CONFIG, XC_chSTREAM_SCAN_FREQUENCY,config->sampling_rate, &x1);
    if(iResult)
    {
        qDebug() << "配置采样率错误" << __LINE__ << __FILE__;
    }
//==========================================================================================================
    iResult = ePut(pHandle, XC_ioPUT_CONFIG, XC_chSTREAM_Channel, config->channel_isEnable, &x1);
    if(iResult)
    {
        qDebug() << "配置使用通道错误" << __LINE__ << __FILE__;
    }
//==========================================================================================================
    iResult = ePut(pHandle, XC_ioPUT_CONFIG, XC_ioPUT_ConstantVolSource, config->current_source, &x1);
    for(int i=0;i<16;i++)
        qDebug()<<config->current_source[i]<< __LINE__ << __FILE__;

    if(iResult)
    {
        qDebug() << "配置配置交直流及恒流源错误" << __LINE__ << __FILE__;
    }
//==========================================================================================================
    iResult = ePut(pHandle, XC_ioPUT_CONFIG, XC_chSTREAM_COMMUNICATION_TIMEOUT, config->waitin_time, &x1);
    if(iResult)
    {
        qDebug() << "配置AD最大等待时间错误" << __LINE__ << __FILE__;
    }
//==========================================================================================================
    iResult = ePut(pHandle, XC_ioPUT_CONFIG, XC_ioPUT_STREAM_BUFFER, config->buffer_size, &x1);
    if(iResult)
    {
        qDebug() << "配置缓冲区大小错误" << __LINE__ << __FILE__;
    }
//==========================================================================================================
    iResult = ePut(pHandle, XC_ioPUT_CONFIG, XC_chSTREAM_ADJ, config->calibration_isEnable, &x1);
    if(iResult)
    {
        qDebug() << "配置是否使用校准错误" << __LINE__ << __FILE__;
    }
//==========================================================================================================
    iResult = ePut(pHandle, XC_ioPUT_CONFIG, XC_ioPUT_STREAM_TIME, config->time_stamp_isEnable, &x1);
    if(iResult)
    {
        qDebug() << "配置是否返回时间戳错误" << __LINE__ << __FILE__;
    }
//==========================================================================================================
    //传输通路控制，0-关闭，1-开启
    long ADControl = 1;
    // 通路类型，0-TCP，1-UDP
    long Atype = 1;
    // 指向上位机端口号，0-随机端口
    long portNo = 0;

    iResult = eControlADAccess(pHandle, ADControl, Atype, &portNo);
    if(iResult)
    {
        qDebug() << "配置传输通路错误" << __LINE__ << __FILE__;
    }
//==========================================================================================================
    // 配置AD启动
    iResult = ePut(pHandle, XC_ioSTART_STREAM, 8, nullptr, &x1);
    if(iResult)
    {
        qDebug() << "配置AD启动错误" << __LINE__ << __FILE__;
    }
//==========================================================================================================
    return pHandle;
}
