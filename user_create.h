#ifndef USER_CREATE_H
#define USER_CREATE_H

#include <QWidget>

namespace Ui {
class user_create;
}

class user_create : public QWidget
{
    Q_OBJECT

public:
    explicit user_create(QWidget *parent = nullptr);
    ~user_create();

signals:
    void reload(QString q);

private slots:
    void on_pushButton_save_clicked();

    void on_pushButton_cancel_clicked();

private:
    Ui::user_create *ui;
};

#endif // USER_CREATE_H
