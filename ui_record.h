/********************************************************************************
** Form generated from reading UI file 'record.ui'
**
** Created by: Qt User Interface Compiler version 5.9.8
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_RECORD_H
#define UI_RECORD_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QDateTimeEdit>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_Record
{
public:
    QHBoxLayout *horizontalLayout;
    QWidget *widget_record_box;
    QVBoxLayout *verticalLayout;
    QWidget *widget_tip;
    QHBoxLayout *horizontalLayout_2;
    QLabel *label_tip;
    QPushButton *pushButton_reconnect;
    QSpacerItem *horizontalSpacer;
    QWidget *widget_tool;
    QHBoxLayout *horizontalLayout_4;
    QDateTimeEdit *dateTimeEdit_start;
    QDateTimeEdit *dateTimeEdit_end;
    QPushButton *pushButton_search;
    QSpacerItem *horizontalSpacer_2;
    QPushButton *pushButton_pause;
    QPushButton *pushButton_filter;
    QWidget *widget;
    QHBoxLayout *horizontalLayout_5;
    QTableWidget *tableWidget_record;
    QWidget *widget_canvas;

    void setupUi(QWidget *Record)
    {
        if (Record->objectName().isEmpty())
            Record->setObjectName(QStringLiteral("Record"));
        Record->resize(900, 500);
        horizontalLayout = new QHBoxLayout(Record);
        horizontalLayout->setSpacing(0);
        horizontalLayout->setObjectName(QStringLiteral("horizontalLayout"));
        horizontalLayout->setContentsMargins(0, 0, 0, 0);
        widget_record_box = new QWidget(Record);
        widget_record_box->setObjectName(QStringLiteral("widget_record_box"));
        verticalLayout = new QVBoxLayout(widget_record_box);
        verticalLayout->setSpacing(0);
        verticalLayout->setObjectName(QStringLiteral("verticalLayout"));
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        widget_tip = new QWidget(widget_record_box);
        widget_tip->setObjectName(QStringLiteral("widget_tip"));
        widget_tip->setMaximumSize(QSize(16777215, 40));
        horizontalLayout_2 = new QHBoxLayout(widget_tip);
        horizontalLayout_2->setSpacing(10);
        horizontalLayout_2->setObjectName(QStringLiteral("horizontalLayout_2"));
        horizontalLayout_2->setContentsMargins(10, 0, 0, 0);
        label_tip = new QLabel(widget_tip);
        label_tip->setObjectName(QStringLiteral("label_tip"));
        label_tip->setMinimumSize(QSize(0, 0));
        QFont font;
        font.setFamily(QStringLiteral("Microsoft YaHei UI"));
        label_tip->setFont(font);
        label_tip->setStyleSheet(QStringLiteral("color:red;"));

        horizontalLayout_2->addWidget(label_tip);

        pushButton_reconnect = new QPushButton(widget_tip);
        pushButton_reconnect->setObjectName(QStringLiteral("pushButton_reconnect"));
        pushButton_reconnect->setMinimumSize(QSize(60, 24));
        pushButton_reconnect->setMaximumSize(QSize(60, 16777215));
        pushButton_reconnect->setFont(font);
        pushButton_reconnect->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#1890FF;\n"
"	border:none;\n"
"	color:#fafafa;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#1281E8;\n"
"}"));

        horizontalLayout_2->addWidget(pushButton_reconnect);

        horizontalSpacer = new QSpacerItem(537, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer);


        verticalLayout->addWidget(widget_tip);

        widget_tool = new QWidget(widget_record_box);
        widget_tool->setObjectName(QStringLiteral("widget_tool"));
        widget_tool->setEnabled(false);
        widget_tool->setMinimumSize(QSize(0, 40));
        widget_tool->setMaximumSize(QSize(16777215, 40));
        horizontalLayout_4 = new QHBoxLayout(widget_tool);
        horizontalLayout_4->setSpacing(10);
        horizontalLayout_4->setObjectName(QStringLiteral("horizontalLayout_4"));
        horizontalLayout_4->setContentsMargins(10, 0, 10, 0);
        dateTimeEdit_start = new QDateTimeEdit(widget_tool);
        dateTimeEdit_start->setObjectName(QStringLiteral("dateTimeEdit_start"));
        dateTimeEdit_start->setMinimumSize(QSize(160, 24));
        dateTimeEdit_start->setFont(font);
        dateTimeEdit_start->setStyleSheet(QLatin1String("border:1px solid #ddd;\n"
"border-radius:2px;"));
        dateTimeEdit_start->setDateTime(QDateTime(QDate(2021, 1, 1), QTime(0, 0, 0)));

        horizontalLayout_4->addWidget(dateTimeEdit_start);

        dateTimeEdit_end = new QDateTimeEdit(widget_tool);
        dateTimeEdit_end->setObjectName(QStringLiteral("dateTimeEdit_end"));
        dateTimeEdit_end->setMinimumSize(QSize(160, 24));
        dateTimeEdit_end->setFont(font);
        dateTimeEdit_end->setStyleSheet(QLatin1String("border:1px solid #ddd;\n"
"border-radius:2px;"));
        dateTimeEdit_end->setDateTime(QDateTime(QDate(2020, 1, 1), QTime(0, 0, 0)));

        horizontalLayout_4->addWidget(dateTimeEdit_end);

        pushButton_search = new QPushButton(widget_tool);
        pushButton_search->setObjectName(QStringLiteral("pushButton_search"));
        pushButton_search->setMinimumSize(QSize(60, 24));
        pushButton_search->setFont(font);
        pushButton_search->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#1890FF;\n"
"	border:none;\n"
"	color:#fafafa;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#1281E8;\n"
"}"));

        horizontalLayout_4->addWidget(pushButton_search);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer_2);

        pushButton_pause = new QPushButton(widget_tool);
        pushButton_pause->setObjectName(QStringLiteral("pushButton_pause"));
        pushButton_pause->setMinimumSize(QSize(60, 24));
        pushButton_pause->setFont(font);
        pushButton_pause->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#ccc;\n"
"	border:none;\n"
"	color:#000;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#bbb;\n"
"}"));

        horizontalLayout_4->addWidget(pushButton_pause);

        pushButton_filter = new QPushButton(widget_tool);
        pushButton_filter->setObjectName(QStringLiteral("pushButton_filter"));
        pushButton_filter->setMinimumSize(QSize(60, 24));
        pushButton_filter->setFont(font);
        pushButton_filter->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#ccc;\n"
"	border:none;\n"
"	color:#000;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#bbb;\n"
"}"));

        horizontalLayout_4->addWidget(pushButton_filter);


        verticalLayout->addWidget(widget_tool);

        widget = new QWidget(widget_record_box);
        widget->setObjectName(QStringLiteral("widget"));
        widget->setStyleSheet(QLatin1String("QWidget {\n"
"	background:#101010;\n"
"}"));
        horizontalLayout_5 = new QHBoxLayout(widget);
        horizontalLayout_5->setSpacing(0);
        horizontalLayout_5->setObjectName(QStringLiteral("horizontalLayout_5"));
        horizontalLayout_5->setContentsMargins(0, 0, 0, 0);
        tableWidget_record = new QTableWidget(widget);
        if (tableWidget_record->columnCount() < 3)
            tableWidget_record->setColumnCount(3);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        tableWidget_record->setHorizontalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        tableWidget_record->setHorizontalHeaderItem(1, __qtablewidgetitem1);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        tableWidget_record->setHorizontalHeaderItem(2, __qtablewidgetitem2);
        tableWidget_record->setObjectName(QStringLiteral("tableWidget_record"));
        tableWidget_record->setMinimumSize(QSize(260, 0));
        tableWidget_record->setMaximumSize(QSize(260, 16777215));
        tableWidget_record->setStyleSheet(QLatin1String("QTableWidget {\n"
"	background:#101010;\n"
"	border:none;\n"
"	border-left:1px solid #222;\n"
"	border-right:1px solid #222;\n"
"	margin:0 0 10px 10px;\n"
"}\n"
"QHeaderView {\n"
"    background:transparent;\n"
"}\n"
"QHeaderView::section{\n"
"	height:26px;\n"
"    color:#ccc;\n"
"    background:#111;\n"
"	border: none;\n"
"	border-right:1px solid #222;\n"
"	border-bottom:1px solid #222;\n"
"}\n"
"QTableWidget::item {\n"
"	border:none;\n"
"	color:#ccc;\n"
"	border-right:1px solid #222;\n"
"	border-bottom:1px solid #222;\n"
"}\n"
"QTableWidget::item::selected {\n"
"	color:#fff;     \n"
"	background:#1890FF;\n"
"}\n"
"QScrollBar{\n"
"	background:#222; \n"
"	width:6px; \n"
"	height:6px;\n"
"}"));
        tableWidget_record->horizontalHeader()->setDefaultSectionSize(121);

        horizontalLayout_5->addWidget(tableWidget_record);

        widget_canvas = new QWidget(widget);
        widget_canvas->setObjectName(QStringLiteral("widget_canvas"));
        widget_canvas->setStyleSheet(QStringLiteral("background:#000;"));

        horizontalLayout_5->addWidget(widget_canvas);


        verticalLayout->addWidget(widget);


        horizontalLayout->addWidget(widget_record_box);


        retranslateUi(Record);

        QMetaObject::connectSlotsByName(Record);
    } // setupUi

    void retranslateUi(QWidget *Record)
    {
        Record->setWindowTitle(QApplication::translate("Record", "\346\225\260\346\215\256\345\233\236\347\234\213", Q_NULLPTR));
        label_tip->setText(QApplication::translate("Record", "\346\255\243\345\234\250\350\277\236\346\216\245...", Q_NULLPTR));
        pushButton_reconnect->setText(QApplication::translate("Record", "\351\207\215\350\277\236", Q_NULLPTR));
        dateTimeEdit_start->setDisplayFormat(QApplication::translate("Record", "yyyy-MM-dd HH:mm:ss", Q_NULLPTR));
        dateTimeEdit_end->setDisplayFormat(QApplication::translate("Record", "yyyy-MM-dd HH:mm:ss", Q_NULLPTR));
        pushButton_search->setText(QApplication::translate("Record", "\346\237\245\350\257\242", Q_NULLPTR));
        pushButton_pause->setText(QApplication::translate("Record", "\346\232\202\345\201\234", Q_NULLPTR));
        pushButton_filter->setText(QApplication::translate("Record", "\345\205\263\351\227\255\346\273\244\346\263\242", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem = tableWidget_record->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QApplication::translate("Record", "\345\267\245\347\250\213", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem1 = tableWidget_record->horizontalHeaderItem(1);
        ___qtablewidgetitem1->setText(QApplication::translate("Record", "\344\272\213\344\273\266\346\227\266\351\227\264", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem2 = tableWidget_record->horizontalHeaderItem(2);
        ___qtablewidgetitem2->setText(QApplication::translate("Record", "\344\272\213\344\273\266\346\225\260\346\215\256", Q_NULLPTR));
    } // retranslateUi

};

namespace Ui {
    class Record: public Ui_Record {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_RECORD_H
