#include "full_store.h"
#include<QDate>
#include<QCoreApplication>
#include<QDir>
#include "data_managent.h"
#include<QMessageBox>
#include<QTextStream>
#include<QString>
full_store::full_store(int index)
{
    this->index=index;
    file = nullptr;  // 初始化文件指针
    timer1= new QTimer();

    connect(timer1,SIGNAL(timeout()), this, SLOT(createFile()));
    timer1->start(1000*60);
    createFile();
}

full_store::~full_store()
{
    QMutexLocker locker(&fileMutex);

    if(timer1) {
        timer1->stop();
        delete timer1;
        timer1 = nullptr;
    }

    if(file) {
        file->close();
        delete file;
        file = nullptr;
    }
}

void full_store::createFile()
{
    QMutexLocker locker(&fileMutex);  // 加锁保护文件操作

    QDateTime nowUtc = QDateTime::currentDateTimeUtc();
    QDate date = nowUtc.date();
   // QString baseDir = QCoreApplication::applicationDirPath() + "/Data"+ "/" ;
     QString baseDir = "D:/Data" ;   //+ "/" ;
   // QString yearDir  = baseDir +;
    QString monDir   =baseDir+"/"+ date.toString("yyyy") + date.toString("MM");
     QTime time = QTime::currentTime();

    QString subPath;    QDir dir;
    if (!dir.exists(monDir))
     {
        dir.mkpath(monDir);
       }
    dir.cd(monDir);
 subPath=monDir+"/"+date.toString("dd");
 if(!dir.exists(subPath))
 {
     dir.mkpath(subPath);
 }
 dir.cd(subPath);
     QString filePath = monDir + "/"+date.toString("dd")  + "/"+time.toString("hhmmss")+"_"+QString::number(this->index)+".txt";

     // 修复文件关闭逻辑
     if(file != nullptr)
     {
         file->close();
         delete file;
         file = nullptr;
     }
     file = new QFile(filePath);

    if (!file->open(QIODevice::WriteOnly | QIODevice::Text)) {
        delete file;
        file = nullptr;
        return;
    }
    stream.setDevice(file);
    stream.setCodec("utf-8");
}

void full_store::store_recive_data(QVector<double> data, int index, long x)
{
    QMutexLocker locker(&fileMutex);  // 加锁保护文件操作

    // 检查文件是否有效
    if(file == nullptr || !file->isOpen()) {
        qDebug() << "File not open for index:" << index;
        return;
    }

   // qDebug()<<"store_recive_data";
    stream.setDevice(file);
    stream.setCodec("UTF-8");//stream.setEncoding(QStringConverter::Utf8);

    double channeldata[channel_count][m_xPointNum];
    // 使用传入的data参数而不是全局数组recive_data
    QDateTime curDateTime = QDateTime::currentDateTimeUtc();
    //qDebug()<<data[1]<<__LINE__<<__FILE__;
    stream<<curDateTime.toString("yyyy-MM-dd hh:mm:ss.zzz");

    // 从传入的data参数中提取数据
    for(int i = 0; i < m_xPointNum; ++i)
    {
        int s = 1;  // data[0]是时间戳，从data[1]开始是实际数据
        for(int m = 0; m < channel_count; m++)
        {
            if(sensor_config_message[index]->channel_isEnable[m])
            {
                // 确保不越界访问
                if(s < data.size()) {
                    channeldata[m][i] = data[s];
                    s++;
                } else {
                    channeldata[m][i] = 0.0;  // 默认值
                }
            }
        }
    }

    for(int m = 0; m < channel_count; m++)
    {
        if(sensor_config_message[index]->channel_isEnable[m])
        {
            stream<<"No."<<index<<"card"<<"No."<<m<<"channel data:"<<"\n";
            for (int i = 0; i < m_xPointNum; ++i) {
                stream<<channeldata[m][i]<<";";
            }
        }
        stream<<"\n";
    }

    // 强制刷新到文件
    stream.flush();
    file->flush();
}
