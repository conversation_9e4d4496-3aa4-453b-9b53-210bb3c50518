#include "full_store.h"
#include<QDate>
#include<QCoreApplication>
#include<QDir>
#include "data_managent.h"
#include<QMessageBox>
#include<QTextStream>
#include<QString>
full_store::full_store(int index)
{
    this->index=index;
    timer1= new QTimer();

    connect(timer1,SIGNAL(timeout()), this, SLOT(createFile()));
    timer1->start(1000*60);
    createFile();
}

void full_store::createFile()
{
    QDateTime nowUtc = QDateTime::currentDateTimeUtc();
    QDate date = nowUtc.date();
   // QString baseDir = QCoreApplication::applicationDirPath() + "/Data"+ "/" ;
     QString baseDir = "D:/Data" ;   //+ "/" ;
   // QString yearDir  = baseDir +;
    QString monDir   =baseDir+"/"+ date.toString("yyyy") + date.toString("MM");
     QTime time = QTime::currentTime();

    QString subPath;    QDir dir;
    if (!dir.exists(monDir))
     {
        dir.mkpath(monDir);
       }
    dir.cd(monDir);
 subPath=monDir+"/"+date.toString("dd");
 if(!dir.exists(subPath))
 {
     dir.mkpath(subPath);
 }
 dir.cd(subPath);
     QString filePath = monDir + "/"+date.toString("dd")  + "/"+time.toString("hhmmss")+"_"+QString::number(this->index)+".txt";
     if(file==NULL)
     {         
         file->close();
     }     
     file=new QFile(filePath);

    if (!file->open(QIODevice::WriteOnly | QIODevice::Text)) {

        return;
    }
    stream.setDevice(file);    stream.setCodec("utf-8");
}

void full_store::store_recive_data(QVector<double> data, int index, long x)
{
   // qDebug()<<"store_recive_data";
      stream.setDevice(file);     stream.setCodec("UTF-8");//stream.setEncoding(QStringConverter::Utf8);
   double channeldata[channel_count][m_xPointNum];
   // QVector<double> adData = data.value<QVector<double>>();
    //QDateTime now = QDateTime::fromMSecsSinceEpoch(data[0] * 1000);
     QDateTime curDateTime = QDateTime::currentDateTimeUtc();
    //qDebug()<<data[1]<<__LINE__<<__FILE__;
    stream<<curDateTime.toString("yyyy-MM-dd hh:mm:ss.zzz");
     for(int i = 0;i<m_xPointNum; ++i)
       {
          int s=1;
           for(int m=0;m<channel_count;m++)
           {
               if(sensor_config_message[index]->channel_isEnable[m])
               {
                   channeldata[m][i]=recive_data[index][s];
                  s++;
               }
           }
       }
          for(int m=0;m<channel_count;m++)
          {
              if(sensor_config_message[index]->channel_isEnable[m])
              {
                  stream<<"No."<<index<<"card"<<"No."<<m<<"channel data:"<<"\n";
                  for (int i = 0;i<m_xPointNum; ++i) {
                      stream<<channeldata[m][i]<<";";
                  }
              }
              stream<<"\n";
          }

}
