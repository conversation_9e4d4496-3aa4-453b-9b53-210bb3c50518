#ifndef EVENT_THREAD_H
#define EVENT_THREAD_H

#include <QThread>
class event_thread : public QThread
{
              Q_OBJECT
public:
    int  sa_count = 50;
    int la_count = 50*4;
    double threshold=1.5;
    int event_effective=4;
    int event_count=0;
    int cur;//STA/LTA计算的点的index
    event_thread();
    void event_front_data();
        void run();
signals:
       void store();
public   slots:
     void event_deal(); // AD数据处理
     void event_back_data(int index);
};

#endif // EVENT_THREAD_H
