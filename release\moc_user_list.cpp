/****************************************************************************
** Meta object code from reading C++ file 'user_list.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.8)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../user_list.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'user_list.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.8. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_user_list_t {
    QByteArrayData data[11];
    char stringdata0[196];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_user_list_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_user_list_t qt_meta_stringdata_user_list = {
    {
QT_MOC_LITERAL(0, 0, 9), // "user_list"
QT_MOC_LITERAL(1, 10, 9), // "load_data"
QT_MOC_LITERAL(2, 20, 0), // ""
QT_MOC_LITERAL(3, 21, 9), // "queryName"
QT_MOC_LITERAL(4, 31, 25), // "on_pushButton_add_clicked"
QT_MOC_LITERAL(5, 57, 26), // "on_pushButton_edit_clicked"
QT_MOC_LITERAL(6, 84, 28), // "on_pushButton_delete_clicked"
QT_MOC_LITERAL(7, 113, 28), // "on_pushButton_search_clicked"
QT_MOC_LITERAL(8, 142, 24), // "on_tableWidget_activated"
QT_MOC_LITERAL(9, 167, 5), // "index"
QT_MOC_LITERAL(10, 173, 22) // "on_tableWidget_clicked"

    },
    "user_list\0load_data\0\0queryName\0"
    "on_pushButton_add_clicked\0"
    "on_pushButton_edit_clicked\0"
    "on_pushButton_delete_clicked\0"
    "on_pushButton_search_clicked\0"
    "on_tableWidget_activated\0index\0"
    "on_tableWidget_clicked"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_user_list[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       7,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    1,   49,    2, 0x08 /* Private */,
       4,    0,   52,    2, 0x08 /* Private */,
       5,    0,   53,    2, 0x08 /* Private */,
       6,    0,   54,    2, 0x08 /* Private */,
       7,    0,   55,    2, 0x08 /* Private */,
       8,    1,   56,    2, 0x08 /* Private */,
      10,    1,   59,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QModelIndex,    9,
    QMetaType::Void, QMetaType::QModelIndex,    9,

       0        // eod
};

void user_list::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        user_list *_t = static_cast<user_list *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->load_data((*reinterpret_cast< QString(*)>(_a[1]))); break;
        case 1: _t->on_pushButton_add_clicked(); break;
        case 2: _t->on_pushButton_edit_clicked(); break;
        case 3: _t->on_pushButton_delete_clicked(); break;
        case 4: _t->on_pushButton_search_clicked(); break;
        case 5: _t->on_tableWidget_activated((*reinterpret_cast< const QModelIndex(*)>(_a[1]))); break;
        case 6: _t->on_tableWidget_clicked((*reinterpret_cast< const QModelIndex(*)>(_a[1]))); break;
        default: ;
        }
    }
}

const QMetaObject user_list::staticMetaObject = {
    { &QWidget::staticMetaObject, qt_meta_stringdata_user_list.data,
      qt_meta_data_user_list,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *user_list::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *user_list::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_user_list.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int user_list::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 7;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
