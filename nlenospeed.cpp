#include "nlenospeed.h"
#include <iostream>
#include <vector>
#include <cmath>
#include <utility>

using namespace std;

pair<bool, vector<double>> NLENoSpeed::GetRootsetGrad(int n, vector<double> x, int nMaxIt, double eps)
{
    int l = nMaxIt;
    vector<double> y(n, 0.0);
    FuncResult result = func(x, y);
    double z_val = result.z;

    while (z_val >= eps)
    {
        if (--l == 0)
        {
            return make_pair(true, x);
        }

        double d = 0.0;
        for (size_t i = 0; i < result.y.size(); ++i)
        {
            d += result.y[i] * result.y[i];
        }

        if (d < 1e-10)
        {
            return make_pair(false, vector<double>());
        }

        double s = z_val / d;
        for (int i = 0; i < n; ++i)
        {
            x[i] -= s * result.y[i];
        }

        result = func(x, y);
        z_val = result.z;
    }
    return make_pair(nMaxIt > l, x);
}

FuncResult NLENoSpeed::func(const vector<double>& x, const vector<double>& y)
{
    double z = 0.0;
    int chanelNum = xZuoBiao.size();
    vector<double> f(chanelNum, 0.0);

    for (int i = 0; i < chanelNum; ++i)
    {
        double dx = xZuoBiao[i] - x[0];
        double dy = yZuoBiao[i] - x[1];
        double dz = zZuoBiao[i] - x[2];
        double dt = (timeArray[i] - x[3]);
        f[i] = dx*dx + dy*dy + dz*dz - pow(x[4] * dt, 2);
        z += f[i] * f[i];
    }

    double a = 0.0, b = 0.0, c = 0.0, d = 0.0, e = 0.0;
    for (int i = 0; i < chanelNum; ++i)
    {
        double df1 = 2 * (x[0] - xZuoBiao[i]);
        a += f[i] * df1;

        double df2 = 2 * (x[1] - yZuoBiao[i]);
        b += f[i] * df2;

        double df3 = 2 * (x[2] - zZuoBiao[i]);
        c += f[i] * df3;

        double df4 = 2 * x[4] * x[4] * (timeArray[i] - x[3]);
        d += f[i] * df4;

        double df5 = -2 * pow(timeArray[i] - x[3], 2) * x[4];
        e += f[i] * df5;
    }

    vector<double> new_y = y;
    new_y[0] = 2.0 * a;
    new_y[1] = 2.0 * b;
    new_y[2] = 2.0 * c;
    new_y[3] = 2.0 * d;
    new_y[4] = 2.0 * e;

    return FuncResult(x, new_y, z);
}
