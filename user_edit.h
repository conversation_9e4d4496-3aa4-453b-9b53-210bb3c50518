#ifndef USER_EDIT_H
#define USER_EDIT_H

#include <QWidget>

namespace Ui {
class user_edit;
}

class user_edit : public QWidget
{
    Q_OBJECT

public:
    explicit user_edit(QWidget *parent = nullptr);
    ~user_edit();

signals:
    void reload(QString q);

private slots:
    void on_pushButton_save_clicked();

    void on_radioButton_enable_clicked();

    void on_radioButton_disable_clicked();

private:
    Ui::user_edit *ui;

};

#endif // USER_EDIT_H
