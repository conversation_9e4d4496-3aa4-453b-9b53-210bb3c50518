/********************************************************************************
** Form generated from reading UI file 'project_list.ui'
**
** Created by: Qt User Interface Compiler version 5.9.8
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_PROJECT_LIST_H
#define UI_PROJECT_LIST_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_Project_list
{
public:
    QHBoxLayout *horizontalLayout_3;
    QWidget *widget_main;
    QVBoxLayout *verticalLayout;
    QWidget *widget_button_box;
    QHBoxLayout *horizontalLayout_2;
    QPushButton *pushButton_reload;
    QSpacerItem *horizontalSpacer;
    QLineEdit *lineEdit_name;
    QPushButton *pushButton_search;
    QWidget *widget_3;
    QHBoxLayout *horizontalLayout;
    QTableWidget *tableWidget;

    void setupUi(QWidget *Project_list)
    {
        if (Project_list->objectName().isEmpty())
            Project_list->setObjectName(QStringLiteral("Project_list"));
        Project_list->resize(900, 500);
        horizontalLayout_3 = new QHBoxLayout(Project_list);
        horizontalLayout_3->setObjectName(QStringLiteral("horizontalLayout_3"));
        widget_main = new QWidget(Project_list);
        widget_main->setObjectName(QStringLiteral("widget_main"));
        widget_main->setStyleSheet(QStringLiteral("background:#fff;"));
        verticalLayout = new QVBoxLayout(widget_main);
        verticalLayout->setSpacing(10);
        verticalLayout->setObjectName(QStringLiteral("verticalLayout"));
        verticalLayout->setContentsMargins(10, 10, 10, 10);
        widget_button_box = new QWidget(widget_main);
        widget_button_box->setObjectName(QStringLiteral("widget_button_box"));
        widget_button_box->setMinimumSize(QSize(0, 30));
        horizontalLayout_2 = new QHBoxLayout(widget_button_box);
        horizontalLayout_2->setSpacing(10);
        horizontalLayout_2->setObjectName(QStringLiteral("horizontalLayout_2"));
        horizontalLayout_2->setContentsMargins(0, 0, 0, 0);
        pushButton_reload = new QPushButton(widget_button_box);
        pushButton_reload->setObjectName(QStringLiteral("pushButton_reload"));
        pushButton_reload->setMinimumSize(QSize(60, 24));
        QFont font;
        font.setFamily(QStringLiteral("Microsoft YaHei UI"));
        pushButton_reload->setFont(font);
        pushButton_reload->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#ccc;\n"
"	border:none;\n"
"	color:#000;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#bbb;\n"
"}"));

        horizontalLayout_2->addWidget(pushButton_reload);

        horizontalSpacer = new QSpacerItem(94, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer);

        lineEdit_name = new QLineEdit(widget_button_box);
        lineEdit_name->setObjectName(QStringLiteral("lineEdit_name"));
        lineEdit_name->setMinimumSize(QSize(240, 26));
        lineEdit_name->setMaximumSize(QSize(240, 16777215));
        lineEdit_name->setFont(font);
        lineEdit_name->setStyleSheet(QLatin1String("background:none;\n"
"border:1px solid #bbb;\n"
"border-radius:2px;\n"
"color:#333;"));

        horizontalLayout_2->addWidget(lineEdit_name);

        pushButton_search = new QPushButton(widget_button_box);
        pushButton_search->setObjectName(QStringLiteral("pushButton_search"));
        pushButton_search->setMinimumSize(QSize(60, 24));
        pushButton_search->setFont(font);
        pushButton_search->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#1890FF;\n"
"	border:none;\n"
"	color:#fafafa;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#1281E8;\n"
"}"));

        horizontalLayout_2->addWidget(pushButton_search);


        verticalLayout->addWidget(widget_button_box);

        widget_3 = new QWidget(widget_main);
        widget_3->setObjectName(QStringLiteral("widget_3"));
        horizontalLayout = new QHBoxLayout(widget_3);
        horizontalLayout->setSpacing(0);
        horizontalLayout->setObjectName(QStringLiteral("horizontalLayout"));
        horizontalLayout->setContentsMargins(0, 0, 0, 0);
        tableWidget = new QTableWidget(widget_3);
        if (tableWidget->columnCount() < 7)
            tableWidget->setColumnCount(7);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        __qtablewidgetitem->setFont(font);
        tableWidget->setHorizontalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        __qtablewidgetitem1->setFont(font);
        tableWidget->setHorizontalHeaderItem(1, __qtablewidgetitem1);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        __qtablewidgetitem2->setFont(font);
        tableWidget->setHorizontalHeaderItem(2, __qtablewidgetitem2);
        QTableWidgetItem *__qtablewidgetitem3 = new QTableWidgetItem();
        __qtablewidgetitem3->setFont(font);
        tableWidget->setHorizontalHeaderItem(3, __qtablewidgetitem3);
        QTableWidgetItem *__qtablewidgetitem4 = new QTableWidgetItem();
        __qtablewidgetitem4->setFont(font);
        tableWidget->setHorizontalHeaderItem(4, __qtablewidgetitem4);
        QTableWidgetItem *__qtablewidgetitem5 = new QTableWidgetItem();
        __qtablewidgetitem5->setFont(font);
        tableWidget->setHorizontalHeaderItem(5, __qtablewidgetitem5);
        QTableWidgetItem *__qtablewidgetitem6 = new QTableWidgetItem();
        tableWidget->setHorizontalHeaderItem(6, __qtablewidgetitem6);
        tableWidget->setObjectName(QStringLiteral("tableWidget"));
        tableWidget->setAcceptDrops(false);
        tableWidget->setLayoutDirection(Qt::LeftToRight);
        tableWidget->setAutoFillBackground(true);
        tableWidget->setStyleSheet(QLatin1String("QTableWidget {\n"
"	border:1px solid #ddd;\n"
"}\n"
"QHeaderView {\n"
"    background:transparent;\n"
"}\n"
"QHeaderView::section{\n"
"	height:26px;\n"
"    color:#111;\n"
"    background:#eee;\n"
"	border: none;\n"
"	border-right:1px solid #ddd;\n"
"	border-bottom:1px solid #ddd;\n"
"}\n"
"QTableWidget::item {\n"
"	border:none;\n"
"	border-bottom:1px solid #EEF1F7;\n"
"}\n"
"QTableWidget::item::selected {\n"
"	color:#fff;     \n"
"	background:#1890FF;\n"
"}\n"
"QScrollBar{background:#d0d2d4; width:6px; height:6px;}"));
        tableWidget->setTextElideMode(Qt::ElideLeft);
        tableWidget->setVerticalScrollMode(QAbstractItemView::ScrollPerItem);
        tableWidget->horizontalHeader()->setCascadingSectionResizes(false);
        tableWidget->horizontalHeader()->setDefaultSectionSize(146);
        tableWidget->horizontalHeader()->setMinimumSectionSize(0);
        tableWidget->horizontalHeader()->setProperty("showSortIndicator", QVariant(false));
        tableWidget->horizontalHeader()->setStretchLastSection(true);
        tableWidget->verticalHeader()->setDefaultSectionSize(30);
        tableWidget->verticalHeader()->setMinimumSectionSize(25);
        tableWidget->verticalHeader()->setStretchLastSection(false);

        horizontalLayout->addWidget(tableWidget);


        verticalLayout->addWidget(widget_3);


        horizontalLayout_3->addWidget(widget_main);


        retranslateUi(Project_list);

        QMetaObject::connectSlotsByName(Project_list);
    } // setupUi

    void retranslateUi(QWidget *Project_list)
    {
        Project_list->setWindowTitle(QApplication::translate("Project_list", "\345\267\245\347\250\213\345\210\227\350\241\250", Q_NULLPTR));
        pushButton_reload->setText(QApplication::translate("Project_list", "\345\210\267\346\226\260", Q_NULLPTR));
        lineEdit_name->setText(QString());
        lineEdit_name->setPlaceholderText(QApplication::translate("Project_list", "\350\257\267\350\276\223\345\205\245\345\267\245\347\250\213\345\220\215\347\247\260", Q_NULLPTR));
        pushButton_search->setText(QApplication::translate("Project_list", "\346\237\245\350\257\242", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem = tableWidget->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QApplication::translate("Project_list", "ID", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem1 = tableWidget->horizontalHeaderItem(1);
        ___qtablewidgetitem1->setText(QApplication::translate("Project_list", "\345\220\215\347\247\260", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem2 = tableWidget->horizontalHeaderItem(2);
        ___qtablewidgetitem2->setText(QApplication::translate("Project_list", "\346\226\207\344\273\266\347\233\256\345\275\225", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem3 = tableWidget->horizontalHeaderItem(3);
        ___qtablewidgetitem3->setText(QApplication::translate("Project_list", "\347\233\221\345\210\266\345\215\225\344\275\215", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem4 = tableWidget->horizontalHeaderItem(4);
        ___qtablewidgetitem4->setText(QApplication::translate("Project_list", "\347\212\266\346\200\201", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem5 = tableWidget->horizontalHeaderItem(5);
        ___qtablewidgetitem5->setText(QApplication::translate("Project_list", "\345\210\233\345\273\272\346\227\266\351\227\264", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem6 = tableWidget->horizontalHeaderItem(6);
        ___qtablewidgetitem6->setText(QApplication::translate("Project_list", "\345\210\240\351\231\244", Q_NULLPTR));
    } // retranslateUi

};

namespace Ui {
    class Project_list: public Ui_Project_list {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_PROJECT_LIST_H
