#include "setting.h"
#include "ui_setting.h"
#include "message.h"

#include <QTableWidget>
#include <QLineEdit>
#include <QComboBox>
#include <QCheckBox>
#include <QDoubleSpinBox>
#include <QSpinBox>

//extern sensor_config* sensor_config_message[3];

Setting::Setting(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::Setting)
{
    ui->setupUi(this);

    this->ui->tableWidget_device_config->setColumnWidth(0,100);
    this->ui->tableWidget_device_config->setColumnWidth(1,100);
    this->ui->tableWidget_device_config->setColumnWidth(2,100);
    this->ui->tableWidget_device_config->setColumnWidth(3,200);
    this->ui->tableWidget_device_config->setColumnWidth(4,300);
    this->ui->tableWidget_device_config->setColumnWidth(5,200);
    this->ui->tableWidget_device_config->setColumnWidth(6,200);
    this->ui->tableWidget_device_config->setColumnWidth(7,200);
    this->ui->tableWidget_device_config->setColumnWidth(8,200);
    this->ui->tableWidget_device_config->setColumnWidth(9,300);
    this->ui->tableWidget_device_config->setColumnWidth(10,200);

    this->ui->tableWidget_device_config->setRowCount(16);

    connect(this->ui->pushButton_save, &QPushButton::clicked, [this](){
        this->on_PushButton_save_clicked(this->ui->comboBox_cardID->currentText().toInt());
    });

    connect(this->ui->comboBox_cardID, &QComboBox::currentTextChanged, [this](){
        this->tableWidget_device_config_init(this->ui->comboBox_cardID->currentText().toInt());
    });

    this->tableWidget_device_config_init(0);

}

void Setting::on_PushButton_save_clicked(int card_id)
{

}

void Setting::tableWidget_device_config_init(int card_id)
{

}


Setting::~Setting()
{
    delete ui;
}

void Setting::on_tableWidget_device_config_doubleClicked(const QModelIndex &index)
{

}
