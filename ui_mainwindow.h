/********************************************************************************
** Form generated from reading UI file 'mainwindow.ui'
**
** Created by: Qt User Interface Compiler version 5.9.8
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>
#include "qcustomplot.h"

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QWidget *centralWidget;
    QVBoxLayout *verticalLayout;
    QCustomPlot *timeDomainPlot;
    QCustomPlot *freqDomainPlot;
    QCustomPlot *ifftPlot;
    QHBoxLayout *horizontalLayout;
    QPushButton *generateButton;
    QPushButton *fftButton;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName(QStringLiteral("MainWindow"));
        MainWindow->resize(1000, 800);
        centralWidget = new QWidget(MainWindow);
        centralWidget->setObjectName(QStringLiteral("centralWidget"));
        verticalLayout = new QVBoxLayout(centralWidget);
        verticalLayout->setObjectName(QStringLiteral("verticalLayout"));
        timeDomainPlot = new QCustomPlot(centralWidget);
        timeDomainPlot->setObjectName(QStringLiteral("timeDomainPlot"));

        verticalLayout->addWidget(timeDomainPlot);

        freqDomainPlot = new QCustomPlot(centralWidget);
        freqDomainPlot->setObjectName(QStringLiteral("freqDomainPlot"));

        verticalLayout->addWidget(freqDomainPlot);

        ifftPlot = new QCustomPlot(centralWidget);
        ifftPlot->setObjectName(QStringLiteral("ifftPlot"));

        verticalLayout->addWidget(ifftPlot);

        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QStringLiteral("horizontalLayout"));
        generateButton = new QPushButton(centralWidget);
        generateButton->setObjectName(QStringLiteral("generateButton"));

        horizontalLayout->addWidget(generateButton);

        fftButton = new QPushButton(centralWidget);
        fftButton->setObjectName(QStringLiteral("fftButton"));

        horizontalLayout->addWidget(fftButton);


        verticalLayout->addLayout(horizontalLayout);

        MainWindow->setCentralWidget(centralWidget);

        retranslateUi(MainWindow);

        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QApplication::translate("MainWindow", "FFT\345\217\230\346\215\242\345\217\257\350\247\206\345\214\226\345\267\245\345\205\267", Q_NULLPTR));
        generateButton->setText(QApplication::translate("MainWindow", "\347\224\237\346\210\220\346\265\213\350\257\225\344\277\241\345\217\267", Q_NULLPTR));
        fftButton->setText(QApplication::translate("MainWindow", "\346\211\247\350\241\214FFT", Q_NULLPTR));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
