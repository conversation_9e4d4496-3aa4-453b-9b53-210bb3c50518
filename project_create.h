#ifndef PROJECT_CREATE_H
#define PROJECT_CREATE_H

#include "message.h"
#include <QWidget>

namespace Ui {
class project_create;
}

class project_create : public QWidget
{
    Q_OBJECT

public:
    explicit project_create(QWidget *parent = nullptr);
    ~project_create();

    void tableWidget_sensor_parameter_init();

private slots:
    void on_pushButton_sensor_setting_clicked();

    void on_pushButton_path_clicked();

    void on_pushButton_save_clicked();

    void on_pushButton_cancel_clicked();

    void on_pushButton_reload_clicked();




    void on_lineEdit_divice_number_textChanged(const QString &arg1);

    void on_lineEdit_device_IP_textChanged(const QString &arg1);

    void on_comboBox_device_choose_currentTextChanged(const QString &arg1);

private:
    Ui::project_create *ui;
     QVector<sensor_config*> sensor_config_temp;
};

#endif // PROJECT_CREATE_H
