#ifndef USER_MAIN_H
#define USER_MAIN_H

#include "user_edit.h"
#include "user_list.h"
#include "reset_password.h"
#include <QWidget>

namespace Ui {
class User_main;
}

class User_main : public QWidget
{
    Q_OBJECT

public:
    explicit User_main(QWidget *parent = nullptr);
    ~User_main();

signals:
    void loginout();

private slots:
    void on_toolButton_user_list_clicked();

    void on_toolButton_reset_clicked();

    void on_pushButton_logout_clicked();

private:
    Ui::User_main *ui;

    user_list* user_list_ui;
    user_edit* user_edit_ui;
    reset_password* reset_password_ui;

};

#endif // USER_MAIN_H
