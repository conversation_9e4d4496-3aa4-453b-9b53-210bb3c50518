<?xml version="1.0"?>
<doc>
    <assembly>
        <name>net2418cDotNet</name>
    </assembly>
    <members>
        <member name="T:XCDotNETAPI.net2418CAPI">
            <summary>
            net2418C C# 的函数调用接口
            </summary>
        </member>
        <member name="T:XCDotNETAPI.net2418CAPI.OrderCode">
            <summary>
            指令代码
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_ioPUT_CONFIG">
            <summary>
            配置卡参数
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_ioGET_CONFIG">
            <summary>
            获取卡参数
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_ioGET_COUNTER">
            <summary>
            获取计数值
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_ioPUT_COUNTER_DISABLE">
            <summary>
            计数器停止
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_ioPUT_COUNTER_ENABLE">
            <summary>
            计数器启动
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_ioGET_COUNTER_FREQ">
            <summary>
            获取测频结果
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_ioPUT_COUNTER_RESET">
            <summary>
            重新初始化计数器初值，注意初值不可为0
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_ioPUT_FREQ_CYC">
            <summary>
            测频周期
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_ioSTART_STREAM">
            <summary>
            启动AD流
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_ioSTOP_STREAM">
            <summary>
            停止AD流
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_ioGET_STREAM_DATA">
            <summary>
            获取AD流数据
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_ioGET_STREAM_LENGTH">
            <summary>
            获取AD流长度
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_ioPUT_STREAM_BUFFER">
            <summary>
            配置上位机为AD流开辟缓冲区大小，默认50M
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_ioSTART_STREAM_TRANS">
            <summary>
            启动AD流传输，需在XC_ioSTART_STREAM后使用
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_ioSTOP_STREAM_TRANS">
            <summary>
            停止AD流传输，需在XC_ioSTART_STREAM后使用
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_ioGET_MEM">
            <summary>
            硬件512字节存储空间读
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_ioSET_MEM">
            <summary>
            硬件512字节存储空间写
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_ctETHERNET_LOCALIP">
            <summary>
            设置选择本地IP
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_chDEVICE_IP">
            <summary>
            设备IP
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_chDEVICE_NETMASK">
            <summary>
            设备子网掩码
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_chDEVICE_GATE">
            <summary>
            设备网关
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_chDEVICE_MAC">
            <summary>
            设备MAC
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_chDEVICE_DHCP">
            <summary>
            设备DHCP
            </summary>	
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_chHARDWARE_VERSION">
            <summary>
            设备硬件版本号
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_chSERIAL_NUMBER">
            <summary>
            序列号
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_chPORT_NUMBER">
            <summary>
            主机端口号
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_chTCP_STATUS">
            <summary>
            tcp状态
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_chFIRMWARE_VERSION">
            <summary>
            固件版本(FPGA)
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_chPRODUCTID">
            <summary>
            产品ID 2418
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_chDEVICE_EFFECT">
            <summary>
            网络配置生效指令
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_ioPUT_AIN_RANGE">
            <summary>
            配置AD增益
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_chSTREAM_COMMUNICATION_TIMEOUT">
            <summary>
            设置AD等待通信完成的时长(ms)
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_chSTREAM_SCAN_FREQUENCY">
            <summary>
            采样率频率，单位 KHz
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_chSTREAM_SCAN_FREQUENCY_DIVID">
            <summary>
            配置采样率分频系数，范围1~255.999
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_chSTREAM_SCAN_FREQUENCY_MULTIPLIER">
            <summary>
            配置采样率倍频系数，范围1~255.999
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_chSTREAM_Length">
            <summary>
            配置定长采集点数
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_chSTREAM_Channel">
            <summary>
            配置所有通道选择情况
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_chSTREAM_BACKLOG_COMM">
            <summary>
            获取fifo状态
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_ioPUT_ConstantVolSource">
            <summary>
            配置恒流源及所有通道交直流
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_chSTREAM_TRIG_MODE">
            <summary>
            配置触发方式
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_chSTREAM_TRIG_Pre">
            <summary>
            配置AD流预采集点数
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_chSTREAM_CLOCK_MODE">
            <summary>
            配置时钟方式
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_ioSD_MODE">
            <summary>
            配置及获取SD卡工作状态
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_ioMEM_Double">
            <summary>
            通过double指针读写存储空间
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_ioMEM_Long">
            <summary>
            通过long指针读写存储空间
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_TRIG_Soft">
            <summary>
            软触发
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_TRIG_Positive">
            <summary>
            上升沿触发
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_TRIG_Negative">
            <summary>
            下降沿触发
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_TRIG_Double">
            <summary>
            双边沿触发
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_CVS">
            <summary>
            开启恒流源，交流
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_DC">
            <summary>
            关闭恒流源，直流
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.OrderCode.XC_AC">
            <summary>
            关闭恒流源，交流
            </summary>
        </member>
        <member name="T:XCDotNETAPI.net2418CAPI.ErrorCode">
            <summary>
            错误代码
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.ErrorCode.DAQE_NOERROR">
            <summary>
            无错误
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.ErrorCode.DAQE_REQUEST_NOT_PROCESSED">
            <summary>
            请求没有处理
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.ErrorCode.ERROR_NO_MORE_DATA_AVAILABLE">
            <summary>
            全部结果均已返回
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.ErrorCode.DAQE_SCRATCH_ERROR">
            <summary>
            未查询到GetResult所需结果
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.ErrorCode.DAQE_DATA_BUFFER_OVERFLOW">
            <summary>
            数据缓冲区溢出
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.ErrorCode.DAQE_DAC_CONFIG_ERROR">
            <summary>
            DA参数输入错误
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.ErrorCode.DAQE_DAC_ERROR">
            <summary>
            DA参数设置错误
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.ErrorCode.DAQE_SOCKET_ERROR">
            <summary>
            Socket创建错误失败
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.ErrorCode.DAQE_SHT_SERIAL_RESET">
            <summary>
            设备重置失败
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.ErrorCode.DAQE_STREAM_TIMEOUT">
            <summary>
            流等待超时
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.ErrorCode.DAQE_UNKNOWN_ERROR">
            <summary>
            未知错误
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.ErrorCode.DAQE_INVALID_HANDLE">
            <summary>
            无效句柄
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.ErrorCode.DAQE_DEVICE_NOT_OPEN">
            <summary>
            设备未打开
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.ErrorCode.DAQE_DAQDEVICE_NOT_FOUND">
            <summary>
            未找到设备
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.ErrorCode.DAQE_COMM_FAILURE">
            <summary>
            通讯错误
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.ErrorCode.DAQE_COMM_TIMEOUT">
            <summary>
            通讯超时
            </summary>
        </member>
        <member name="F:XCDotNETAPI.net2418CAPI.ErrorCode.DAQE_INVALID_CONNECTION_TYPE">
            <summary>
            当前模式无此功能 
            </summary>
        </member>
        <member name="M:XCDotNETAPI.net2418CAPI.OpenDAQDevice(System.Byte[],System.Int32,System.Int32@)">
            <summary>
            打开指定物理设备
            </summary>
            <param name="pAddress">卡IP地址，举例************。注意这是一个字符串。</param>
            <param name="FirstFound">如果为true，则忽略Address并找到第一个可用的net2418设备。</param> 
            <param name="pHandle">此设备上用于后续函数的句柄，如果失败，则为NULL</param>
            <returns>如果打开成功，返回0，pHandle返回打开设备的句柄；如果打开失败，返回错误代码。</returns>
        </member>
        <member name="M:XCDotNETAPI.net2418CAPI.eControlADAccess(System.Int32,System.Int32,System.Int32,System.Int32@)">
            <summary>
            AD数据传输通路控制
            </summary>
            <param name="Handle">OpenDAQDevice返回的设备句柄</param>
            <param name="ADControl">传输通路控制，0-关闭，1-开启</param> 
            <param name="AType">通路类型，0-TCP，1-UDP</param> 
            <param name="portNO">指针，指向上位机端口号，0-随机端口，并返回实际使用端口号，其他数-指定端口</param> 
            <returns>如果调用成功，返回0；如果调用失败，返回错误代码。</returns>
        </member>
        <member name="M:XCDotNETAPI.net2418CAPI.CloseDAQDevice(System.Int32)">
            <summary>
            关闭指定设备
            </summary>
            <param name="Handle">OpenDAQDevice返回的设备句柄</param>
            <returns>如果调用成功，返回0；如果调用失败，返回错误代码。</returns>
        </member>
        <member name="M:XCDotNETAPI.net2418CAPI.AddRequest(System.Int32,System.Int32,System.Int32,System.Double,System.Int64,System.Double)">
            <summary>
            添加操作请求
            </summary>
            <param name="Handle">OpenDAQDevice返回的设备句柄</param>
            <param name="IOType">操作请求的类型（见上文定义）</param>
            <param name="Channel">IOType操作通道</param>
            <param name="Value">为输出通道传递的值</param>
            <param name="x1">某些IOType使用的可选参数</param>
            <param name="UserData">只随请求一起传递的数据</param>
            <returns>如果调用成功，返回0；如果调用失败，返回错误代码。</returns>
        </member>
        <member name="M:XCDotNETAPI.net2418CAPI.Go">
            <summary>
            运行所有已被添加的操作请求
            </summary>
            <returns>如果调用成功，返回0；如果调用失败，返回错误代码。</returns>
        </member>
        <member name="M:XCDotNETAPI.net2418CAPI.eGet(System.Int32,System.Int32,System.Int32,System.Double@,System.Int32@)">
            <summary>
            获取函数
            直接调用则将执行AddRequest、GoOne和GetResult三个函数
            </summary>
            <param name="Handle">OpenDAQDevice返回的设备句柄</param>
            <param name="IOType">获取操作请求的类型</param>
            <param name="Channel">IOType操作通道</param>
            <param name="pValue">指向接收数据值的指针</param>
            <param name="px1">指向某些IOType使用的可选参数的指针</param>
            <returns>如果调用成功，返回0；如果调用失败，返回错误代码。</returns>
        </member>
        <member name="M:XCDotNETAPI.net2418CAPI.ePut(System.Int32,System.Int32,System.Int32,System.Double@,System.Int32@)">
            <summary>
            配置函数
            直接调用则将执行AddRequest、GoOne和GetResult三个函数
            </summary>
            <param name="Handle">OpenDAQDevice返回的设备句柄</param>
            <param name="IOType">配置操作请求的类型</param>
            <param name="Channel">IOType操作通道</param>
            <param name="pValue">指向配置数据值的指针</param>
            <param name="px1">指向某些IOType使用的可选参数的指针</param>
            <returns>如果调用成功，返回0；如果调用失败，返回错误代码。</returns>
        </member>
        <member name="M:XCDotNETAPI.net2418CAPI.GetResult(System.Int32,System.Int32,System.Int32,System.Double@)">
            <summary>
            获取操作请求结果
            </summary>
            <param name="Handle">OpenDAQDevice返回的设备句柄</param>
            <param name="IOType">操作请求的类型</param>
            <param name="Channel">IOType操作通道</param>
            <param name="pValue">指向接收数据值的指针</param>
            <returns>如果调用成功，返回0；如果调用失败，返回错误代码。</returns>
        </member>
        <member name="M:XCDotNETAPI.net2418CAPI.eDIOReadBack(System.Int32,System.Int32@,System.Int32@)">
            <summary>
            DIO通道状态回读
            </summary>
            <param name="Handle">OpenDAQDevice返回的设备句柄</param>
            <param name="DIOState">获取通道属性，0--DO，1--DI。</param>
            <param name="State">获取通道状态，0=假=低，1=真=高。</param>
            <returns>如果调用成功，返回0；如果调用失败，返回错误代码。</returns>
        </member>
        <member name="M:XCDotNETAPI.net2418CAPI.eDI(System.Int32,System.Int32,System.Int32@)">
            <summary>
            DI
            </summary>
            <param name="Handle">OpenDAQDevice返回的设备句柄</param>
            <param name="Channel">DI通道</param>
            <param name="State">获取DI输入的状态。0=假=低，1=真=高。</param>
            <returns>如果调用成功，返回0；如果调用失败，返回错误代码。</returns>
        </member>
        <member name="M:XCDotNETAPI.net2418CAPI.eDIs(System.Int32,System.Int32@)">
            <summary>
            获取多通道DI
            </summary>
            <param name="Handle">OpenDAQDevice返回的设备句柄</param>
            <param name="pState">指向DI数组的指针，各元素对应各通道。
            [in]各元素指代各通道属性 1=读取此通道DI输入状态，其他=此通道原DIO属性及高低状态配置不变
            [out]DI属性通道返回当前输入状态。0=假=低，1=真=高。</param>
            <returns>如果调用成功，返回0；如果调用失败，返回错误代码。</returns>
        </member>
        <member name="M:XCDotNETAPI.net2418CAPI.eDO(System.Int32,System.Int32,System.Int32)">
            <summary>
            DO
            </summary>
            <param name="Handle">OpenDAQDevice返回的设备句柄</param>
            <param name="Channel">DO通道</param>
            <param name="State">配置DO输出的状态。0=假=低，1=真=高。</param>
            <returns>如果调用成功，返回0；如果调用失败，返回错误代码。</returns>
        </member>
        <member name="M:XCDotNETAPI.net2418CAPI.eDOs(System.Int32,System.Int32@)">
            <summary>
            配置多通道DO
            </summary>
            <param name="Handle">OpenDAQDevice返回的设备句柄</param>
            <param name="pState">指向DO数组的指针，各元素对应各通道。
            [in]各元素指代各通道属性 0=假=低，1=真=高，255=此通道原DIO属性及高低状态配置不变
            </param>
            <returns>如果调用成功，返回0；如果调用失败，返回错误代码。</returns>
        </member>
        <member name="M:XCDotNETAPI.net2418CAPI.ResetDAQDevice(System.Int32,System.Int32)">
            <summary>
            设备重置
            </summary>
            <param name="Handle">OpenDAQDevice返回的设备句柄</param>
            <param name="Mode">配置重置类型。0=功能重置，1=网络配置恢复出厂配置</param>
            <returns>如果调用成功，返回0；如果调用失败，返回错误代码。</returns>
        </member>
        <member name="M:XCDotNETAPI.net2418CAPI.ErrorToString(System.Int32,System.Byte[])">
            <summary>
            根据错误代码获取字符串格式的含义
            </summary>
            <param name="ErrorCode">错误代码</param>
            <param name="pString">返回错误代码所代表的含义,应指向长度不小于256的缓存区</param>
        </member>
        <member name="M:XCDotNETAPI.net2418CAPI.GetDriverVersion">
            <summary>
            获取dll版本
            </summary>
            <returns>返回dll版本</returns>
        </member>
        <member name="M:XCDotNETAPI.net2418CAPI.ListAll(System.Int32@,System.Int32[],System.Double[])">
            <summary>
            罗列当前可用设备
            按照找到设备的顺序，设备拨码ID和设备IP分别填在数组的相应元素上
            </summary>
            <param name="pNumFound">指针，返回设备数量</param>
            <param name="pIDs">指针，指向序列号数组，数组大小最大为当前网络环境可找寻到的卡数</param>
            <param name="pAddresses">指针，指向拨码设备IP数组，数组大小最大为当前网络环境可找寻到的卡数</param>
            <returns>如果调用成功，返回0；如果调用失败，返回错误代码。</returns>
        </member>
    </members>
</doc>
