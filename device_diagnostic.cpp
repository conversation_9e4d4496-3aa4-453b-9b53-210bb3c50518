#include <QCoreApplication>
#include <QDebug>
#include <QTimer>
#include "NET2418.h"
#include "common.h"
#include "message.h"

// 设备诊断工具
class DeviceDiagnostic
{
public:
    static void checkDeviceConnections()
    {
        qDebug() << "=== 设备连接诊断开始 ===";
        
        // 1. 列出所有可用设备
        long numFound = 0;
        long deviceIDs[10];
        double deviceAddresses[10];
        
        int result = ListAll(&numFound, deviceIDs, deviceAddresses);
        if(result != 0) {
            qDebug() << "ListAll failed with error code:" << result;
            return;
        }
        
        qDebug() << "Found" << numFound << "devices:";
        for(int i = 0; i < numFound; i++) {
            qDebug() << "Device" << i << "- ID:" << deviceIDs[i] << "Address:" << deviceAddresses[i];
        }
        
        // 2. 检查配置的设备IP
        qDebug() << "\n=== 检查配置的设备 ===";
        for(int i = 0; i < sensor_config_message.size() && i < 3; i++) {
            if(sensor_config_message[i]) {
                qDebug() << "Device" << i << "configured IP:" << sensor_config_message[i]->IP;
                testDeviceConnection(sensor_config_message[i]->IP, i);
            }
        }
        
        qDebug() << "=== 设备连接诊断结束 ===\n";
    }
    
    static void testDeviceConnection(const QString& ip, int index)
    {
        qDebug() << "Testing connection to device" << index << "at" << ip;
        
        MyHANDLE testHandle = nullptr;
        int result = OpenDAQDevice(ip.toStdString().c_str(), false, &testHandle);
        
        if(result != 0 || testHandle == nullptr) {
            qDebug() << "  ❌ Failed to connect - Error code:" << result;
            
            // 获取错误描述
            char errorString[256];
            ErrorToString(result, errorString);
            qDebug() << "  Error description:" << QString::fromLocal8Bit(errorString);
        } else {
            qDebug() << "  ✅ Connected successfully - Handle:" << testHandle;
            
            // 测试基本通信
            double version = 0;
            long x1 = 0;
            result = eGet(testHandle, XC_ioGET_CONFIG, XC_chHARDWARE_VERSION, &version, &x1);
            if(result == 0) {
                qDebug() << "  Hardware version:" << version;
            } else {
                qDebug() << "  Failed to get hardware version - Error:" << result;
            }
            
            // 关闭测试连接
            CloseDAQDevice(testHandle);
        }
    }
    
    static void printErrorCode(int errorCode)
    {
        char errorString[256];
        ErrorToString(errorCode, errorString);
        qDebug() << "Error code" << errorCode << ":" << QString::fromLocal8Bit(errorString);
    }
};

// 诊断主函数
void runDeviceDiagnostic()
{
    qDebug() << "设备连接诊断工具";
    qDebug() << "此工具将检查设备连接状态并提供诊断信息\n";
    
    // 检查是否有配置的设备
    if(sensor_config_message.isEmpty()) {
        qDebug() << "警告：没有找到设备配置信息";
        qDebug() << "请确保已正确加载项目配置";
        return;
    }
    
    DeviceDiagnostic::checkDeviceConnections();
    
    // 提供故障排除建议
    qDebug() << "=== 故障排除建议 ===";
    qDebug() << "如果设备连接失败，请检查：";
    qDebug() << "1. 网络连接是否正常";
    qDebug() << "2. 设备IP地址是否正确";
    qDebug() << "3. 设备是否已开机并完成初始化";
    qDebug() << "4. 防火墙是否阻止了连接";
    qDebug() << "5. 是否有其他程序正在使用该设备";
    
    qDebug() << "\n常见错误代码：";
    qDebug() << "19 (DAQE_SCRATCH_ERROR) - 未查询到结果，通常是设备连接问题";
    qDebug() << "1004 (DAQE_DEVICE_NOT_OPEN) - 设备未打开";
    qDebug() << "1007 (DAQE_DAQDEVICE_NOT_FOUND) - 未找到设备";
    qDebug() << "1008 (DAQE_COMM_FAILURE) - 通讯错误";
    qDebug() << "1011 (DAQE_COMM_TIMEOUT) - 通讯超时";
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    // 延迟执行诊断，给用户时间阅读信息
    QTimer::singleShot(1000, []() {
        runDeviceDiagnostic();
        QCoreApplication::quit();
    });
    
    return app.exec();
}
