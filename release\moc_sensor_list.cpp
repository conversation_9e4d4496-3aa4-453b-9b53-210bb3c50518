/****************************************************************************
** Meta object code from reading C++ file 'sensor_list.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.8)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../sensor_list.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'sensor_list.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.8. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_sensor_list_t {
    QByteArrayData data[20];
    char stringdata0[417];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_sensor_list_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_sensor_list_t qt_meta_stringdata_sensor_list = {
    {
QT_MOC_LITERAL(0, 0, 11), // "sensor_list"
QT_MOC_LITERAL(1, 12, 23), // "save_project_and_sensor"
QT_MOC_LITERAL(2, 36, 0), // ""
QT_MOC_LITERAL(3, 37, 6), // "new_IP"
QT_MOC_LITERAL(4, 44, 6), // "old_IP"
QT_MOC_LITERAL(5, 51, 21), // "on_pushButton_clicked"
QT_MOC_LITERAL(6, 73, 28), // "on_pushButton_reload_clicked"
QT_MOC_LITERAL(7, 102, 44), // "on_comboBox_device_choose_cur..."
QT_MOC_LITERAL(8, 147, 4), // "arg1"
QT_MOC_LITERAL(9, 152, 38), // "on_comboBox_project_currentTe..."
QT_MOC_LITERAL(10, 191, 26), // "on_pushButton_save_clicked"
QT_MOC_LITERAL(11, 218, 32), // "on_tableWidget_device_IP_clicked"
QT_MOC_LITERAL(12, 251, 5), // "index"
QT_MOC_LITERAL(13, 257, 43), // "on_tableWidget_device_IP_curr..."
QT_MOC_LITERAL(14, 301, 17), // "QTableWidgetItem*"
QT_MOC_LITERAL(15, 319, 7), // "current"
QT_MOC_LITERAL(16, 327, 8), // "previous"
QT_MOC_LITERAL(17, 336, 36), // "on_tableWidget_device_IP_item..."
QT_MOC_LITERAL(18, 373, 4), // "item"
QT_MOC_LITERAL(19, 378, 38) // "on_tableWidget_device_IP_doub..."

    },
    "sensor_list\0save_project_and_sensor\0"
    "\0new_IP\0old_IP\0on_pushButton_clicked\0"
    "on_pushButton_reload_clicked\0"
    "on_comboBox_device_choose_currentTextChanged\0"
    "arg1\0on_comboBox_project_currentTextChanged\0"
    "on_pushButton_save_clicked\0"
    "on_tableWidget_device_IP_clicked\0index\0"
    "on_tableWidget_device_IP_currentItemChanged\0"
    "QTableWidgetItem*\0current\0previous\0"
    "on_tableWidget_device_IP_itemChanged\0"
    "item\0on_tableWidget_device_IP_doubleClicked"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_sensor_list[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
      10,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,   64,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       5,    0,   69,    2, 0x08 /* Private */,
       6,    0,   70,    2, 0x08 /* Private */,
       7,    1,   71,    2, 0x08 /* Private */,
       9,    1,   74,    2, 0x08 /* Private */,
      10,    0,   77,    2, 0x08 /* Private */,
      11,    1,   78,    2, 0x08 /* Private */,
      13,    2,   81,    2, 0x08 /* Private */,
      17,    1,   86,    2, 0x08 /* Private */,
      19,    1,   89,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString, QMetaType::QString,    3,    4,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    8,
    QMetaType::Void, QMetaType::QString,    8,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QModelIndex,   12,
    QMetaType::Void, 0x80000000 | 14, 0x80000000 | 14,   15,   16,
    QMetaType::Void, 0x80000000 | 14,   18,
    QMetaType::Void, QMetaType::QModelIndex,   12,

       0        // eod
};

void sensor_list::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        sensor_list *_t = static_cast<sensor_list *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->save_project_and_sensor((*reinterpret_cast< QString(*)>(_a[1])),(*reinterpret_cast< QString(*)>(_a[2]))); break;
        case 1: _t->on_pushButton_clicked(); break;
        case 2: _t->on_pushButton_reload_clicked(); break;
        case 3: _t->on_comboBox_device_choose_currentTextChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 4: _t->on_comboBox_project_currentTextChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 5: _t->on_pushButton_save_clicked(); break;
        case 6: _t->on_tableWidget_device_IP_clicked((*reinterpret_cast< const QModelIndex(*)>(_a[1]))); break;
        case 7: _t->on_tableWidget_device_IP_currentItemChanged((*reinterpret_cast< QTableWidgetItem*(*)>(_a[1])),(*reinterpret_cast< QTableWidgetItem*(*)>(_a[2]))); break;
        case 8: _t->on_tableWidget_device_IP_itemChanged((*reinterpret_cast< QTableWidgetItem*(*)>(_a[1]))); break;
        case 9: _t->on_tableWidget_device_IP_doubleClicked((*reinterpret_cast< const QModelIndex(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            typedef void (sensor_list::*_t)(QString , QString );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&sensor_list::save_project_and_sensor)) {
                *result = 0;
                return;
            }
        }
    }
}

const QMetaObject sensor_list::staticMetaObject = {
    { &QWidget::staticMetaObject, qt_meta_stringdata_sensor_list.data,
      qt_meta_data_sensor_list,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *sensor_list::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *sensor_list::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_sensor_list.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int sensor_list::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 10)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 10)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 10;
    }
    return _id;
}

// SIGNAL 0
void sensor_list::save_project_and_sensor(QString _t1, QString _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
