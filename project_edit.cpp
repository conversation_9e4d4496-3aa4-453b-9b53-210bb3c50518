#include "project_edit.h"
#include "ui_project_edit.h"
#include "message.h"
#include "sensor_list.h"

#include <QDebug>
#include <QFileDialog>
#include <QSqlQuery>
#include <QSqlError>

Project_edit::Project_edit(Project* p, QWidget *parent) :
    QWidget(parent),
    ui(new Ui::Project_edit),
    project(p)
{
    ui->setupUi(this);

    this->ui->lineEdit_name->setText(project->name);
    this->ui->lineEdit_company->setText(project->company );
    this->ui->lineEdit_operator->setText(project->Operator);
    this->ui->lineEdit_path->setText( project->file_path);
    this->ui->textEdit_description->setText(project->description);




}

Project_edit::~Project_edit()
{
    delete ui;
}

void Project_edit::on_pushButton_path_clicked()
{
    QString path = QFileDialog::getExistingDirectory(this, "选择存储路径", "/");
    if (!path.isEmpty())
    {
        ui->lineEdit_path->setText(path);
    }
}

void Project_edit::on_pushButton_save_clicked()
{
    project->name = this->ui->lineEdit_name->text();
    project->company = this->ui->lineEdit_company->text();
    project->Operator = this->ui->lineEdit_operator->text();
    project->file_path = this->ui->lineEdit_path->text();
    project->description = this->ui->textEdit_description->toPlainText();

    QSqlQuery query;
    QString sql = "UPDATE top_project SET "
                  "name = '" + project->name + "', "
                  //"speed = " + QString::number(project->speed) + ", "
                  "company = '" + project->company + "', "
                  //"damping = " + QString::number(project->damping) + ", "
                  "operator = '" + project->Operator + "', "
                  "file_path = '" + project->file_path + "', "
                 // "frequency = " + QString::number(project->frequency) + ", "
                  "description = '" + project->description + "', "
                  //"sensitivity = " + QString::number(project->sensitivity) + " "
                  "WHERE id = " + QString::number(project->id) + ";";
    //qDebug() << sql;
    if (query.exec(sql))
    {
        qDebug() << "数据插入成功" << __LINE__;
    }
    else
    {
        qDebug() << "数据插入失败:" << query.lastError().text() << __LINE__;
    }
}

void Project_edit::on_pushButton_cancel_clicked()
{
    this->close();
}

void Project_edit::on_pushButton_sensor_setting_clicked()
{

}
