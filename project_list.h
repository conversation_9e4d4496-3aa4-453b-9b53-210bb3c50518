#ifndef PROJECT_LIST_H
#define PROJECT_LIST_H

#include <QWidget>
#include <QTableWidget>

namespace Ui {
class Project_list;
}

class Project_list : public QWidget
{
    Q_OBJECT

public:
    explicit Project_list(QWidget *parent = nullptr);
    ~Project_list();

private slots:


    void on_pushButton_search_clicked();

    void on_pushButton_reload_clicked();

    void load_data(QString queryName);

    void on_tableWidget_clicked(const QModelIndex &index);

    void on_tableWidget_doubleClicked(const QModelIndex &index);

    void on_tableWidget_itemChanged(QTableWidgetItem *item);

private:
    Ui::Project_list *ui;
    QString data;
};

#endif // PROJECT_LIST_H
