﻿#include "common.h"

//QList<QHostAddress> XC_COMMON::GetLocalHostAddress()
//{
//    QString hostName=QHostInfo::localHostName();//本地主机名
//    QHostInfo hostInfo=QHostInfo::fromName(hostName); //本机IP地址
//    return hostInfo.addresses();
//}
//获得ip列表
//借助 QNetworkInterface::allAddresses() 获取本地主机的所有网络地址。
//遍历地址列表，把非 IPv4 地址和回环地址从列表中移除。
//返回过滤后的 IPv4 地址列表。
QList<QHostAddress> XC_COMMON::GetLocalHostAddress()
{
    QList<QHostAddress> ipAddressesList = QNetworkInterface::allAddresses();

    for (int i = 0; i < ipAddressesList.size(); ) {
        if (ipAddressesList.at(i).protocol() != QAbstractSocket::IPv4Protocol) {
            ipAddressesList.removeAt(i);
        }else if(ipAddressesList.at(i).toString().contains("127.0.")){
            ipAddressesList.removeAt(i);
        }else{
            ++i;
        }
    }

    return ipAddressesList;
}
//该函数用于将一个 32 位无符号整数形式的 IPv4 地址转换为标准的点分十进制字符串形式，例如把 3232235777 转换为 ***********。
QString XC_COMMON::IPV4IntegerToString(quint32 ip) {
    return QString("%1.%2.%3.%4")
            .arg(ip & 0xFF)
            .arg((ip >> 8) & 0xFF)
            .arg((ip >> 16) & 0xFF)
            .arg((ip >> 24)& 0xFF);
}
//此函数的作用是将点分十进制字符串形式的 IPv4 地址转换为 32 位无符号整数形式，例如把 *********** 转换为 3232235777。若输入的字符串格式不对（不是由四个数字段用点分隔），则返回 0。
quint32 XC_COMMON::IPV4StringToInteger(const QString& ip) {
    QStringList ips = ip.split(".");
    if (ips.size() == 4) {
        return ips.at(0).toInt()
                | ips.at(1).toInt() << 8
                | ips.at(2).toInt() << 16
                | ips.at(3).toInt() << 24;
    }
    return 0;
}
//该函数把点分十进制字符串形式的 IPv4 地址转换为一个包含四个字节的数组，若输入的字符串格式不对，则返回全 0 的数组。
uchar* XC_COMMON::IPV4StringToChar4(const QString& ip)
{
    QStringList ips = ip.split(".");
    static unsigned char iIp[4] = {0};

    if (ips.size() == 4)
    {
        for (int i = 0; i < 4; ++i)
            iIp[i] = ips.at(i).toUInt();
    }

    return iIp;
}
//此函数将一个 64 位无符号整数形式的 MAC 地址转换为标准的十六进制字符串形式，例如 123456789ABC 转换为 12-34-56-78-9A-BC。
QString XC_COMMON::MACIntegerToString(quint64 mac) {
    return QString("%1-%2-%3-%4-%5-%6")
            .arg(mac & 0xFF, 2, 16).toUpper()
            .arg((mac >> 8) & 0xFF, 2, 16).toUpper()
            .arg((mac >> 16) & 0xFF, 2, 16).toUpper()
            .arg((mac >> 24) & 0xFF, 2, 16).toUpper()
            .arg((mac >> 32) & 0xFF, 2, 16).toUpper()
            .arg((mac >> 40) & 0xFF, 2, 16).toUpper();
}
//该函数把标准的十六进制字符串形式的 MAC 地址转换为 64 位无符号整数形式，例如 12-34-56-78-9A-BC 转换为 123456789ABC。若输入的字符串格式不对（不是由六个十六进制数段用连字符分隔），则返回 0。
quint64 XC_COMMON::MACStringToInteger(const QString& mac)
{
    QStringList macs = mac.split("-");
    if (macs.size() == 6) {
        return macs.at(0).toLongLong(nullptr, 16)
                | macs.at(1).toLongLong(nullptr, 16) << 8
                | macs.at(2).toLongLong(nullptr, 16) << 16
                | macs.at(3).toLongLong(nullptr, 16) << 24
                | macs.at(4).toLongLong(nullptr, 16) << 32
                | macs.at(5).toLongLong(nullptr, 16) << 40;
    }
    return 0;
}

