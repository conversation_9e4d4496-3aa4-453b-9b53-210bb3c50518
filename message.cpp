#include "message.h"

#include "database.h"
#include "device_configuration.h"
#include "sensor_list.h"
#include "mainwindow.h"
#include "common.h"
#include "NET2418.h"

#include <QJsonArray>
#include <QJsonDocument>
#include <QSqlQuery>
#include <QString>
#include <QTableWidget>
#include <QSqlError>
#include <QDebug>
#include <QJsonObject>
#include <QJsonArray>
#include <QCheckBox>
#include <QHBoxLayout>
#include <QMessageBox>
#include <QTableWidget>
#include <QLineEdit>
#include <QComboBox>
#include <QCheckBox>
#include <QDoubleSpinBox>
#include <QSpinBox>
#include <QCoreApplication>

User* current_user = new User();
Project* current_project = new Project();

/**
 * @brief 全局数据库，控制数据库的连接和断开连接
 */
database* db = new database();

/**
 * @brief 全局设备数，保存当前工程的板卡数量
 */
int nums = 0;

/**
 * @brief 设备默认配置，有参构造传入默认参数，用于创建工程和创建检波器时
 */
sensor_config* sensor_config_message_default = new sensor_config("0");

/**
 * @brief 全局设备配置，大小为当前工程的板卡数量，储存每个板卡的检波器信息
 */
QVector<sensor_config*> sensor_config_message;


/**
 * @brief 把指定检波器配置加载到表单上
 * @param 要加载的检波器配置
 * @param 用于显示的表单
 */
void sensor_config::class_to_tableWidget(sensor_config* sensor_config_message, QTableWidget* tableWidget)
{
    tableWidget->clearContents();
    tableWidget->setRowCount(16); // 重置行数

    for(int col = 0; col < 14; ++col)
    {
        for(int row = 0; row < 16; ++row)
        {
            tableWidget->setRowHeight(row, 70);

            if(col == 0)
            {
                QTableWidgetItem* item = new QTableWidgetItem("通道" + QString::number(row + 1));
                item->setTextAlignment(Qt::AlignCenter);

                tableWidget->setItem(row, col, item);
            }
            else if (col == 1)
            {
                QDoubleSpinBox* item = new QDoubleSpinBox();
                item->setValue(sensor_config_message->sampling_rate[row]);
                item->setAlignment(Qt::AlignCenter);
                item->setRange(0.1, 10);
                item->setSingleStep(0.1);
                tableWidget->setCellWidget(row, col, item);
            }
//            else if (col == 2)
//            {
//                QComboBox* item = new QComboBox();
//                //给QComboBox加选项
//                for(int i = 0; i < 11; ++i)
//                {
//                    item->addItem(QString::number(i));
//                }
//                item->setCurrentText(QString::number(sensor_config_message->gain[row]));
//                tableWidget->setCellWidget(row, col, item);
//            }
            else if (col == 2)
            {
                QCheckBox* item = new QCheckBox();
                if(int(sensor_config_message->channel_isEnable[row]) == 0)
                {
                    item->setChecked(0);
                }
                else if(int(sensor_config_message->channel_isEnable[row]) == 1)
                {
                    item->setChecked(1);
                }
                tableWidget->setCellWidget(row, col, item);
            }
//            else if (col == 4)
//            {
//                QComboBox* item = new QComboBox();
//                item->addItem("电压");
//                item->addItem("电流");
//                if(sensor_config_message->jumper_voltage[row] == 0.0)
//                {
//                    item->setCurrentText("电流");
//                }
//                else if(sensor_config_message->jumper_voltage[row] == 1.0)
//                {
//                    item->setCurrentText("电压");
//                }
//                tableWidget->setCellWidget(row, col, item);
//            }
            else if(col == 3)
            {
                QComboBox* item = new QComboBox();
                item->addItem("开启恒流源，交流");
                item->addItem("关闭恒流源，直流");
                item->addItem("关闭恒流源，交流");
                if(sensor_config_message->current_source[row] == 0.0)
                {
                    item->setCurrentText("开启恒流源，交流");
                }
                else if(sensor_config_message->current_source[row] == 1.0)
                {
                    item->setCurrentText("关闭恒流源，直流");
                }
                else if(sensor_config_message->current_source[row] == 2.0)
                {
                    item->setCurrentText("关闭恒流源，交流");
                }
                tableWidget->setCellWidget(row, col, item);
            }
            else if (col == 4)
            {
                QSpinBox* item = new QSpinBox();
                item->setRange(0,10000);
                item->setValue(sensor_config_message->waitin_time[row]);
                item->setAlignment(Qt::AlignCenter);
                tableWidget->setCellWidget(row, col, item);
            }
            else if (col == 5)
            {
                QSpinBox* item = new QSpinBox();
                item->setValue(sensor_config_message->buffer_size[row]);
                item->setAlignment(Qt::AlignCenter);
                tableWidget->setCellWidget(row, col, item);
            }
            else if (col == 6)
            {
                QCheckBox* item = new QCheckBox();

                if(sensor_config_message->calibration_isEnable[row] == 0)
                {
                    item->setChecked(false);
                }
                else if(sensor_config_message->calibration_isEnable[row] == 1)
                {
                    item->setChecked(true);
                }
                tableWidget->setCellWidget(row, col, item);
            }
            else if (col == 7)
            {
                QCheckBox* item = new QCheckBox();
                if(sensor_config_message->time_stamp_isEnable[row] == 0)
                {
                    item->setChecked(0);
                }
                else if(sensor_config_message->time_stamp_isEnable[row] == 1)
                {
                    item->setChecked(1);
                }
                tableWidget->setCellWidget(row, col, item);
            }
            else if (col == 8)
            {
                QComboBox* item = new QComboBox();
                item->addItem("TCP");
                item->addItem("UDP");
                if(sensor_config_message->transmission_pathway[row] == 0)
                {
                    item->setCurrentText("TCP");
                }
                else if(sensor_config_message->transmission_pathway[row] == 1)
                {
                    item->setCurrentText("UDP");
                }
                tableWidget->setCellWidget(row, col, item);
            }
            else if (col == 9)
            {
                QComboBox* item = new QComboBox();
                item->addItem("仅采集AD信号");
                item->addItem("同步传输包尾");
                if(sensor_config_message->AD_start[row] == 0)
                {
                    item->setCurrentText("仅采集AD信号");
                }
                else if(sensor_config_message->AD_start[row] == 8)
                {
                    item->setCurrentText("同步传输包尾");
                }
                tableWidget->setCellWidget(row, col, item);
            }
            else if(col == 10)
            {
                QDoubleSpinBox* item = new QDoubleSpinBox();
                item->setValue(sensor_config_message->x[row]);
                item->setAlignment(Qt::AlignCenter);
                tableWidget->setCellWidget(row, col, item);
            }
            else if(col == 11)
            {
                QDoubleSpinBox* item = new QDoubleSpinBox();
                item->setValue(sensor_config_message->y[row]);
                item->setAlignment(Qt::AlignCenter);
                tableWidget->setCellWidget(row, col, item);
            }
            else if(col == 12)
            {
                QDoubleSpinBox* item = new QDoubleSpinBox();
                item->setValue(sensor_config_message->z[row]);
                item->setAlignment(Qt::AlignCenter);
                tableWidget->setCellWidget(row, col, item);
            }
        }
    }
}

/**
 * @brief 通过当前设备的IP获取其索引
 * @param 指定设备的IP
 * @return 返回设备配置对象数组的索引
 */
int get_index_by_IP(QString IP)
{
    for(int i = 0; i < nums; ++i)
    {
        if(sensor_config_message[i]->IP == IP)
        {
            return  i;
        }
    }
    return -1;
}

/**
 * @brief 获取表单当前显示的检波器配置并储存到指定检波器配置对象
 * @param 用于存储配置的检波器配置对象
 * @param 指定数据从哪个表单获取
 */
void sensor_config::tableWidget_to_class(sensor_config* sensor_config_message, QTableWidget* tableWidget, QString IP)
{
    for (int col = 0; col < tableWidget->columnCount(); ++col)
    {
        if(col == 0)
        {
            sensor_config_message->IP = IP;
        }
        else if(col == 1)
        {
            for(int row = 0; row < channel_count; row++)
            {
                 QDoubleSpinBox *spinDoubleBox = qobject_cast<QDoubleSpinBox*>(tableWidget->cellWidget(row, col));
                 sensor_config_message->sampling_rate[row] = spinDoubleBox->text().toDouble();
            }
        }
//        else if(col == 2)
//        {
//            for(int row = 0; row < channel_count; row++)
//            {
//                 QComboBox *comboBox = qobject_cast<QComboBox*>(tableWidget->cellWidget(row, col));
//                 sensor_config_message->gain[row] = comboBox->currentText().toDouble();
//            }
//        }
        else if(col == 2)
        {
            for(int row = 0; row < channel_count; row++)
            {
                 QCheckBox *checkBox = qobject_cast<QCheckBox*>(tableWidget->cellWidget(row, col));
                 if(checkBox->checkState() == Qt::Checked)
                 {
                     sensor_config_message->channel_isEnable[row] = 1;
                 }
                 else if(checkBox->checkState() == Qt::Unchecked)
                 {
                     sensor_config_message->channel_isEnable[row] = 0;
                 }
            }
        }
//        else if(col == 4)
//        {
//            for(int row = 0; row < channel_count; row++)
//            {
//                 QComboBox *comboBox = qobject_cast<QComboBox*>(tableWidget->cellWidget(row, col));
//                 if(comboBox->currentText() == "电压")
//                 {
//                     sensor_config_message->jumper_voltage[row] = 1.0;
//                 }
//                 else if(comboBox->currentText() == "电流")
//                 {
//                     sensor_config_message->jumper_voltage[row] = 0.0;
//                 }
//            }
//        }
        else if(col == 3)
        {
            for(int row = 0; row < channel_count; row++)
            {
                 QComboBox *comBox = qobject_cast<QComboBox*>(tableWidget->cellWidget(row, col));
                 if(comBox->currentText() == "开启恒流源， 交流")
                 {
                     sensor_config_message->current_source[row] = 0;
                 }
                 else if(comBox->currentText() == "关闭恒流源，直流")
                 {
                     sensor_config_message->current_source[row] = 1;
                 }
                 else if(comBox->currentText() == "关闭恒流源，交流")
                 {
                     sensor_config_message->current_source[row] = 2;
                 }
            }
        }
        else if(col == 4)
        {
            for(int row = 0; row < channel_count; row++)
            {
                 QSpinBox *spinBox = qobject_cast<QSpinBox*>(tableWidget->cellWidget(row, col));
                 sensor_config_message->waitin_time[row] = spinBox->text().toInt();
            }
        }
        else if(col == 5)
        {
            for(int row = 0; row < channel_count; row++)
            {
                 QSpinBox *spinBox = qobject_cast<QSpinBox*>(tableWidget->cellWidget(row, col));
                 sensor_config_message->buffer_size[row] = spinBox->text().toInt();
            }
        }
        else if(col == 6)
        {
            for(int row = 0; row < channel_count; row++)
            {
                 QCheckBox *checkBox = qobject_cast<QCheckBox*>(tableWidget->cellWidget(row, col));
                 if(checkBox->checkState() == Qt::Checked)
                 {
                     sensor_config_message->calibration_isEnable[row] = 1;
                 }
                 else if(checkBox->checkState() == Qt::Unchecked)
                 {
                     sensor_config_message->calibration_isEnable[row] = 0;
                 }
            }
        }
        else if(col == 7)
        {
            for(int row = 0; row < channel_count; row++)
            {
                 QCheckBox *checkBox = qobject_cast<QCheckBox*>(tableWidget->cellWidget(row, col));
                 if(checkBox->checkState() == Qt::Checked)
                 {
                     sensor_config_message->time_stamp_isEnable[row] = 1;
                 }
                 else if(checkBox->checkState() == Qt::Unchecked)
                 {
                     sensor_config_message->time_stamp_isEnable[row] = 0;
                 }
            }
        }
        else if(col == 8)
        {
            for(int row = 0; row < channel_count; row++)
            {
                 QComboBox *comboBox = qobject_cast<QComboBox*>(tableWidget->cellWidget(row, col));
                 if(comboBox->currentText() == "TCP")
                 {
                     sensor_config_message->transmission_pathway[row] = 0;
                 }
                 else if(comboBox->currentText() == "UDP")
                 {
                     sensor_config_message->transmission_pathway[row] = 1;
                 }
            }
        }
        else if(col == 9)
        {
            for(int row = 0; row < channel_count; row++)
            {
                 QComboBox *comboBox = qobject_cast<QComboBox*>(tableWidget->cellWidget(row, col));
                 if(comboBox->currentText() == "仅采集AD信号")
                 {
                     sensor_config_message->AD_start[row] = 0;
                 }
                 else if(comboBox->currentText() == "同步传输包尾")
                 {
                     sensor_config_message->AD_start[row] = 8;
                 }
            }
        }
        else if(col == 10)
        {
            for(int row = 0; row < channel_count; row++)
            {
                 QDoubleSpinBox *spinDoubleBox = qobject_cast<QDoubleSpinBox*>(tableWidget->cellWidget(row, col));
                 sensor_config_message->x[row] = spinDoubleBox->text().toDouble();
            }
        }
        else if(col == 11)
        {for(int row = 0; row < channel_count; row++)
            {
                 QDoubleSpinBox *spinDoubleBox = qobject_cast<QDoubleSpinBox*>(tableWidget->cellWidget(row, col));
                 sensor_config_message->y[row] = spinDoubleBox->text().toDouble();
            }
        }
        else if(col == 12)
        {
            for(int row = 0; row < channel_count; row++)
            {
                 QDoubleSpinBox *spinDoubleBox = qobject_cast<QDoubleSpinBox*>(tableWidget->cellWidget(row, col));
                 sensor_config_message->z[row] = spinDoubleBox->text().toDouble();
            }
        }
    }
}

/**
 * @brief 读取数据库数据，加载指定当前启用工程的检波器配置、板卡数量
 * 此函数将更新全局当前工程变量、全局检波器配置变量
 */
void load_project_and_sensor_message_config_from_database(int project_id)
{
    QSqlQuery query("SELECT * FROM top_project WHERE id = " + QString::number(project_id) + ";");
    if(!query.exec())
    {
        qDebug() << query.lastError() << __LINE__;
    }
    query.next();
    current_project->id = query.value(0).toInt();
    current_project->user_id = query.value(1).toInt();
    current_project->name = query.value(2).toString();
    current_project->description = query.value(3).toString();
    current_project->file_path = query.value(4).toString();
    current_project->company = query.value(5).toString();
    current_project->Operator = query.value(6).toString();
    current_project->status = query.value(7).toInt();
    current_project->create_time = query.value(8).toString();

    query.exec("SELECT * FROM top_sensor WHERE project_id = " + QString::number(project_id) + ";");
    if(!query.exec())
    {
        qDebug() << query.lastError() << __LINE__;
    }

    //更新当前工程设备数量
    while (query.next())
    {
        nums++;
        sensor_config_message << new sensor_config();
    }




//    //更新句柄数量_;
//    for(int i = 0; i < nums; i++)
//    {
//        qDebug() <<sensor_config_message[i]<<__LINE__ <<__FILE__;
//       pHandle.append(MyHANDLE_init(sensor_config_message[i]));
//    }

    query.exec("SELECT * FROM top_sensor WHERE project_id = " + QString::number(project_id) + ";");
    if (!query.exec())
    {
        qDebug() << query.lastError() << __LINE__;
    }

    for(int device_count = 0; device_count < nums; device_count++)
    {
         query.next();
        for (int loop_count = -1; loop_count< 13; ++loop_count)
        {
            if(loop_count == -1)//IP
            {
                sensor_config_message[device_count]->IP = query.value(0).toString();
            }
            else if(loop_count == 0)//采样率
            {
                QString jsonString = query.value(1).toString();
                QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8());
                if(!doc.isNull() && doc.isArray())
                {
                    QJsonArray jsonArray = doc.array();
                    for(int i = 0; i < jsonArray.size(); ++i)
                    {
                        sensor_config_message[device_count]->sampling_rate[i] = jsonArray[i].toDouble();
                    }
                }
            }
//            else if (loop_count == 1)//增益
//            {
//                QString jsonString = query.value(2).toString();
//                QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8());
//                if(!doc.isNull() && doc.isArray())
//                {
//                    QJsonArray jsonArray = doc.array();
//                    for(int i = 0; i < jsonArray.size(); ++i)
//                    {
//                        sensor_config_message[device_count]->gain[i] = jsonArray[i].toDouble();
//                    }
//                }
//            }
            else if (loop_count == 1)//通道是否启用
            {
                QString jsonString = query.value(2).toString();
                QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8());
                if(!doc.isNull() && doc.isArray())
                {
                    QJsonArray jsonArray = doc.array();
                    for(int i = 0; i < jsonArray.size(); ++i)
                    {
                        sensor_config_message[device_count]->channel_isEnable[i] = jsonArray[i].toDouble();
                    }
                }
            }
//            else if (loop_count == 3)//跳线电流//电压
//            {
//                QString jsonString = query.value(4).toString();
//                QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8());
//                if(!doc.isNull() && doc.isArray())
//                {
//                    QJsonArray jsonArray = doc.array();
//                    for(int i = 0; i < jsonArray.size(); ++i)
//                    {
//                        sensor_config_message[device_count]->jumper_voltage[i] = jsonArray[i].toDouble();
//                    }
//                }
//            }
            else if(loop_count == 2)
            {
                QString jsonString = query.value(3).toString();
                QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8());
                if(!doc.isNull() && doc.isArray())
                {
                    QJsonArray jsonArray = doc.array();
                    for(int i = 0; i < jsonArray.size(); ++i)
                    {
                        sensor_config_message[device_count]->current_source[i] = jsonArray[i].toInt();
                    }
                }
            }
            else if (loop_count == 3)//AD最大等待时间
            {
                QString jsonString = query.value(4).toString();
                QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8());
                if(!doc.isNull() && doc.isArray())
                {
                    QJsonArray jsonArray = doc.array();
                    for(int i = 0; i < jsonArray.size(); ++i)
                    {
                        sensor_config_message[device_count]->waitin_time[i] = jsonArray[i].toInt();
                    }
                }
            }
            else if (loop_count == 4)//缓冲区大小
            {
                QString jsonString = query.value(5).toString();
                QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8());
                if(!doc.isNull() && doc.isArray())
                {
                    QJsonArray jsonArray = doc.array();
                    for(int i = 0; i < jsonArray.size(); ++i)
                    {
                        sensor_config_message[device_count]->buffer_size[i] = jsonArray[i].toInt();
                    }
                }
            }
            else if (loop_count == 5)//是否使用校准
            {
                QString jsonString = query.value(6).toString();
                QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8());
                if(!doc.isNull() && doc.isArray())
                {
                    QJsonArray jsonArray = doc.array();
                    for(int i = 0; i < jsonArray.size(); ++i)
                    {
                        sensor_config_message[device_count]->calibration_isEnable[i] = jsonArray[i].toInt();
                    }
                }
            }
            else if (loop_count == 6)//是否返回时间戳
            {
                QString jsonString = query.value(7).toString();
                QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8());
                if(!doc.isNull() && doc.isArray())
                {
                    QJsonArray jsonArray = doc.array();
                    for(int i = 0; i < jsonArray.size(); ++i)
                    {
                        sensor_config_message[device_count]->time_stamp_isEnable[i] = jsonArray[i].toInt();
                    }
                }
            }
            else if (loop_count == 7)//传输通路
            {
                QString jsonString = query.value(8).toString();
                QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8());
                if(!doc.isNull() && doc.isArray())
                {
                    QJsonArray jsonArray = doc.array();
                    for(int i = 0; i < jsonArray.size(); ++i)
                    {
                        sensor_config_message[device_count]->transmission_pathway[i] = jsonArray[i].toInt();
                    }
                }
            }
            else if (loop_count == 8)//AD启动
            {
                QString jsonString = query.value(9).toString();
                QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8());
                if(!doc.isNull() && doc.isArray())
                {
                    QJsonArray jsonArray = doc.array();
                    for(int i = 0; i < jsonArray.size(); ++i)
                    {
                        sensor_config_message[device_count]->AD_start[i] = jsonArray[i].toInt();
                    }
                }
            }
            else if (loop_count == 9)//x
            {
                QString jsonString = query.value(10).toString();
                QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8());
                if(!doc.isNull() && doc.isArray())
                {
                    QJsonArray jsonArray = doc.array();
                    for(int i = 0; i < jsonArray.size(); ++i)
                    {
                        sensor_config_message[device_count]->x[i] = jsonArray[i].toInt();
                    }
                }
            }
            else if (loop_count == 10)//y
            {
                QString jsonString = query.value(11).toString();
                QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8());
                if(!doc.isNull() && doc.isArray())
                {
                    QJsonArray jsonArray = doc.array();
                    for(int i = 0; i < jsonArray.size(); ++i)
                    {
                        sensor_config_message[device_count]->y[i] = jsonArray[i].toInt();
                    }
                }
            }
            else if (loop_count == 11)//z
            {
                QString jsonString = query.value(12).toString();
                QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8());
                if(!doc.isNull() && doc.isArray())
                {
                    QJsonArray jsonArray = doc.array();
                    for(int i = 0; i < jsonArray.size(); ++i)
                    {
                        sensor_config_message[device_count]->z[i] = jsonArray[i].toInt();
                    }
                }
            }
        }
    }
}

sensor_config::sensor_config()
{
    this->sensor_config_get();
}

sensor_config::~sensor_config()
{

}
/**
 * @brief 将这个检波器配置对象中的内容保存到数据库
 */
void sensor_config::sensor_config_save(int project_id, QString mode)//保存到数据库
{
    QString sampling_rate_string, channel_isEnable_string, current_source_string,
               waitin_time_string, buffer_size_string, calibration_isEnable_string, time_stamp_isEnable_string,
               transmission_pathway_string, AD_start_string, x_string, y_string, z_string;

    for (int i = -1; i< 13; ++i)
    {
        if(i == -1)
        {
            //IP
        }
        else if (i == 0)
        {
            QJsonArray jsonArray;
            for(int i = 0; i < channel_count; ++i)
            {
                jsonArray.append(sampling_rate[i]);
            }
            QJsonDocument doc(jsonArray);
            sampling_rate_string = doc.toJson(QJsonDocument::Compact);
        }
//        else if (i == 1)
//        {
//            QJsonArray jsonArray;
//            for(int i = 0; i < channel_count; ++i)
//            {
//                jsonArray.append(gain[i]);
//            }
//            QJsonDocument doc(jsonArray);
//            gain_string = doc.toJson(QJsonDocument::Compact);
//        }
        else if (i == 1)
        {
            QJsonArray jsonArray;
            for(int i = 0; i < channel_count; ++i)
            {
                jsonArray.append(channel_isEnable[i]);
            }
            QJsonDocument doc(jsonArray);
            channel_isEnable_string = doc.toJson(QJsonDocument::Compact);
        }
//        else if (i == 3)
//        {
//            QJsonArray jsonArray;
//            for(int i = 0; i < channel_count; ++i)
//            {
//                jsonArray.append(jumper_voltage[i]);
//            }
//            QJsonDocument doc(jsonArray);
//            jumper_voltage_string = doc.toJson(QJsonDocument::Compact);
//        }
        else if (i == 2)
        {
            QJsonArray jsonArray;
            for(int i = 0; i < channel_count; ++i)
            {
                jsonArray.append(current_source[i]);
            }
            QJsonDocument doc(jsonArray);
            current_source_string = doc.toJson(QJsonDocument::Compact);
        }
        else if (i == 3)
        {
            QJsonArray jsonArray;
            for(int i = 0; i < channel_count; ++i)
            {
                jsonArray.append(waitin_time[i]);
            }
            QJsonDocument doc(jsonArray);
            waitin_time_string = doc.toJson(QJsonDocument::Compact);
        }
        else if (i == 4)
        {
            QJsonArray jsonArray;
            for(int i = 0; i < channel_count; ++i)
            {
                jsonArray.append(buffer_size[i]);
            }
            QJsonDocument doc(jsonArray);
            buffer_size_string = doc.toJson(QJsonDocument::Compact);
        }
        else if (i == 5)
        {
            QJsonArray jsonArray;
            for(int i = 0; i < channel_count; ++i)
            {
                jsonArray.append(calibration_isEnable[i]);
            }
            QJsonDocument doc(jsonArray);
            calibration_isEnable_string = doc.toJson(QJsonDocument::Compact);
        }
        else if (i == 6)
        {
            QJsonArray jsonArray;
            for(int i = 0; i < channel_count; ++i)
            {
                jsonArray.append(time_stamp_isEnable[i]);
            }
            QJsonDocument doc(jsonArray);
            time_stamp_isEnable_string = doc.toJson(QJsonDocument::Compact);
        }
        else if (i == 7)
        {
            QJsonArray jsonArray;
            for(int i = 0; i < channel_count; ++i)
            {
                jsonArray.append(transmission_pathway[i]);
            }
            QJsonDocument doc(jsonArray);
            transmission_pathway_string = doc.toJson(QJsonDocument::Compact);
        }
        else if (i == 8)
        {
            QJsonArray jsonArray;
            for(int i = 0; i < channel_count; ++i)
            {
                jsonArray.append(AD_start[i]);
            }
            QJsonDocument doc(jsonArray);
            AD_start_string = doc.toJson(QJsonDocument::Compact);;
        }
        else if (i == 9)
        {
            QJsonArray jsonArray;
            for(int i = 0; i < channel_count; ++i)
            {
                jsonArray.append(x[i]);
            }
            QJsonDocument doc(jsonArray);
            x_string = doc.toJson(QJsonDocument::Compact);
        }
        else if (i == 10)
        {
            QJsonArray jsonArray;
            for(int i = 0; i < channel_count; ++i)
            {
                jsonArray.append(y[i]);
            }
            QJsonDocument doc(jsonArray);
            y_string = doc.toJson(QJsonDocument::Compact);
        }
        else if (i == 11)
        {
            QJsonArray jsonArray;
            for(int i = 0; i < channel_count; ++i)
            {
                jsonArray.append(buffer_size[i]);
            }
            QJsonDocument doc(jsonArray);
            z_string = doc.toJson(QJsonDocument::Compact);
        }
    }
    if(mode == "insert")
    {
        QSqlQuery query;
        query.prepare("INSERT INTO top_sensor (IP, sampling_rate, channel_isEnable, "
                                "current_source, waitin_time, buffer_size, calibration_isEnable, "
                                "time_stamp_isEnable, transmission_pathway, AD_start, x, y, z, project_id) "
                      "VALUES (:IP, :sampling_rate, :gain, :channel_isEnable, "
                                ":jumper_voltage, :waitin_time, :buffer_size, :calibration_isEnable, "
                                ":time_stamp_isEnable, :transmission_pathway, :AD_start, :x, :y, :z, :project_id)");

        query.bindValue(":IP", this->IP);
        query.bindValue(":sampling_rate", sampling_rate_string);
        //query.bindValue(":gain", gain_string);
        query.bindValue(":channel_isEnable", channel_isEnable_string);

        //query.bindValue(":jumper_voltage", jumper_voltage_string);
        query.bindValue(":current_source", current_source_string);
        query.bindValue(":waitin_time", waitin_time_string);
        query.bindValue(":buffer_size", buffer_size_string);
        query.bindValue(":calibration_isEnable", calibration_isEnable_string);

        query.bindValue(":time_stamp_isEnable", time_stamp_isEnable_string);
        query.bindValue(":transmission_pathway", transmission_pathway_string);
        query.bindValue(":AD_start", AD_start_string);
        query.bindValue(":x", x_string);

        query.bindValue(":y", y_string);
        query.bindValue(":z", z_string);
        query.bindValue(":project_id", project_id);
        if(!query.exec())
        {
            qDebug() << "数据库连接失败：" << query.lastError().text() << __LINE__ << __FILE__;
        }
    }
    if(mode == "update")
    {
        QSqlQuery query;
        QString sql = "UPDATE top_sensor "
                      "SET "
                      "IP = '" + this->IP + "', "
                      "sampling_rate = " + sampling_rate_string + ", "
                      //"gain = " +gain_string + ", "
                      "channel_isEnable = "+ channel_isEnable_string + ", "
                     // "jumper_voltage = "+ jumper_voltage_string + ",
                      "current_source = "+ current_source_string + ", "
                      "waitin_time = "+ waitin_time_string + ", "
                      "buffer_size = "+ buffer_size_string + ", "
                      "calibration_isEnable = "+ calibration_isEnable_string + ", "
                      "time_stamp_isEnable = "+ time_stamp_isEnable_string + ", "
                      "transmission_pathway = "+ transmission_pathway_string + ", "
                      "AD_start = "+ AD_start_string + ", "
                      "x = "+ x_string + ", "
                      "y = "+ y_string + ", "
                      "z = "+ z_string +
                      " WHERE id = " + QString::number(project_id) + ";"; // 需要添加唯一标识记录的条件

        if(!query.exec(sql))
        {
            qDebug() << query.lastError().text() << __LINE__ << __FILE__;
        }
    }

}

void sensor_config::sensor_config_get()
{

}


/**
 * @brief 配置数据库参数
 */
database::database()
{
     QString savePath = QCoreApplication::applicationDirPath() +"msvcx90.db";
    this->db = QSqlDatabase::addDatabase("QSQLITE");
    this->db.setDatabaseName(savePath); // 数据库文件名
}

/**
 * @brief 连接数据库
 */
void database::connect_database()
{
    if (!this->db.open())
    {
        qDebug() << "数据库连接失败：" << this->db.lastError().text() << __LINE__ << __FILE__;
        return;
    }
    else
    {
        qDebug() <<" 数据库连接成功" << __LINE__ << __FILE__;
    }
}

/**
 * @brief 断开数据库连接
 */
void database::disconnect_database()
{
    this->db.close();
    qDebug() << "数据库断开连接" << __LINE__ << __FILE__;
}


MyHANDLE MyHANDLE_init(sensor_config* config)
{
    long    x1 = 8;
    QString cardIP="************";
    int iResult = 0;
    bool useFirst = false;

    MyHANDLE pHandle = nullptr; // 初始化为nullptr

     double f = 10.00;

    qDebug() << "Initializing device with IP:" << config->IP;
    iResult = OpenDAQDevice(config->IP.toStdString().c_str(), useFirst, &pHandle);

    // 检查设备打开是否成功
    if(iResult != 0 || pHandle == nullptr) {
        qDebug() << "Failed to open device" << config->IP << "Error code:" << iResult << __LINE__ << __FILE__;
        return nullptr;
    }

    qDebug() << "Device opened successfully, handle:" << pHandle << __LINE__ << __FILE__;

//==========================================================================================================
    double ipValue = XC_COMMON::IPV4StringToInteger("************");
    iResult = ePut(pHandle, XC_ioPUT_CONFIG, XC_ctETHERNET_LOCALIP, &ipValue, &x1);//设置本地IP - 修复：使用pHandle而不是0
    if(iResult)
    {
        qDebug()<<"设置IP失败！返回码：" + QString::number(iResult) << __LINE__ << __FILE__;
    }
//==========================================================================================================
    double  freq=0.100;
    iResult = ePut(pHandle, XC_ioPUT_CONFIG, XC_chSTREAM_SCAN_FREQUENCY,&freq, &x1);
    //iResult = ePut(pHandle, XC_ioPUT_CONFIG, XC_chSTREAM_SCAN_FREQUENCY, config->sampling_rate, &x1);
    if(iResult)
    {
        qDebug() << "配置采样率错误" + QString::number(iResult) << __LINE__ << __FILE__;
    }
//==========================================================================================================
    iResult = ePut(pHandle, XC_ioPUT_CONFIG, XC_chSTREAM_Channel, config->channel_isEnable, &x1);
    if(iResult)
    {
        qDebug() << "配置使用通道错误" + QString::number(iResult) << __LINE__ << __FILE__;
    }
//==========================================================================================================
    iResult = ePut(pHandle, XC_ioPUT_CONFIG, XC_ioPUT_ConstantVolSource, config->current_source, &x1);
    if(iResult)
    {
        qDebug() << "配置配置交直流及恒流源错误" + QString::number(iResult) << __LINE__ << __FILE__;
    }
//==========================================================================================================
    iResult = ePut(pHandle, XC_ioPUT_CONFIG, XC_chSTREAM_COMMUNICATION_TIMEOUT, config->waitin_time, &x1);
    if(iResult)
    {
        qDebug() << "配置AD最大等待时间错误" + QString::number(iResult) << __LINE__ << __FILE__;
    }
//==========================================================================================================
    iResult = ePut(pHandle, XC_ioPUT_CONFIG, XC_ioPUT_STREAM_BUFFER, config->buffer_size, &x1);
    if(iResult)
    {
        qDebug() << "配置缓冲区大小错误" + QString::number(iResult) << __LINE__ << __FILE__;
    }
//==========================================================================================================
    iResult = ePut(pHandle, XC_ioPUT_CONFIG, XC_chSTREAM_ADJ, config->calibration_isEnable, &x1);
    if(iResult)
    {
        qDebug() << "配置是否使用校准错误" + QString::number(iResult) << __LINE__ << __FILE__;
    }
//==========================================================================================================
    iResult = ePut(pHandle, XC_ioPUT_CONFIG, XC_ioPUT_STREAM_TIME, config->time_stamp_isEnable, &x1);
    if(iResult)
    {
        qDebug() << "配置是否返回时间戳错误" + QString::number(iResult) << __LINE__ << __FILE__;
    }
//==========================================================================================================
    //传输通路控制，0-关闭，1-开启
    long ADControl = 1;
    // 通路类型，0-TCP，1-UDP
    long Atype = 1;
    // 指向上位机端口号，0-随机端口
    long portNo = 0;

    iResult = eControlADAccess(pHandle, ADControl, Atype, &portNo);
    if(iResult)
    {
        qDebug() << "配置传输通路错误" + QString::number(iResult) << __LINE__ << __FILE__;
    }
//==========================================================================================================
    // 配置AD启动
    iResult = ePut(pHandle, XC_ioSTART_STREAM, 8, nullptr, &x1);
    if(iResult)
    {
        qDebug() << "配置AD启动错误" + QString::number(iResult) << __LINE__ << __FILE__;
        // 如果AD启动失败，关闭设备并返回nullptr
        CloseDAQDevice(pHandle);
        return nullptr;
    }

    qDebug() << "Device" << config->IP << "initialized successfully" << __LINE__ << __FILE__;
//==========================================================================================================
    return pHandle;
}
