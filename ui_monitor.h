/********************************************************************************
** Form generated from reading UI file 'monitor.ui'
**
** Created by: Qt User Interface Compiler version 5.9.8
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MONITOR_H
#define UI_MONITOR_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_monitor
{
public:
    QVBoxLayout *verticalLayout_3;
    QWidget *widget_monitor_box;
    QVBoxLayout *verticalLayout_2;
    QWidget *widget_tool;
    QHBoxLayout *horizontalLayout_4;
    QLabel *label_device;
    QComboBox *comboBox_device;
    QSpacerItem *horizontalSpacer_4;
    QLabel *label_err_msg;
    QPushButton *pushButton_model_change;
    QSpacerItem *horizontalSpacer_3;
    QPushButton *pushButton_reconnect;
    QSpacerItem *horizontalSpacer_2;
    QPushButton *pushButton_start;
    QSpacerItem *horizontalSpacer_5;
    QPushButton *pushButton_filter;
    QWidget *widget;
    QHBoxLayout *horizontalLayout;
    QWidget *widget_channel_chose;
    QWidget *widget_focus;

    void setupUi(QWidget *monitor)
    {
        if (monitor->objectName().isEmpty())
            monitor->setObjectName(QStringLiteral("monitor"));
        monitor->resize(805, 623);
        verticalLayout_3 = new QVBoxLayout(monitor);
        verticalLayout_3->setObjectName(QStringLiteral("verticalLayout_3"));
        widget_monitor_box = new QWidget(monitor);
        widget_monitor_box->setObjectName(QStringLiteral("widget_monitor_box"));
        widget_monitor_box->setMaximumSize(QSize(16777215, 70));
        widget_monitor_box->setStyleSheet(QStringLiteral("background:#fff;"));
        verticalLayout_2 = new QVBoxLayout(widget_monitor_box);
        verticalLayout_2->setObjectName(QStringLiteral("verticalLayout_2"));
        widget_tool = new QWidget(widget_monitor_box);
        widget_tool->setObjectName(QStringLiteral("widget_tool"));
        widget_tool->setMinimumSize(QSize(0, 40));
        widget_tool->setMaximumSize(QSize(16777215, 40));
        horizontalLayout_4 = new QHBoxLayout(widget_tool);
        horizontalLayout_4->setSpacing(0);
        horizontalLayout_4->setObjectName(QStringLiteral("horizontalLayout_4"));
        horizontalLayout_4->setContentsMargins(10, 0, 10, 0);
        label_device = new QLabel(widget_tool);
        label_device->setObjectName(QStringLiteral("label_device"));
        QFont font;
        font.setFamily(QStringLiteral("Microsoft YaHei UI"));
        label_device->setFont(font);

        horizontalLayout_4->addWidget(label_device);

        comboBox_device = new QComboBox(widget_tool);
        comboBox_device->setObjectName(QStringLiteral("comboBox_device"));
        comboBox_device->setMinimumSize(QSize(150, 24));
        comboBox_device->setFont(font);

        horizontalLayout_4->addWidget(comboBox_device);

        horizontalSpacer_4 = new QSpacerItem(10, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer_4);

        label_err_msg = new QLabel(widget_tool);
        label_err_msg->setObjectName(QStringLiteral("label_err_msg"));
        label_err_msg->setFont(font);
        label_err_msg->setStyleSheet(QStringLiteral("color:red;"));

        horizontalLayout_4->addWidget(label_err_msg);

        pushButton_model_change = new QPushButton(widget_tool);
        pushButton_model_change->setObjectName(QStringLiteral("pushButton_model_change"));
        pushButton_model_change->setMinimumSize(QSize(0, 24));

        horizontalLayout_4->addWidget(pushButton_model_change);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer_3);

        pushButton_reconnect = new QPushButton(widget_tool);
        pushButton_reconnect->setObjectName(QStringLiteral("pushButton_reconnect"));
        pushButton_reconnect->setMinimumSize(QSize(60, 24));
        pushButton_reconnect->setFont(font);
        pushButton_reconnect->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#ccc;\n"
"	border:none;\n"
"	color:#000;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#bbb;\n"
"}"));

        horizontalLayout_4->addWidget(pushButton_reconnect);

        horizontalSpacer_2 = new QSpacerItem(10, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer_2);

        pushButton_start = new QPushButton(widget_tool);
        pushButton_start->setObjectName(QStringLiteral("pushButton_start"));
        pushButton_start->setMinimumSize(QSize(60, 24));
        pushButton_start->setFont(font);
        pushButton_start->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#ccc;\n"
"	border:none;\n"
"	color:#000;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#bbb;\n"
"}"));

        horizontalLayout_4->addWidget(pushButton_start);

        horizontalSpacer_5 = new QSpacerItem(10, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer_5);

        pushButton_filter = new QPushButton(widget_tool);
        pushButton_filter->setObjectName(QStringLiteral("pushButton_filter"));
        pushButton_filter->setMinimumSize(QSize(60, 24));
        pushButton_filter->setFont(font);
        pushButton_filter->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#ccc;\n"
"	border:none;\n"
"	color:#000;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#bbb;\n"
"}"));

        horizontalLayout_4->addWidget(pushButton_filter);


        verticalLayout_2->addWidget(widget_tool);


        verticalLayout_3->addWidget(widget_monitor_box);

        widget = new QWidget(monitor);
        widget->setObjectName(QStringLiteral("widget"));
        horizontalLayout = new QHBoxLayout(widget);
        horizontalLayout->setObjectName(QStringLiteral("horizontalLayout"));
        widget_channel_chose = new QWidget(widget);
        widget_channel_chose->setObjectName(QStringLiteral("widget_channel_chose"));
        widget_channel_chose->setMaximumSize(QSize(16777215, 16777215));

        horizontalLayout->addWidget(widget_channel_chose);

        widget_focus = new QWidget(widget);
        widget_focus->setObjectName(QStringLiteral("widget_focus"));

        horizontalLayout->addWidget(widget_focus);


        verticalLayout_3->addWidget(widget);


        retranslateUi(monitor);

        QMetaObject::connectSlotsByName(monitor);
    } // setupUi

    void retranslateUi(QWidget *monitor)
    {
        monitor->setWindowTitle(QApplication::translate("monitor", "\345\256\236\346\227\266\347\233\221\346\216\247", Q_NULLPTR));
        label_device->setText(QApplication::translate("monitor", "\351\200\211\346\213\251\350\256\276\345\244\207\357\274\232", Q_NULLPTR));
        label_err_msg->setText(QString());
        pushButton_model_change->setText(QApplication::translate("monitor", "\346\200\273\350\247\210", Q_NULLPTR));
        pushButton_reconnect->setText(QApplication::translate("monitor", "\351\207\215\350\277\236\350\256\276\345\244\207", Q_NULLPTR));
        pushButton_start->setText(QApplication::translate("monitor", "\345\274\200\345\247\213\351\207\207\351\233\206", Q_NULLPTR));
        pushButton_filter->setText(QApplication::translate("monitor", "\345\274\200\345\220\257\346\273\244\346\263\242", Q_NULLPTR));
    } // retranslateUi

};

namespace Ui {
    class monitor: public Ui_monitor {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MONITOR_H
