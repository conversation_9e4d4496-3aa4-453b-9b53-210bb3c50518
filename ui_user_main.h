/********************************************************************************
** Form generated from reading UI file 'user_main.ui'
**
** Created by: Qt User Interface Compiler version 5.9.8
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_USER_MAIN_H
#define UI_USER_MAIN_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QStackedWidget>
#include <QtWidgets/QToolButton>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_User_main
{
public:
    QHBoxLayout *horizontalLayout_3;
    QWidget *widget_main;
    QVBoxLayout *verticalLayout;
    QWidget *widget_tab_box;
    QHBoxLayout *horizontalLayout;
    QToolButton *toolButton_user_list;
    QToolButton *toolButton_reset;
    QSpacerItem *horizontalSpacer;
    QLabel *label_user;
    QSpacerItem *horizontalSpacer_2;
    QPushButton *pushButton_logout;
    QWidget *widget_con;
    QHBoxLayout *horizontalLayout_2;
    QStackedWidget *stackedWidget_main;
    QWidget *page_1;
    QHBoxLayout *horizontalLayout_5;
    QWidget *widget_monitor_box;
    QWidget *page_2;

    void setupUi(QWidget *User_main)
    {
        if (User_main->objectName().isEmpty())
            User_main->setObjectName(QStringLiteral("User_main"));
        User_main->resize(900, 500);
        horizontalLayout_3 = new QHBoxLayout(User_main);
        horizontalLayout_3->setObjectName(QStringLiteral("horizontalLayout_3"));
        horizontalLayout_3->setContentsMargins(0, 0, 0, 0);
        widget_main = new QWidget(User_main);
        widget_main->setObjectName(QStringLiteral("widget_main"));
        widget_main->setStyleSheet(QStringLiteral("background:#fff;"));
        verticalLayout = new QVBoxLayout(widget_main);
        verticalLayout->setSpacing(10);
        verticalLayout->setObjectName(QStringLiteral("verticalLayout"));
        verticalLayout->setContentsMargins(10, 10, 10, 10);
        widget_tab_box = new QWidget(widget_main);
        widget_tab_box->setObjectName(QStringLiteral("widget_tab_box"));
        widget_tab_box->setMinimumSize(QSize(0, 30));
        widget_tab_box->setStyleSheet(QStringLiteral("background:#f0f0f0;"));
        horizontalLayout = new QHBoxLayout(widget_tab_box);
        horizontalLayout->setSpacing(10);
        horizontalLayout->setObjectName(QStringLiteral("horizontalLayout"));
        horizontalLayout->setContentsMargins(0, 0, 0, 0);
        toolButton_user_list = new QToolButton(widget_tab_box);
        toolButton_user_list->setObjectName(QStringLiteral("toolButton_user_list"));
        toolButton_user_list->setMinimumSize(QSize(80, 24));
        QFont font;
        font.setFamily(QStringLiteral("Microsoft YaHei UI"));
        font.setPointSize(10);
        font.setBold(true);
        font.setWeight(75);
        toolButton_user_list->setFont(font);
        toolButton_user_list->setStyleSheet(QLatin1String("QToolButton{\n"
"	background:none;\n"
"	border:none;\n"
"	color:#1281E8;\n"
"}"));

        horizontalLayout->addWidget(toolButton_user_list);

        toolButton_reset = new QToolButton(widget_tab_box);
        toolButton_reset->setObjectName(QStringLiteral("toolButton_reset"));
        toolButton_reset->setMinimumSize(QSize(80, 24));
        toolButton_reset->setFont(font);
        toolButton_reset->setStyleSheet(QLatin1String("QToolButton{\n"
"	background:none;\n"
"	border:none;\n"
"	color:#666;\n"
"}\n"
"QToolButton:hover{\n"
"	color:#1281E8;\n"
"}"));

        horizontalLayout->addWidget(toolButton_reset);

        horizontalSpacer = new QSpacerItem(769, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer);

        label_user = new QLabel(widget_tab_box);
        label_user->setObjectName(QStringLiteral("label_user"));
        QFont font1;
        font1.setFamily(QStringLiteral("Microsoft YaHei UI"));
        label_user->setFont(font1);

        horizontalLayout->addWidget(label_user);

        horizontalSpacer_2 = new QSpacerItem(10, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_2);

        pushButton_logout = new QPushButton(widget_tab_box);
        pushButton_logout->setObjectName(QStringLiteral("pushButton_logout"));
        pushButton_logout->setMinimumSize(QSize(80, 24));
        QFont font2;
        font2.setFamily(QStringLiteral("Microsoft YaHei UI"));
        font2.setPointSize(9);
        pushButton_logout->setFont(font2);
        pushButton_logout->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#ccc;\n"
"	border:none;\n"
"	color:#000;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#bbb;\n"
"}"));

        horizontalLayout->addWidget(pushButton_logout);


        verticalLayout->addWidget(widget_tab_box);

        widget_con = new QWidget(widget_main);
        widget_con->setObjectName(QStringLiteral("widget_con"));
        horizontalLayout_2 = new QHBoxLayout(widget_con);
        horizontalLayout_2->setSpacing(0);
        horizontalLayout_2->setObjectName(QStringLiteral("horizontalLayout_2"));
        horizontalLayout_2->setContentsMargins(0, 0, 0, 0);
        stackedWidget_main = new QStackedWidget(widget_con);
        stackedWidget_main->setObjectName(QStringLiteral("stackedWidget_main"));
        page_1 = new QWidget();
        page_1->setObjectName(QStringLiteral("page_1"));
        horizontalLayout_5 = new QHBoxLayout(page_1);
        horizontalLayout_5->setSpacing(0);
        horizontalLayout_5->setObjectName(QStringLiteral("horizontalLayout_5"));
        horizontalLayout_5->setContentsMargins(0, 0, 0, 0);
        widget_monitor_box = new QWidget(page_1);
        widget_monitor_box->setObjectName(QStringLiteral("widget_monitor_box"));

        horizontalLayout_5->addWidget(widget_monitor_box);

        stackedWidget_main->addWidget(page_1);
        page_2 = new QWidget();
        page_2->setObjectName(QStringLiteral("page_2"));
        stackedWidget_main->addWidget(page_2);

        horizontalLayout_2->addWidget(stackedWidget_main);


        verticalLayout->addWidget(widget_con);


        horizontalLayout_3->addWidget(widget_main);


        retranslateUi(User_main);

        stackedWidget_main->setCurrentIndex(0);


        QMetaObject::connectSlotsByName(User_main);
    } // setupUi

    void retranslateUi(QWidget *User_main)
    {
        User_main->setWindowTitle(QApplication::translate("User_main", "\347\224\250\346\210\267\344\270\255\345\277\203", Q_NULLPTR));
        toolButton_user_list->setText(QApplication::translate("User_main", "\347\224\250\346\210\267\347\256\241\347\220\206", Q_NULLPTR));
        toolButton_reset->setText(QApplication::translate("User_main", "\351\207\215\347\275\256\345\257\206\347\240\201", Q_NULLPTR));
        label_user->setText(QApplication::translate("User_main", "\347\231\273\345\275\225\347\224\250\346\210\267\357\274\232", Q_NULLPTR));
        pushButton_logout->setText(QApplication::translate("User_main", "\351\200\200\345\207\272\347\231\273\345\275\225", Q_NULLPTR));
    } // retranslateUi

};

namespace Ui {
    class User_main: public Ui_User_main {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_USER_MAIN_H
