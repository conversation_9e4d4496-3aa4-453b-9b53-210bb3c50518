/********************************************************************************
** Form generated from reading UI file 'user_list.ui'
**
** Created by: Qt User Interface Compiler version 5.9.8
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_USER_LIST_H
#define UI_USER_LIST_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_user_list
{
public:
    QHBoxLayout *horizontalLayout_4;
    QWidget *widget_main;
    QVBoxLayout *verticalLayout;
    QWidget *widget_button_box;
    QHBoxLayout *horizontalLayout_2;
    QPushButton *pushButton_add;
    QPushButton *pushButton_edit;
    QPushButton *pushButton_delete;
    QSpacerItem *horizontalSpacer;
    QLineEdit *lineEdit_username;
    QPushButton *pushButton_search;
    QWidget *widget_table;
    QHBoxLayout *horizontalLayout;
    QTableWidget *tableWidget;

    void setupUi(QWidget *user_list)
    {
        if (user_list->objectName().isEmpty())
            user_list->setObjectName(QStringLiteral("user_list"));
        user_list->resize(900, 500);
        horizontalLayout_4 = new QHBoxLayout(user_list);
        horizontalLayout_4->setSpacing(0);
        horizontalLayout_4->setObjectName(QStringLiteral("horizontalLayout_4"));
        horizontalLayout_4->setContentsMargins(0, 0, 0, 0);
        widget_main = new QWidget(user_list);
        widget_main->setObjectName(QStringLiteral("widget_main"));
        widget_main->setStyleSheet(QStringLiteral("background:#fff;"));
        verticalLayout = new QVBoxLayout(widget_main);
        verticalLayout->setSpacing(10);
        verticalLayout->setObjectName(QStringLiteral("verticalLayout"));
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        widget_button_box = new QWidget(widget_main);
        widget_button_box->setObjectName(QStringLiteral("widget_button_box"));
        widget_button_box->setMinimumSize(QSize(0, 30));
        horizontalLayout_2 = new QHBoxLayout(widget_button_box);
        horizontalLayout_2->setSpacing(10);
        horizontalLayout_2->setObjectName(QStringLiteral("horizontalLayout_2"));
        horizontalLayout_2->setContentsMargins(0, 0, 0, 0);
        pushButton_add = new QPushButton(widget_button_box);
        pushButton_add->setObjectName(QStringLiteral("pushButton_add"));
        pushButton_add->setMinimumSize(QSize(80, 24));
        QFont font;
        font.setFamily(QStringLiteral("Microsoft YaHei UI"));
        pushButton_add->setFont(font);
        pushButton_add->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#1890FF;\n"
"	border:none;\n"
"	color:#fafafa;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#1281E8;\n"
"}"));

        horizontalLayout_2->addWidget(pushButton_add);

        pushButton_edit = new QPushButton(widget_button_box);
        pushButton_edit->setObjectName(QStringLiteral("pushButton_edit"));
        pushButton_edit->setMinimumSize(QSize(80, 24));
        pushButton_edit->setFont(font);
        pushButton_edit->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#ccc;\n"
"	border:none;\n"
"	color:#000;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#bbb;\n"
"}"));

        horizontalLayout_2->addWidget(pushButton_edit);

        pushButton_delete = new QPushButton(widget_button_box);
        pushButton_delete->setObjectName(QStringLiteral("pushButton_delete"));
        pushButton_delete->setMinimumSize(QSize(80, 24));
        pushButton_delete->setFont(font);
        pushButton_delete->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#ccc;\n"
"	border:none;\n"
"	color:#000;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#bbb;\n"
"}"));

        horizontalLayout_2->addWidget(pushButton_delete);

        horizontalSpacer = new QSpacerItem(94, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer);

        lineEdit_username = new QLineEdit(widget_button_box);
        lineEdit_username->setObjectName(QStringLiteral("lineEdit_username"));
        lineEdit_username->setMinimumSize(QSize(240, 26));
        lineEdit_username->setMaximumSize(QSize(240, 16777215));
        lineEdit_username->setFont(font);
        lineEdit_username->setStyleSheet(QLatin1String("background:none;\n"
"border:1px solid #bbb;\n"
"border-radius:2px;\n"
"color:#333;"));

        horizontalLayout_2->addWidget(lineEdit_username);

        pushButton_search = new QPushButton(widget_button_box);
        pushButton_search->setObjectName(QStringLiteral("pushButton_search"));
        pushButton_search->setMinimumSize(QSize(60, 24));
        pushButton_search->setFont(font);
        pushButton_search->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#1890FF;\n"
"	border:none;\n"
"	color:#fafafa;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#1281E8;\n"
"}"));

        horizontalLayout_2->addWidget(pushButton_search);


        verticalLayout->addWidget(widget_button_box);

        widget_table = new QWidget(widget_main);
        widget_table->setObjectName(QStringLiteral("widget_table"));
        horizontalLayout = new QHBoxLayout(widget_table);
        horizontalLayout->setSpacing(0);
        horizontalLayout->setObjectName(QStringLiteral("horizontalLayout"));
        horizontalLayout->setContentsMargins(0, 0, 0, 0);
        tableWidget = new QTableWidget(widget_table);
        if (tableWidget->columnCount() < 5)
            tableWidget->setColumnCount(5);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        __qtablewidgetitem->setFont(font);
        tableWidget->setHorizontalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        __qtablewidgetitem1->setFont(font);
        tableWidget->setHorizontalHeaderItem(1, __qtablewidgetitem1);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        __qtablewidgetitem2->setFont(font);
        tableWidget->setHorizontalHeaderItem(2, __qtablewidgetitem2);
        QTableWidgetItem *__qtablewidgetitem3 = new QTableWidgetItem();
        __qtablewidgetitem3->setFont(font);
        tableWidget->setHorizontalHeaderItem(3, __qtablewidgetitem3);
        QTableWidgetItem *__qtablewidgetitem4 = new QTableWidgetItem();
        __qtablewidgetitem4->setFont(font);
        tableWidget->setHorizontalHeaderItem(4, __qtablewidgetitem4);
        tableWidget->setObjectName(QStringLiteral("tableWidget"));
        tableWidget->setAcceptDrops(false);
        tableWidget->setLayoutDirection(Qt::LeftToRight);
        tableWidget->setAutoFillBackground(true);
        tableWidget->setStyleSheet(QLatin1String("QTableWidget {\n"
"	border:1px solid #ddd;\n"
"}\n"
"QHeaderView {\n"
"    background:transparent;\n"
"}\n"
"QHeaderView::section{\n"
"	height:26px;\n"
"    color:#111;\n"
"    background:#eee;\n"
"	border: none;\n"
"	border-right:1px solid #ddd;\n"
"	border-bottom:1px solid #ddd;\n"
"}\n"
"QTableWidget::item {\n"
"	border:none;\n"
"	border-bottom:1px solid #EEF1F7;\n"
"}\n"
"QTableWidget::item::selected {\n"
"	color:#fff;     \n"
"	background:#1890FF;\n"
"}"));
        tableWidget->setTextElideMode(Qt::ElideLeft);
        tableWidget->setVerticalScrollMode(QAbstractItemView::ScrollPerItem);
        tableWidget->horizontalHeader()->setCascadingSectionResizes(false);
        tableWidget->horizontalHeader()->setDefaultSectionSize(150);
        tableWidget->horizontalHeader()->setMinimumSectionSize(0);
        tableWidget->horizontalHeader()->setProperty("showSortIndicator", QVariant(false));
        tableWidget->horizontalHeader()->setStretchLastSection(true);
        tableWidget->verticalHeader()->setDefaultSectionSize(30);
        tableWidget->verticalHeader()->setMinimumSectionSize(25);
        tableWidget->verticalHeader()->setStretchLastSection(false);

        horizontalLayout->addWidget(tableWidget);


        verticalLayout->addWidget(widget_table);


        horizontalLayout_4->addWidget(widget_main);


        retranslateUi(user_list);

        QMetaObject::connectSlotsByName(user_list);
    } // setupUi

    void retranslateUi(QWidget *user_list)
    {
        user_list->setWindowTitle(QApplication::translate("user_list", "\347\224\250\346\210\267\345\210\227\350\241\250", Q_NULLPTR));
        pushButton_add->setText(QApplication::translate("user_list", "\346\226\260\345\242\236\347\224\250\346\210\267", Q_NULLPTR));
        pushButton_edit->setText(QApplication::translate("user_list", "\347\274\226\350\276\221\347\224\250\346\210\267", Q_NULLPTR));
        pushButton_delete->setText(QApplication::translate("user_list", "\345\210\240\351\231\244\347\224\250\346\210\267", Q_NULLPTR));
        lineEdit_username->setText(QString());
        lineEdit_username->setPlaceholderText(QApplication::translate("user_list", "\350\257\267\350\276\223\345\205\245\350\264\246\345\217\267", Q_NULLPTR));
        pushButton_search->setText(QApplication::translate("user_list", "\346\237\245\350\257\242", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem = tableWidget->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QApplication::translate("user_list", "\350\264\246\345\217\267", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem1 = tableWidget->horizontalHeaderItem(1);
        ___qtablewidgetitem1->setText(QApplication::translate("user_list", "\345\247\223\345\220\215", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem2 = tableWidget->horizontalHeaderItem(2);
        ___qtablewidgetitem2->setText(QApplication::translate("user_list", "\347\224\250\346\210\267\347\273\204", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem3 = tableWidget->horizontalHeaderItem(3);
        ___qtablewidgetitem3->setText(QApplication::translate("user_list", "\347\224\250\346\210\267\347\212\266\346\200\201", Q_NULLPTR));
        QTableWidgetItem *___qtablewidgetitem4 = tableWidget->horizontalHeaderItem(4);
        ___qtablewidgetitem4->setText(QApplication::translate("user_list", "\345\210\233\345\273\272\346\227\266\351\227\264", Q_NULLPTR));
    } // retranslateUi

};

namespace Ui {
    class user_list: public Ui_user_list {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_USER_LIST_H
