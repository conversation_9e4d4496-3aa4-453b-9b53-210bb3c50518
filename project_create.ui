<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>project_create</class>
 <widget class="QWidget" name="project_create">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1046</width>
    <height>576</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3">
   <item>
    <widget class="QWidget" name="widget_main" native="true">
     <property name="baseSize">
      <size>
       <width>968</width>
       <height>571</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">background:#fff;</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <widget class="QGroupBox" name="groupBox_project">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>200</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>200</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Microsoft YaHei UI</family>
         </font>
        </property>
        <property name="title">
         <string>项目信息</string>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_2">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>6</number>
         </property>
         <property name="topMargin">
          <number>6</number>
         </property>
         <property name="rightMargin">
          <number>6</number>
         </property>
         <property name="bottomMargin">
          <number>6</number>
         </property>
         <item>
          <widget class="QWidget" name="widget" native="true">
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>40</height>
            </size>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_3">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QLabel" name="label">
              <property name="styleSheet">
               <string notr="true">color:red;</string>
              </property>
              <property name="text">
               <string>*</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="label_name">
              <property name="minimumSize">
               <size>
                <width>64</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color:#333;</string>
              </property>
              <property name="text">
               <string>项目名称：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="lineEdit_name">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>24</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background:none;
border:1px solid #bbb;
border-radius:3px;
color:#333;</string>
              </property>
              <property name="placeholderText">
               <string>请输入项目名称</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QWidget" name="widget_5" native="true">
           <property name="maximumSize">
            <size>
             <width>1000000</width>
             <height>40</height>
            </size>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_6">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QLabel" name="label_details">
              <property name="minimumSize">
               <size>
                <width>70</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>100</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color:#333;</string>
              </property>
              <property name="text">
               <string>项目描述：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QTextEdit" name="textEdit_description">
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>40</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background:none;
border:1px solid #bbb;
border-radius:3px;
color:#333;</string>
              </property>
              <property name="html">
               <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'Microsoft YaHei UI'; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot;-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;br /&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QWidget" name="widget_4" native="true">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>50</height>
            </size>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_4">
            <item>
             <widget class="QLabel" name="label_2">
              <property name="styleSheet">
               <string notr="true">color:red;</string>
              </property>
              <property name="text">
               <string>*</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="label_path">
              <property name="minimumSize">
               <size>
                <width>64</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color:#333;</string>
              </property>
              <property name="text">
               <string>存储路径：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="lineEdit_path">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>24</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>200</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
               </font>
              </property>
              <property name="layoutDirection">
               <enum>Qt::LeftToRight</enum>
              </property>
              <property name="styleSheet">
               <string notr="true">background:none;
border:1px solid #bbb;
border-radius:3px;
color:#333;</string>
              </property>
              <property name="placeholderText">
               <string>请选择存储路径</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="pushButton_path">
              <property name="minimumSize">
               <size>
                <width>60</width>
                <height>24</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>60</width>
                <height>200</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="layoutDirection">
               <enum>Qt::LeftToRight</enum>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton{
	background:#1890FF;
	border:none;
	color:#fafafa;
	border-radius:2px;
}
QPushButton:hover{
	background:#1281E8;
}</string>
              </property>
              <property name="text">
               <string>选择</string>
              </property>
              <property name="iconSize">
               <size>
                <width>12</width>
                <height>12</height>
               </size>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_7">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeType">
               <enum>QSizePolicy::Fixed</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>10</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QLabel" name="label_speed">
              <property name="minimumSize">
               <size>
                <width>75</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color:#333;</string>
              </property>
              <property name="text">
               <string>速度(米/秒)：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="lineEdit_speed">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>24</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background:none;
border:1px solid #bbb;
border-radius:3px;
color:#333;</string>
              </property>
              <property name="placeholderText">
               <string>请输入速度</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_6">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>60</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QLabel" name="label_supervisor">
              <property name="minimumSize">
               <size>
                <width>70</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color:#333;</string>
              </property>
              <property name="text">
               <string>监制单位：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="label_speed_unit">
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color:#333;</string>
              </property>
              <property name="text">
               <string/>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="lineEdit_company">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>24</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background:none;
border:1px solid #bbb;
border-radius:3px;
color:#333;</string>
              </property>
              <property name="placeholderText">
               <string>请输入监制单位</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_9">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QLabel" name="label_operator">
              <property name="minimumSize">
               <size>
                <width>75</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color:#333;</string>
              </property>
              <property name="text">
               <string>操作人员：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="lineEdit_operator">
              <property name="enabled">
               <bool>false</bool>
              </property>
              <property name="minimumSize">
               <size>
                <width>200</width>
                <height>24</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>176</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">background:none;
border:1px solid #bbb;
border-radius:3px;
color:#333;</string>
              </property>
              <property name="placeholderText">
               <string>请输入操作人员</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QGroupBox" name="groupBox_sensor">
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Microsoft YaHei UI</family>
         </font>
        </property>
        <property name="title">
         <string>检波器参数</string>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_4">
         <item>
          <widget class="QWidget" name="widget_6" native="true">
           <layout class="QHBoxLayout" name="horizontalLayout_7">
            <item>
             <widget class="QLabel" name="label_4">
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
               </font>
              </property>
              <property name="text">
               <string>板卡数量：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="lineEdit_divice_number">
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
                <pointsize>9</pointsize>
               </font>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_4">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QLabel" name="label_3">
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
               </font>
              </property>
              <property name="text">
               <string> 板卡选择：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="comboBox_device_choose">
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
               </font>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_5">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QLabel" name="label_5">
              <property name="font">
               <font>
                <family>Microsoft YaHei</family>
               </font>
              </property>
              <property name="text">
               <string> 板卡IP：</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="lineEdit_device_IP">
              <property name="font">
               <font>
                <family>Microsoft YaHei UI</family>
               </font>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QWidget" name="widget_7" native="true">
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>16777215</height>
            </size>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout">
            <item>
             <widget class="QTableWidget" name="tableWidget_sensor_parameter">
              <column>
               <property name="text">
                <string>通道号</string>
               </property>
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                </font>
               </property>
              </column>
              <column>
               <property name="text">
                <string>采样率</string>
               </property>
              </column>
              <column>
               <property name="text">
                <string>通道使用状态</string>
               </property>
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                </font>
               </property>
              </column>
              <column>
               <property name="text">
                <string>恒流直流电源</string>
               </property>
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                </font>
               </property>
              </column>
              <column>
               <property name="text">
                <string>AD最大等待时间</string>
               </property>
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                </font>
               </property>
              </column>
              <column>
               <property name="text">
                <string>缓冲区大小</string>
               </property>
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                </font>
               </property>
              </column>
              <column>
               <property name="text">
                <string>是否使用校准</string>
               </property>
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                </font>
               </property>
              </column>
              <column>
               <property name="text">
                <string>是否返回时间戳</string>
               </property>
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                </font>
               </property>
              </column>
              <column>
               <property name="text">
                <string>传输通路(TCP/UDP)</string>
               </property>
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                </font>
               </property>
              </column>
              <column>
               <property name="text">
                <string>AD启动方式</string>
               </property>
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                </font>
               </property>
              </column>
              <column>
               <property name="text">
                <string>X坐标</string>
               </property>
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                </font>
               </property>
              </column>
              <column>
               <property name="text">
                <string>Y坐标</string>
               </property>
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                </font>
               </property>
              </column>
              <column>
               <property name="text">
                <string>Z坐标</string>
               </property>
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                </font>
               </property>
              </column>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="widget_button_box" native="true">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>40</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>50</height>
         </size>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>127</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_save">
           <property name="minimumSize">
            <size>
             <width>80</width>
             <height>28</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>9</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{
	background:#1890FF;
	border:none;
	color:#fafafa;
	border-radius:2px;
}
QPushButton:hover{
	background:#1281E8;
}</string>
           </property>
           <property name="text">
            <string>确定</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Minimum</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_cancel">
           <property name="minimumSize">
            <size>
             <width>80</width>
             <height>28</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Microsoft YaHei UI</family>
             <pointsize>9</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{
	background:#ccc;
	border:none;
	color:#000;
	border-radius:2px;
}
QPushButton:hover{
	background:#bbb;
}</string>
           </property>
           <property name="text">
            <string>取消</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_3">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>127</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
