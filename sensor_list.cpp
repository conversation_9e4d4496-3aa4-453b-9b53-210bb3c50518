#include "sensor_list.h"
#include "ui_sensor_list.h"
#include "message.h"

#include <QTableWidget>
#include <QSqlQuery>
#include <QSqlError>
#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QCheckBox>
#include <QHBoxLayout>
#include <QMessageBox>

#include <QTableWidget>
#include <QLineEdit>
#include <QComboBox>
#include <QCheckBox>
#include <QDoubleSpinBox>
#include <QSpinBox>

sensor_list::sensor_list(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::sensor_list)
{
    ui->setupUi(this);
    this->ui->tableWidget_chanel->setColumnWidth(0,100);
    this->ui->tableWidget_chanel->setColumnWidth(1,100);
    this->ui->tableWidget_chanel->setColumnWidth(2,100);
    this->ui->tableWidget_chanel->setColumnWidth(3,200);
    this->ui->tableWidget_chanel->setColumnWidth(4,200);
    this->ui->tableWidget_chanel->setColumnWidth(5,150);
    this->ui->tableWidget_chanel->setColumnWidth(6,150);
    this->ui->tableWidget_chanel->setColumnWidth(7,150);
    this->ui->tableWidget_chanel->setColumnWidth(8,150);
    this->ui->tableWidget_chanel->setColumnWidth(9,200);
    this->ui->tableWidget_chanel->setColumnWidth(10,150);
    this->ui->tableWidget_chanel->setColumnWidth(11,100);
    this->ui->tableWidget_chanel->setColumnWidth(12,100);
    this->ui->tableWidget_chanel->setColumnWidth(13,100);

    this->ui->tableWidget_sensor->setColumnWidth(0,70);
    this->ui->tableWidget_sensor->setColumnWidth(1,80);
    this->ui->tableWidget_sensor->setColumnWidth(2,70);
    this->ui->tableWidget_sensor->setColumnWidth(3,150);

    this->ui->tableWidget_device_IP->setColumnWidth(0,180);

    this->ui->tableWidget_device_IP->verticalHeader()->setVisible(false);

    this->ui->lineEdit_devic_count->setText(QString::number(nums));

    this->on_pushButton_reload_clicked();

    static QString styleSheet = R"(
    /* 主窗口整体样式 */
    QMainWindow {
        background-color: #f5f7fa; /* 浅灰蓝色背景，营造清爽氛围 */
    }

    /* 中心部件，作为基础容器 */
    QWidget#centralwidget {
        background-color: transparent; /* 透明，让 stackedWidget 等自己控制背景 */
    }

    /* 栈式窗口，用于页面切换的容器 */
    QStackedWidget {
        background-color: white;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 10px;
        margin: 8px; /* 与主窗口边缘保持间距 */
    }

    /* 栈式窗口里的页面，可给不同页面差异化设置，这里统一先简单处理 */
    QWidget#page, QWidget#page_2 {
        background-color: white;
        border-radius: 6px;
        padding: 12px;
    }

    /* 若页面里有其他控件（如按钮、标签等，可提前预设通用样式，后续叠加） */
    QLabel {
        color: #333333;
        font-size: 20px;
    }

    QLineEdit {
        color: #333333;
        font-size: 20px;
    }

    QComBox {
        color: #333333;
        font-size: 20px;
    }

    QPushButton {
        background-color: #409eff; /* 经典主题蓝色 */
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 16px;
        margin: 4px;
    }
    QPushButton:hover {
        background-color: #66b1ff; /* 悬停变浅蓝 */
    }
    QPushButton:pressed {
        background-color: #337ab7; /* 按下加深 */
    }

    )";
    this->setObjectName("sensor_list");
    this->setStyleSheet(styleSheet);

    QString style ="QTableWidget {"
                "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f9f9f9, stop:1 #f0f0f0);"
                "    gridline-color: #e0e0e0;"
                "    border: 1px solid #d0d0d0;"
                "    border-radius: 4px;"
                "    font-family: 'Microsoft YaHei UI', sans-serif;"
                "}"
                "QTableWidget::item {"
                "    color: #333;"
                "    background-color: white;"
                "    border: none;"
                "    padding: 6px;"
                "}"
                "QTableWidget::item:alternate {"
                "    background-color: #f9f9f9;" // 交替行背景
                "}"
                "QTableWidget::item:hover {"
                "    background-color: #f0f7ff;" // 鼠标悬停背景
                "}"
                "QTableWidget::item:selected {"
                "    background-color: #c2e0ff;"
                "    color: #000;"
                "}"
                "QHeaderView::section {"
                "    background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #e6f2ff, stop:1 #d9e6ff);"
                "    color: #222;"
                "    border: 1px solid #bbd6ff;"
                "    border-bottom: 2px solid #a3c6ff;"
                "    padding: 6px 12px;"
                "    font-weight: bold;"
                "    text-align: center;"
                "}"
                "QHeaderView::section:horizontal {"
                "    border-right: 2px solid #a3c6ff;" // 水平表头右侧边框
                "}"
                "QHeaderView::section:vertical {"
                "    border-right: 2px solid #a3c6ff;" // 垂直表头下边框
                "}"

                "QPushButton {"
                "    font-family: 'Microsoft YaHei UI', sans-serif;"
                "    background-color: #409eff;"
                "    color: white;"
                "    border: none;"
                "    border-radius: 4px;"
                "    padding: 8px 16px;"
                "    margin: 4px;"
                "}"
                "QComboBox {"
                "    font-family: 'Microsoft YaHei UI', sans-serif;"
                "    background-color: white;"
                "    border: 1px solid #ccc;"
                "    border-radius: 4px;"
                "    padding: 6px;"
                "QDoubleSpinBox {"
                "    font-family: 'Microsoft YaHei UI', sans-serif;"
                "    background-color: white;"
                "    border: 1px solid #ccc;"
                "    border-radius: 4px;"
                "    padding: 6px;"
                "QSpinBox {"
                "    font-family: 'Microsoft YaHei UI', sans-serif;"
                "    background-color: white;"
                "    border: 1px solid #ccc;"
                "    border-radius: 4px;"
                "    padding: 6px;"
                "}";


    this->ui->tableWidget_chanel->setStyleSheet(style);
    this->ui->tableWidget_sensor->setStyleSheet(style);
    this->ui->tableWidget_device_IP->setStyleSheet(style);


}

sensor_list::~sensor_list()
{
    delete ui;
}

void sensor_list::on_pushButton_clicked()
{

}


void sensor_list::on_pushButton_reload_clicked()
{
    this->load_data_project();

    this->on_comboBox_project_currentTextChanged("0");

    this->load_data_sensor(this->ui->comboBox_device_choose->itemText(0));
}

/**
 * @brief 通过IP查找要加载的检波器配置并显示在表单
 * @param IP 要加载的板卡IP
 */
void sensor_list::load_data_sensor(QString IP)
{
    int devidce_index = get_index_by_IP(IP);
    sensor_config_message[devidce_index]->class_to_tableWidget(sensor_config_message[devidce_index], this->ui->tableWidget_chanel);
}

/**
 * @brief 从数据库读取工程数据，并加载到工程表单和工程下拉框
 */
void sensor_list::load_data_project()
{
    this->ui->comboBox_project->blockSignals(true);
    this->ui->tableWidget_sensor->setRowCount(0);
    this->ui->comboBox_project->clear();
    QSqlQuery query("SELECT id, name, user_id, create_time FROM top_project;");
    if(!query.exec())
    {
        qDebug() << query.lastError() << __LINE__;
    }

    int row = 0;
    while(query.next())
    {
        this->ui->tableWidget_sensor->insertRow(this->ui->tableWidget_sensor->rowCount());
        this->ui->tableWidget_sensor->setRowHeight(row, 70);

        for (int col = 0; col < 4; ++col)
        {
            QTableWidgetItem* item = new QTableWidgetItem(query.value(col).toString());
            item->setTextAlignment(Qt::AlignCenter);
            this->ui->tableWidget_sensor->setItem(row, col, item);
        }
        this->ui->comboBox_project->addItem(query.value(0).toString());
        row++;
    }

    this->ui->comboBox_project->blockSignals(false);
}

QString old_IP;
QString new_IP;

//板卡IPcomBox修改
void sensor_list::on_comboBox_project_currentTextChanged(const QString &arg1)
{
    this->ui->comboBox_device_choose->blockSignals(true);
    //需添加全局设备数修改

    //--------------------------------------------------------------------------------------
    this->ui->tableWidget_device_IP->setRowCount(nums);
    this->ui->comboBox_device_choose->clear();

    for(int i = 0; i < nums; ++i)
    {
        //设备IPcomBox添加内容
        this->ui->comboBox_device_choose->addItem(sensor_config_message[i]->IP);
        //设备IPtableWidget添加内容
        QTableWidgetItem* item = new QTableWidgetItem(sensor_config_message[i]->IP);
        item->setTextAlignment(Qt::AlignCenter);
        this->ui->tableWidget_device_IP->blockSignals(true);
        this->ui->tableWidget_device_IP->setRowHeight(i, 70);
        this->ui->tableWidget_device_IP->setItem(i, 0, item);
        this->ui->tableWidget_device_IP->blockSignals(false);
    }
    new_IP = sensor_config_message[0]->IP;
    old_IP = new_IP;

    this->ui->comboBox_device_choose->blockSignals(false);
}

//点击保存后更新全局配置变量，发送信号更新检波器配置
void sensor_list::on_pushButton_save_clicked()
{
    for(int i = 0; i < nums; i++)
    {
        if(sensor_config_message[i]->IP == old_IP)
        {
            sensor_config_message[i]->IP = new_IP;
        }
    }
    int current_device_index = get_index_by_IP(this->ui->comboBox_device_choose->currentText());
    sensor_config_message[current_device_index]->tableWidget_to_class(sensor_config_message[current_device_index], this->ui->tableWidget_chanel, sensor_config_message[current_device_index]->IP);
    //缺少将其更新到数据库

    sensor_config_message[current_device_index]->sensor_config_save(13, "update");


    //-------------------------------------------------------------------------

   // qDebug() << "发射前"<< new_IP<< old_IP;
    emit save_project_and_sensor(new_IP, old_IP);
   // qDebug() << "发射后"<< new_IP<< old_IP;
    old_IP = new_IP;
   // qDebug() << "发射后"<< new_IP<< old_IP;
    //this->on_pushButton_reload_clicked();
}

//设备IPtableWidget点击后更新检波器配置表
void sensor_list::on_tableWidget_device_IP_clicked(const QModelIndex &index)
{
    new_IP = index.data().toString();
    old_IP = new_IP;
    load_data_sensor(index.data().toString());
    this->ui->comboBox_device_choose->setCurrentText(this->ui->tableWidget_device_IP->currentItem()->text());
}

void sensor_list::on_comboBox_device_choose_currentTextChanged(const QString &arg1)
{
    load_data_sensor(arg1);

    for(int row = 1; row < this->ui->tableWidget_device_IP->rowCount(); ++row)
    {
        if(this->ui->tableWidget_device_IP->item(row, 0)->text() == arg1)
        {
            this->ui->tableWidget_device_IP->setCurrentItem(this->ui->tableWidget_device_IP->item(row, 0));
        }
    }
}

void sensor_list::on_tableWidget_device_IP_currentItemChanged(QTableWidgetItem *current, QTableWidgetItem *previous)
{

}

/**
 * @brief IP被修改后更新新IP,并更新当前全局检波器配置 和 IP下拉框为新IP
 * @param item
 */
void sensor_list::on_tableWidget_device_IP_itemChanged(QTableWidgetItem *item)
{
    for(int i = 0; i < nums; i++)
    {
        if(sensor_config_message[i]->IP == new_IP )
        {
            sensor_config_message[i]->IP = item->text();
        }
        if(this->ui->comboBox_device_choose->itemText(i) == new_IP)
        {
             this->ui->comboBox_device_choose->setItemText(i, item->text());
        }
    }
    new_IP = item->text();
}

/**
 * @brief 双击IP表单单元格后更新旧IP和新IP为当前选中IP，因为IP的修改只是提供了双击
 * @param index
 */
void sensor_list::on_tableWidget_device_IP_doubleClicked(const QModelIndex &index)
{
    new_IP = index.data().toString();
    old_IP = new_IP;
}
