#ifndef MESSAGE_H
#define MESSAGE_H

#include <QString>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QDebug>
#include <QTableWidget>

#include "common.h"


const int channel_count = 16;

class database
{
public:
    database();
    ~database();
    void connect_database();
    void disconnect_database();

private:
    QSqlDatabase db;
};

class Project
{
public:
    int id;//工程id
    int user_id;//用户id
    QString name;//工程名
    QString description;//工程描述
    QString file_path;//工程路径
    QString company;//公司
    QString Operator;//操作员
    int status;//状态
    QString create_time;//创建时间
};


class User
{
public:
    int id;
    QString user_name;
    QString password;
    QString real_name;
    int role;
    int status;
    QString create_time;
};

//0—1倍放大， 1--2倍放大，
//2--5倍放大， 3--10倍放大]， 跳线选为电压采集时[0—±5V， 1--±2.5V， 2--±1V， 3--±0.5V]
class sensor_config
{
public:
    QString IP;

    double sampling_rate[channel_count];//采样率
//    double gain[channel_count];//增益
    double channel_isEnable[channel_count];//通道是否启用

//    double jumper_voltage[channel_count];//跳线电流//电压
    double current_source[channel_count];//配置交直流及恒流源
    double waitin_time[channel_count];//AD最大等待时间
    double buffer_size[channel_count];//缓冲区大小
    double calibration_isEnable[channel_count];//是否使用校准
    double time_stamp_isEnable[channel_count];//是否返回时间戳
    int transmission_pathway[channel_count];//传输通路
    double AD_start[channel_count];//AD启动

    double x[channel_count];//x坐标
    double y[channel_count];//y坐标
    double z[channel_count];//z坐标

    sensor_config();
    sensor_config(QString IP) : IP(IP)
    {
        for(int i = 0; i < channel_count; ++i)
        {
            this->sampling_rate[i] = 10;
//            this->gain[i] = 10;
            this->channel_isEnable[i] = 1;
//            this->jumper_voltage[i] = 0;
            this->current_source[i] = 0;
            this->waitin_time[i] = 10;

            this->buffer_size[i] = 10;
            this->calibration_isEnable[i] = 0;
            this->time_stamp_isEnable[i] = 0;
            this->transmission_pathway[i] = 1;
            this->AD_start[i] = 0;

            this->x[i] = 0;
            this->y[i] = 0;
            this->z[i] = 0;
        }
    }
    ~sensor_config();
    void sensor_config_save(int project_id, QString mode);//保存到数据库
    void sensor_config_get();//从数据库中获取

    void class_to_tableWidget(sensor_config* sensor_config_message, QTableWidget* tableWidget);
    void tableWidget_to_class(sensor_config* sensor_config_message, QTableWidget* tableWidget, QString IP);
};

void load_project_and_sensor_message_config_from_database(int project_id);
int get_index_by_IP(QString IP);
MyHANDLE MyHANDLE_init(sensor_config* config);

extern QVector<sensor_config*> sensor_config_message;

extern int nums;//设备数
extern database* db;
extern User* current_user;
extern Project* current_project;

extern sensor_config* sensor_config_message_default;



#endif // MESSAGE_H
