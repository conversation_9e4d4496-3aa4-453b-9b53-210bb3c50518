/********************************************************************************
** Form generated from reading UI file 'project_edit.ui'
**
** Created by: Qt User Interface Compiler version 5.9.8
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_PROJECT_EDIT_H
#define UI_PROJECT_EDIT_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QTreeWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_Project_edit
{
public:
    QHBoxLayout *horizontalLayout;
    QWidget *widget_main;
    QVBoxLayout *verticalLayout;
    QGroupBox *groupBox_project;
    QVBoxLayout *verticalLayout_4;
    QWidget *widget;
    QHBoxLayout *horizontalLayout_3;
    QLabel *label;
    QLabel *label_name;
    QLineEdit *lineEdit_name;
    QWidget *widget_5;
    QHBoxLayout *horizontalLayout_6;
    QLabel *label_details;
    QTextEdit *textEdit_description;
    QWidget *widget_4;
    QHBoxLayout *horizontalLayout_5;
    QLabel *label_2;
    QLabel *label_path;
    QLineEdit *lineEdit_path;
    QSpacerItem *horizontalSpacer_12;
    QPushButton *pushButton_path;
    QSpacerItem *horizontalSpacer_7;
    QLabel *label_speed;
    QLineEdit *lineEdit_speed;
    QLabel *label_speed_unit;
    QSpacerItem *horizontalSpacer_8;
    QWidget *widget_3;
    QHBoxLayout *horizontalLayout_4;
    QLabel *label_supervisor;
    QLineEdit *lineEdit_company;
    QSpacerItem *horizontalSpacer_11;
    QLabel *label_operator;
    QLineEdit *lineEdit_operator;
    QSpacerItem *horizontalSpacer_6;
    QGroupBox *groupBox_sensor;
    QVBoxLayout *verticalLayout_5;
    QWidget *widget_6;
    QHBoxLayout *horizontalLayout_7;
    QLabel *label_frequency;
    QLineEdit *lineEdit_frequency;
    QLabel *label_frequency_unit;
    QSpacerItem *horizontalSpacer_9;
    QLabel *label_sensitivity;
    QLineEdit *lineEdit_sensitivity;
    QLabel *label_sensitivity_unit;
    QSpacerItem *horizontalSpacer_10;
    QLabel *label_damping;
    QLineEdit *lineEdit_damping;
    QLabel *label_damping_unit;
    QSpacerItem *horizontalSpacer_5;
    QWidget *widget_7;
    QHBoxLayout *horizontalLayout_8;
    QTreeWidget *treeWidget;
    QWidget *widget_8;
    QHBoxLayout *horizontalLayout_9;
    QLabel *label_sensor_tip;
    QPushButton *pushButton_reload;
    QSpacerItem *horizontalSpacer_4;
    QPushButton *pushButton_sensor_setting;
    QWidget *widget_button_box;
    QHBoxLayout *horizontalLayout_2;
    QSpacerItem *horizontalSpacer_2;
    QPushButton *pushButton_save;
    QSpacerItem *horizontalSpacer;
    QPushButton *pushButton_cancel;
    QSpacerItem *horizontalSpacer_3;

    void setupUi(QWidget *Project_edit)
    {
        if (Project_edit->objectName().isEmpty())
            Project_edit->setObjectName(QStringLiteral("Project_edit"));
        Project_edit->resize(957, 584);
        horizontalLayout = new QHBoxLayout(Project_edit);
        horizontalLayout->setObjectName(QStringLiteral("horizontalLayout"));
        widget_main = new QWidget(Project_edit);
        widget_main->setObjectName(QStringLiteral("widget_main"));
        widget_main->setBaseSize(QSize(968, 571));
        widget_main->setStyleSheet(QStringLiteral("background:#fff;"));
        verticalLayout = new QVBoxLayout(widget_main);
        verticalLayout->setSpacing(10);
        verticalLayout->setObjectName(QStringLiteral("verticalLayout"));
        verticalLayout->setContentsMargins(10, 10, 10, 10);
        groupBox_project = new QGroupBox(widget_main);
        groupBox_project->setObjectName(QStringLiteral("groupBox_project"));
        groupBox_project->setMinimumSize(QSize(0, 0));
        groupBox_project->setMaximumSize(QSize(16777215, 180));
        QFont font;
        font.setFamily(QStringLiteral("Microsoft YaHei UI"));
        groupBox_project->setFont(font);
        verticalLayout_4 = new QVBoxLayout(groupBox_project);
        verticalLayout_4->setSpacing(0);
        verticalLayout_4->setObjectName(QStringLiteral("verticalLayout_4"));
        verticalLayout_4->setContentsMargins(10, 0, 10, 10);
        widget = new QWidget(groupBox_project);
        widget->setObjectName(QStringLiteral("widget"));
        widget->setMaximumSize(QSize(16777215, 16777215));
        horizontalLayout_3 = new QHBoxLayout(widget);
        horizontalLayout_3->setSpacing(0);
        horizontalLayout_3->setObjectName(QStringLiteral("horizontalLayout_3"));
        horizontalLayout_3->setContentsMargins(0, 0, 0, 0);
        label = new QLabel(widget);
        label->setObjectName(QStringLiteral("label"));
        label->setStyleSheet(QStringLiteral("color:red;"));

        horizontalLayout_3->addWidget(label);

        label_name = new QLabel(widget);
        label_name->setObjectName(QStringLiteral("label_name"));
        label_name->setMinimumSize(QSize(64, 0));
        QFont font1;
        font1.setFamily(QStringLiteral("Microsoft YaHei UI"));
        font1.setPointSize(9);
        label_name->setFont(font1);
        label_name->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_3->addWidget(label_name);

        lineEdit_name = new QLineEdit(widget);
        lineEdit_name->setObjectName(QStringLiteral("lineEdit_name"));
        lineEdit_name->setMinimumSize(QSize(0, 24));
        lineEdit_name->setFont(font);
        lineEdit_name->setStyleSheet(QLatin1String("background:none;\n"
"border:1px solid #bbb;\n"
"border-radius:3px;\n"
"color:#333;"));

        horizontalLayout_3->addWidget(lineEdit_name);


        verticalLayout_4->addWidget(widget);

        widget_5 = new QWidget(groupBox_project);
        widget_5->setObjectName(QStringLiteral("widget_5"));
        widget_5->setMaximumSize(QSize(1000000, 50));
        horizontalLayout_6 = new QHBoxLayout(widget_5);
        horizontalLayout_6->setSpacing(0);
        horizontalLayout_6->setObjectName(QStringLiteral("horizontalLayout_6"));
        horizontalLayout_6->setContentsMargins(0, 0, 0, 0);
        label_details = new QLabel(widget_5);
        label_details->setObjectName(QStringLiteral("label_details"));
        label_details->setMinimumSize(QSize(70, 0));
        label_details->setMaximumSize(QSize(16777215, 100));
        label_details->setFont(font1);
        label_details->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_6->addWidget(label_details);

        textEdit_description = new QTextEdit(widget_5);
        textEdit_description->setObjectName(QStringLiteral("textEdit_description"));
        textEdit_description->setMaximumSize(QSize(16777215, 40));
        textEdit_description->setFont(font);
        textEdit_description->setStyleSheet(QLatin1String("background:none;\n"
"border:1px solid #bbb;\n"
"border-radius:3px;\n"
"color:#333;"));

        horizontalLayout_6->addWidget(textEdit_description);


        verticalLayout_4->addWidget(widget_5);

        widget_4 = new QWidget(groupBox_project);
        widget_4->setObjectName(QStringLiteral("widget_4"));
        widget_4->setMaximumSize(QSize(16777215, 30));
        horizontalLayout_5 = new QHBoxLayout(widget_4);
        horizontalLayout_5->setSpacing(0);
        horizontalLayout_5->setObjectName(QStringLiteral("horizontalLayout_5"));
        horizontalLayout_5->setContentsMargins(0, 0, 0, 0);
        label_2 = new QLabel(widget_4);
        label_2->setObjectName(QStringLiteral("label_2"));
        label_2->setStyleSheet(QStringLiteral("color:red;"));

        horizontalLayout_5->addWidget(label_2);

        label_path = new QLabel(widget_4);
        label_path->setObjectName(QStringLiteral("label_path"));
        label_path->setMinimumSize(QSize(64, 0));
        label_path->setFont(font1);
        label_path->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_5->addWidget(label_path);

        lineEdit_path = new QLineEdit(widget_4);
        lineEdit_path->setObjectName(QStringLiteral("lineEdit_path"));
        lineEdit_path->setMinimumSize(QSize(220, 24));
        lineEdit_path->setMaximumSize(QSize(200, 16777215));
        lineEdit_path->setFont(font);
        lineEdit_path->setLayoutDirection(Qt::LeftToRight);
        lineEdit_path->setStyleSheet(QLatin1String("background:none;\n"
"border:1px solid #bbb;\n"
"border-radius:3px;\n"
"color:#333;"));

        horizontalLayout_5->addWidget(lineEdit_path);

        horizontalSpacer_12 = new QSpacerItem(15, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_5->addItem(horizontalSpacer_12);

        pushButton_path = new QPushButton(widget_4);
        pushButton_path->setObjectName(QStringLiteral("pushButton_path"));
        pushButton_path->setMinimumSize(QSize(60, 24));
        pushButton_path->setMaximumSize(QSize(60, 24));
        pushButton_path->setFont(font);
        pushButton_path->setLayoutDirection(Qt::LeftToRight);
        pushButton_path->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#1890FF;\n"
"	border:none;\n"
"	color:#fafafa;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#1281E8;\n"
"}"));
        pushButton_path->setIconSize(QSize(12, 12));

        horizontalLayout_5->addWidget(pushButton_path);

        horizontalSpacer_7 = new QSpacerItem(10, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_5->addItem(horizontalSpacer_7);

        label_speed = new QLabel(widget_4);
        label_speed->setObjectName(QStringLiteral("label_speed"));
        label_speed->setMinimumSize(QSize(75, 0));
        label_speed->setFont(font1);
        label_speed->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_5->addWidget(label_speed);

        lineEdit_speed = new QLineEdit(widget_4);
        lineEdit_speed->setObjectName(QStringLiteral("lineEdit_speed"));
        lineEdit_speed->setMinimumSize(QSize(200, 24));
        lineEdit_speed->setMaximumSize(QSize(200, 16777215));
        lineEdit_speed->setFont(font);
        lineEdit_speed->setStyleSheet(QLatin1String("background:none;\n"
"border:1px solid #bbb;\n"
"border-radius:3px;\n"
"color:#333;"));

        horizontalLayout_5->addWidget(lineEdit_speed);

        label_speed_unit = new QLabel(widget_4);
        label_speed_unit->setObjectName(QStringLiteral("label_speed_unit"));
        label_speed_unit->setFont(font1);
        label_speed_unit->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_5->addWidget(label_speed_unit);

        horizontalSpacer_8 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_5->addItem(horizontalSpacer_8);


        verticalLayout_4->addWidget(widget_4);

        widget_3 = new QWidget(groupBox_project);
        widget_3->setObjectName(QStringLiteral("widget_3"));
        widget_3->setMaximumSize(QSize(16777215, 30));
        horizontalLayout_4 = new QHBoxLayout(widget_3);
        horizontalLayout_4->setSpacing(0);
        horizontalLayout_4->setObjectName(QStringLiteral("horizontalLayout_4"));
        horizontalLayout_4->setContentsMargins(0, 0, 0, 0);
        label_supervisor = new QLabel(widget_3);
        label_supervisor->setObjectName(QStringLiteral("label_supervisor"));
        label_supervisor->setMinimumSize(QSize(70, 0));
        label_supervisor->setMaximumSize(QSize(16777215, 16777215));
        label_supervisor->setFont(font1);
        label_supervisor->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_4->addWidget(label_supervisor);

        lineEdit_company = new QLineEdit(widget_3);
        lineEdit_company->setObjectName(QStringLiteral("lineEdit_company"));
        lineEdit_company->setMinimumSize(QSize(296, 24));
        lineEdit_company->setMaximumSize(QSize(270, 16777215));
        lineEdit_company->setFont(font);
        lineEdit_company->setStyleSheet(QLatin1String("background:none;\n"
"border:1px solid #bbb;\n"
"border-radius:3px;\n"
"color:#333;"));

        horizontalLayout_4->addWidget(lineEdit_company);

        horizontalSpacer_11 = new QSpacerItem(10, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer_11);

        label_operator = new QLabel(widget_3);
        label_operator->setObjectName(QStringLiteral("label_operator"));
        label_operator->setMinimumSize(QSize(75, 0));
        label_operator->setFont(font1);
        label_operator->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_4->addWidget(label_operator);

        lineEdit_operator = new QLineEdit(widget_3);
        lineEdit_operator->setObjectName(QStringLiteral("lineEdit_operator"));
        lineEdit_operator->setEnabled(false);
        lineEdit_operator->setMinimumSize(QSize(200, 24));
        lineEdit_operator->setMaximumSize(QSize(176, 16777215));
        lineEdit_operator->setFont(font);
        lineEdit_operator->setStyleSheet(QLatin1String("background:none;\n"
"border:1px solid #bbb;\n"
"border-radius:3px;\n"
"color:#333;"));

        horizontalLayout_4->addWidget(lineEdit_operator);

        horizontalSpacer_6 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer_6);


        verticalLayout_4->addWidget(widget_3);


        verticalLayout->addWidget(groupBox_project);

        groupBox_sensor = new QGroupBox(widget_main);
        groupBox_sensor->setObjectName(QStringLiteral("groupBox_sensor"));
        groupBox_sensor->setMaximumSize(QSize(16777215, 16777215));
        groupBox_sensor->setFont(font);
        verticalLayout_5 = new QVBoxLayout(groupBox_sensor);
        verticalLayout_5->setSpacing(0);
        verticalLayout_5->setObjectName(QStringLiteral("verticalLayout_5"));
        verticalLayout_5->setContentsMargins(10, 0, 10, 10);
        widget_6 = new QWidget(groupBox_sensor);
        widget_6->setObjectName(QStringLiteral("widget_6"));
        widget_6->setMinimumSize(QSize(0, 40));
        widget_6->setMaximumSize(QSize(16777215, 40));
        horizontalLayout_7 = new QHBoxLayout(widget_6);
        horizontalLayout_7->setSpacing(4);
        horizontalLayout_7->setObjectName(QStringLiteral("horizontalLayout_7"));
        horizontalLayout_7->setContentsMargins(0, 0, 0, 0);
        label_frequency = new QLabel(widget_6);
        label_frequency->setObjectName(QStringLiteral("label_frequency"));
        label_frequency->setMaximumSize(QSize(85, 16777215));
        label_frequency->setFont(font1);
        label_frequency->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_7->addWidget(label_frequency);

        lineEdit_frequency = new QLineEdit(widget_6);
        lineEdit_frequency->setObjectName(QStringLiteral("lineEdit_frequency"));
        lineEdit_frequency->setMinimumSize(QSize(0, 24));
        lineEdit_frequency->setMaximumSize(QSize(50, 16777215));
        lineEdit_frequency->setFont(font);
        lineEdit_frequency->setStyleSheet(QLatin1String("background:none;\n"
"border:1px solid #bbb;\n"
"border-radius:3px;\n"
"color:#333;"));

        horizontalLayout_7->addWidget(lineEdit_frequency);

        label_frequency_unit = new QLabel(widget_6);
        label_frequency_unit->setObjectName(QStringLiteral("label_frequency_unit"));
        label_frequency_unit->setMaximumSize(QSize(30, 16777215));
        label_frequency_unit->setFont(font1);
        label_frequency_unit->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_7->addWidget(label_frequency_unit);

        horizontalSpacer_9 = new QSpacerItem(20, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_7->addItem(horizontalSpacer_9);

        label_sensitivity = new QLabel(widget_6);
        label_sensitivity->setObjectName(QStringLiteral("label_sensitivity"));
        label_sensitivity->setMaximumSize(QSize(70, 16777215));
        label_sensitivity->setFont(font1);
        label_sensitivity->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_7->addWidget(label_sensitivity);

        lineEdit_sensitivity = new QLineEdit(widget_6);
        lineEdit_sensitivity->setObjectName(QStringLiteral("lineEdit_sensitivity"));
        lineEdit_sensitivity->setMinimumSize(QSize(0, 24));
        lineEdit_sensitivity->setMaximumSize(QSize(50, 16777215));
        lineEdit_sensitivity->setFont(font);
        lineEdit_sensitivity->setStyleSheet(QLatin1String("background:none;\n"
"border:1px solid #bbb;\n"
"border-radius:3px;\n"
"color:#333;"));

        horizontalLayout_7->addWidget(lineEdit_sensitivity);

        label_sensitivity_unit = new QLabel(widget_6);
        label_sensitivity_unit->setObjectName(QStringLiteral("label_sensitivity_unit"));
        label_sensitivity_unit->setMinimumSize(QSize(0, 0));
        label_sensitivity_unit->setMaximumSize(QSize(16777215, 16777215));
        label_sensitivity_unit->setFont(font1);
        label_sensitivity_unit->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_7->addWidget(label_sensitivity_unit);

        horizontalSpacer_10 = new QSpacerItem(20, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_7->addItem(horizontalSpacer_10);

        label_damping = new QLabel(widget_6);
        label_damping->setObjectName(QStringLiteral("label_damping"));
        label_damping->setMaximumSize(QSize(85, 16777215));
        label_damping->setFont(font1);
        label_damping->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_7->addWidget(label_damping);

        lineEdit_damping = new QLineEdit(widget_6);
        lineEdit_damping->setObjectName(QStringLiteral("lineEdit_damping"));
        lineEdit_damping->setMinimumSize(QSize(0, 24));
        lineEdit_damping->setMaximumSize(QSize(50, 16777215));
        lineEdit_damping->setFont(font);
        lineEdit_damping->setStyleSheet(QLatin1String("background:none;\n"
"border:1px solid #bbb;\n"
"border-radius:3px;\n"
"color:#333;"));

        horizontalLayout_7->addWidget(lineEdit_damping);

        label_damping_unit = new QLabel(widget_6);
        label_damping_unit->setObjectName(QStringLiteral("label_damping_unit"));
        label_damping_unit->setMaximumSize(QSize(16777215, 16777215));
        label_damping_unit->setFont(font1);
        label_damping_unit->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_7->addWidget(label_damping_unit);

        horizontalSpacer_5 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_7->addItem(horizontalSpacer_5);


        verticalLayout_5->addWidget(widget_6);

        widget_7 = new QWidget(groupBox_sensor);
        widget_7->setObjectName(QStringLiteral("widget_7"));
        widget_7->setMaximumSize(QSize(16777215, 16777215));
        horizontalLayout_8 = new QHBoxLayout(widget_7);
        horizontalLayout_8->setSpacing(0);
        horizontalLayout_8->setObjectName(QStringLiteral("horizontalLayout_8"));
        horizontalLayout_8->setContentsMargins(0, 0, 0, 0);
        treeWidget = new QTreeWidget(widget_7);
        treeWidget->headerItem()->setText(2, QString());
        treeWidget->setObjectName(QStringLiteral("treeWidget"));
        treeWidget->setMaximumSize(QSize(16777215, 130));
        treeWidget->setStyleSheet(QLatin1String("QTreeWidget{  \n"
"	background:#f0f0f0;\n"
"	border:1px solid #eee;\n"
"}\n"
"QScrollBar{background:#d0d2d4; width:6px; height:6px;}"));
        treeWidget->setFrameShape(QFrame::StyledPanel);
        treeWidget->setHeaderHidden(true);

        horizontalLayout_8->addWidget(treeWidget);


        verticalLayout_5->addWidget(widget_7);

        widget_8 = new QWidget(groupBox_sensor);
        widget_8->setObjectName(QStringLiteral("widget_8"));
        widget_8->setMinimumSize(QSize(0, 40));
        widget_8->setMaximumSize(QSize(16777215, 40));
        horizontalLayout_9 = new QHBoxLayout(widget_8);
        horizontalLayout_9->setSpacing(10);
        horizontalLayout_9->setObjectName(QStringLiteral("horizontalLayout_9"));
        horizontalLayout_9->setContentsMargins(0, -1, 0, 0);
        label_sensor_tip = new QLabel(widget_8);
        label_sensor_tip->setObjectName(QStringLiteral("label_sensor_tip"));
        label_sensor_tip->setFont(font);
        label_sensor_tip->setStyleSheet(QStringLiteral("color:green;"));

        horizontalLayout_9->addWidget(label_sensor_tip);

        pushButton_reload = new QPushButton(widget_8);
        pushButton_reload->setObjectName(QStringLiteral("pushButton_reload"));
        pushButton_reload->setMinimumSize(QSize(80, 24));
        pushButton_reload->setFont(font);
        pushButton_reload->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#1890FF;\n"
"	border:none;\n"
"	color:#fafafa;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#1281E8;\n"
"}"));

        horizontalLayout_9->addWidget(pushButton_reload);

        horizontalSpacer_4 = new QSpacerItem(170, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_9->addItem(horizontalSpacer_4);

        pushButton_sensor_setting = new QPushButton(widget_8);
        pushButton_sensor_setting->setObjectName(QStringLiteral("pushButton_sensor_setting"));
        pushButton_sensor_setting->setMinimumSize(QSize(80, 24));
        pushButton_sensor_setting->setFont(font);
        pushButton_sensor_setting->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#1890FF;\n"
"	border:none;\n"
"	color:#fafafa;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#1281E8;\n"
"}"));

        horizontalLayout_9->addWidget(pushButton_sensor_setting);


        verticalLayout_5->addWidget(widget_8);


        verticalLayout->addWidget(groupBox_sensor);

        widget_button_box = new QWidget(widget_main);
        widget_button_box->setObjectName(QStringLiteral("widget_button_box"));
        widget_button_box->setMinimumSize(QSize(0, 40));
        widget_button_box->setMaximumSize(QSize(16777215, 50));
        horizontalLayout_2 = new QHBoxLayout(widget_button_box);
        horizontalLayout_2->setSpacing(0);
        horizontalLayout_2->setObjectName(QStringLiteral("horizontalLayout_2"));
        horizontalLayout_2->setContentsMargins(0, 0, 0, 0);
        horizontalSpacer_2 = new QSpacerItem(127, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer_2);

        pushButton_save = new QPushButton(widget_button_box);
        pushButton_save->setObjectName(QStringLiteral("pushButton_save"));
        pushButton_save->setMinimumSize(QSize(80, 28));
        pushButton_save->setFont(font);
        pushButton_save->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#1890FF;\n"
"	border:none;\n"
"	color:#fafafa;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#1281E8;\n"
"}"));

        horizontalLayout_2->addWidget(pushButton_save);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Minimum, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer);

        pushButton_cancel = new QPushButton(widget_button_box);
        pushButton_cancel->setObjectName(QStringLiteral("pushButton_cancel"));
        pushButton_cancel->setMinimumSize(QSize(80, 28));
        pushButton_cancel->setFont(font);
        pushButton_cancel->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#ccc;\n"
"	border:none;\n"
"	color:#000;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#bbb;\n"
"}"));

        horizontalLayout_2->addWidget(pushButton_cancel);

        horizontalSpacer_3 = new QSpacerItem(127, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer_3);


        verticalLayout->addWidget(widget_button_box);


        horizontalLayout->addWidget(widget_main);


        retranslateUi(Project_edit);

        QMetaObject::connectSlotsByName(Project_edit);
    } // setupUi

    void retranslateUi(QWidget *Project_edit)
    {
        Project_edit->setWindowTitle(QApplication::translate("Project_edit", "Form", Q_NULLPTR));
        groupBox_project->setTitle(QApplication::translate("Project_edit", "\351\241\271\347\233\256\344\277\241\346\201\257", Q_NULLPTR));
        label->setText(QApplication::translate("Project_edit", "*", Q_NULLPTR));
        label_name->setText(QApplication::translate("Project_edit", "\351\241\271\347\233\256\345\220\215\347\247\260\357\274\232", Q_NULLPTR));
        lineEdit_name->setPlaceholderText(QApplication::translate("Project_edit", "\350\257\267\350\276\223\345\205\245\351\241\271\347\233\256\345\220\215\347\247\260", Q_NULLPTR));
        label_details->setText(QApplication::translate("Project_edit", "\351\241\271\347\233\256\346\217\217\350\277\260\357\274\232", Q_NULLPTR));
        textEdit_description->setHtml(QApplication::translate("Project_edit", "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n"
"<html><head><meta name=\"qrichtext\" content=\"1\" /><style type=\"text/css\">\n"
"p, li { white-space: pre-wrap; }\n"
"</style></head><body style=\" font-family:'Microsoft YaHei UI'; font-size:9pt; font-weight:400; font-style:normal;\">\n"
"<p style=\"-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><br /></p></body></html>", Q_NULLPTR));
        label_2->setText(QApplication::translate("Project_edit", "*", Q_NULLPTR));
        label_path->setText(QApplication::translate("Project_edit", "\345\255\230\345\202\250\350\267\257\345\276\204\357\274\232", Q_NULLPTR));
        lineEdit_path->setPlaceholderText(QApplication::translate("Project_edit", "\350\257\267\351\200\211\346\213\251\345\255\230\345\202\250\350\267\257\345\276\204", Q_NULLPTR));
        pushButton_path->setText(QApplication::translate("Project_edit", "\351\200\211\346\213\251", Q_NULLPTR));
        label_speed->setText(QApplication::translate("Project_edit", "\351\200\237\345\272\246(\347\261\263/\347\247\222)\357\274\232", Q_NULLPTR));
        lineEdit_speed->setPlaceholderText(QApplication::translate("Project_edit", "\350\257\267\350\276\223\345\205\245\351\200\237\345\272\246", Q_NULLPTR));
        label_speed_unit->setText(QString());
        label_supervisor->setText(QApplication::translate("Project_edit", "\347\233\221\345\210\266\345\215\225\344\275\215\357\274\232", Q_NULLPTR));
        lineEdit_company->setPlaceholderText(QApplication::translate("Project_edit", "\350\257\267\350\276\223\345\205\245\347\233\221\345\210\266\345\215\225\344\275\215", Q_NULLPTR));
        label_operator->setText(QApplication::translate("Project_edit", "\346\223\215\344\275\234\344\272\272\345\221\230\357\274\232", Q_NULLPTR));
        lineEdit_operator->setPlaceholderText(QApplication::translate("Project_edit", "\350\257\267\350\276\223\345\205\245\346\223\215\344\275\234\344\272\272\345\221\230", Q_NULLPTR));
        groupBox_sensor->setTitle(QApplication::translate("Project_edit", "\346\243\200\346\263\242\345\231\250\345\217\202\346\225\260", Q_NULLPTR));
        label_frequency->setText(QApplication::translate("Project_edit", "\350\207\252\347\204\266\351\242\221\347\216\207(Fn)\357\274\232", Q_NULLPTR));
        label_frequency_unit->setText(QApplication::translate("Project_edit", "(Hz)", Q_NULLPTR));
        label_sensitivity->setText(QApplication::translate("Project_edit", "\347\201\265\346\225\217\345\272\246(Gv):", Q_NULLPTR));
        label_sensitivity_unit->setText(QApplication::translate("Project_edit", "(12.0-150.0 v/n/s)", Q_NULLPTR));
        label_damping->setText(QApplication::translate("Project_edit", "\351\230\273\345\260\274\347\263\273\346\225\260(Bt)\357\274\232", Q_NULLPTR));
        label_damping_unit->setText(QApplication::translate("Project_edit", "(0.2-1.0)", Q_NULLPTR));
        QTreeWidgetItem *___qtreewidgetitem = treeWidget->headerItem();
        ___qtreewidgetitem->setText(1, QApplication::translate("Project_edit", "2", Q_NULLPTR));
        ___qtreewidgetitem->setText(0, QApplication::translate("Project_edit", "1", Q_NULLPTR));
        label_sensor_tip->setText(QApplication::translate("Project_edit", "\346\234\252\346\211\253\346\217\217", Q_NULLPTR));
        pushButton_reload->setText(QApplication::translate("Project_edit", "\351\207\215\346\226\260\346\211\253\346\217\217", Q_NULLPTR));
        pushButton_sensor_setting->setText(QApplication::translate("Project_edit", "\351\205\215\347\275\256\346\243\200\346\263\242\345\231\250", Q_NULLPTR));
        pushButton_save->setText(QApplication::translate("Project_edit", "\347\241\256\345\256\232", Q_NULLPTR));
        pushButton_cancel->setText(QApplication::translate("Project_edit", "\345\217\226\346\266\210", Q_NULLPTR));
    } // retranslateUi

};

namespace Ui {
    class Project_edit: public Ui_Project_edit {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_PROJECT_EDIT_H
