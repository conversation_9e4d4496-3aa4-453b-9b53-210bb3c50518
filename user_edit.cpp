#include "user_edit.h"
#include "ui_user_edit.h"
#include "message.h"

#include <QSqlQuery>
#include <QSqlError>
#include <QDebug>
#include <QMessageBox>


user_edit::user_edit(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::user_edit)
{
    ui->setupUi(this);
}

user_edit::~user_edit()
{
    delete ui;
}

void user_edit::on_pushButton_save_clicked()
{
    QMessageBox::information(this, "提示:","修改成功");
    this->close();

}

void user_edit::on_radioButton_enable_clicked()
{
    if(!this->ui->lineEdit_username->text().isEmpty())
    {
        QSqlQuery query;
        QString sql = "SELECT username, password FROM top_user WHERE id = '" + this->ui->lineEdit_username->text() + "';";
        if (!query.exec(sql))
        {
            qDebug() << "Query error:" << query.lastError().text() << __LINE__;
            return;
        }
        query.next();
        if(this->ui->lineEdit_realname->text() == query.value("username") && this->ui->lineEdit_password->text() == query.value("password"))
        {
            sql = "UPDATE top_user SET status = 1 WHERE id = " + this->ui->lineEdit_username->text() + ";";
            if (!query.exec(sql))
            {
                qDebug() << "Query error:" << query.lastError().text() << __LINE__;
                return;
            }
        }
        else
        {
            QMessageBox::information(this, "更改失败","请入正确的用户名和密码");
        }
    }
}

void user_edit::on_radioButton_disable_clicked()
{
    if(!this->ui->lineEdit_username->text().isEmpty())
    {
        QSqlQuery query;
        QString sql = "SELECT username, password FROM top_user WHERE id = '" + this->ui->lineEdit_username->text() + "';";
        if (!query.exec(sql))
        {
            qDebug() << "Query error:" << query.lastError().text() << __LINE__;
            return;
        }
        query.next();

        if(this->ui->lineEdit_realname->text() == query.value("username") &&
                this->ui->lineEdit_password->text() == query.value("password"))
        {
            sql = "UPDATE top_user SET status = 0 WHERE id = " + this->ui->lineEdit_username->text() + ";";
            if (!query.exec(sql))
            {
                qDebug() << "Query error:" << query.lastError().text() << __LINE__;
                return;
            }
        }
        else
        {
            QMessageBox::information(this, "更改失败","请入正确的用户名和密码");
        }
    }
    else
    {
        QMessageBox::information(this, "更改失败","请输入账号");
    }
}
