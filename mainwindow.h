#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include"project_list.h"
//#include"monitor.h"
#include"record.h"
#include"setting.h"
#include "user_main.h"
#include"more.h"
namespace Ui {
class MainWindow;
}

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();
    Project_list* windowProject;
    //Monitor* windowMonitor;
    Record*  windowRecord;
    User_main* windowUser;
    Setting* windowSetting;
    More* windowMore;
private slots:
    void on_toolButton_project_clicked();

    void on_toolButton_monitor_clicked();

    void on_toolButton_record_clicked();

    void on_toolButton_user_clicked();

    void on_toolButton_more_clicked();

    void on_toolButton_setting_clicked();

private:
    Ui::MainWindow *ui;
};

#endif // MAINWINDOW_H
