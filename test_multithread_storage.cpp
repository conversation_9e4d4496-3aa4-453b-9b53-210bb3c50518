#include <QCoreApplication>
#include <QDebug>
#include <QThread>
#include <QTimer>
#include <QDir>
#include "data_managent.h"

// 简单的测试函数来验证多线程存储
void test_storage_functionality()
{
    qDebug() << "开始测试多线程数据存储...";
    
    // 检查数据目录是否存在
    QDir dataDir("D:/Data");
    if (!dataDir.exists()) {
        qDebug() << "创建数据目录: D:/Data";
        dataDir.mkpath("D:/Data");
    }
    
    // 初始化数据和线程
    data_and_thread_init();
    
    qDebug() << "线程初始化完成，开始数据采集和存储...";
    qDebug() << "请观察以下几点：";
    qDebug() << "1. 三个线程是否都能正常启动";
    qDebug() << "2. 数据文件是否都能正常创建";
    qDebug() << "3. 是否存在线程竞争导致的数据丢失或文件损坏";
    
    // 让程序运行一段时间以观察效果
    QTimer::singleShot(30000, []() {  // 30秒后退出
        qDebug() << "测试完成，程序即将退出...";
        QCoreApplication::quit();
    });
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "多线程数据存储测试程序";
    qDebug() << "修复内容：";
    qDebug() << "1. 添加了文件操作互斥锁";
    qDebug() << "2. 修复了文件指针管理问题";
    qDebug() << "3. 使用信号参数而非全局数组传递数据";
    qDebug() << "4. 添加了数据访问的线程同步";
    
    // 延迟启动测试，给用户时间阅读信息
    QTimer::singleShot(2000, test_storage_functionality);
    
    return app.exec();
}
