/********************************************************************************
** Form generated from reading UI file 'reset_password.ui'
**
** Created by: Qt User Interface Compiler version 5.9.8
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_RESET_PASSWORD_H
#define UI_RESET_PASSWORD_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_reset_password
{
public:
    QHBoxLayout *horizontalLayout_2;
    QWidget *widget_main;
    QHBoxLayout *horizontalLayout;
    QSpacerItem *horizontalSpacer;
    QWidget *widget_reset_box;
    QVBoxLayout *verticalLayout;
    QSpacerItem *verticalSpacer;
    QWidget *widget_old_password;
    QHBoxLayout *horizontalLayout_5;
    QLabel *label_4;
    QLabel *label;
    QLineEdit *lineEdit_old_password;
    QSpacerItem *horizontalSpacer_3;
    QWidget *widget_new_password;
    QHBoxLayout *horizontalLayout_4;
    QLabel *label_5;
    QLabel *label_2;
    QLineEdit *lineEdit_new_password;
    QSpacerItem *horizontalSpacer_4;
    QWidget *widget_confirm_password;
    QHBoxLayout *horizontalLayout_3;
    QLabel *label_6;
    QLabel *label_3;
    QLineEdit *lineEdit_confirm_password;
    QSpacerItem *horizontalSpacer_5;
    QWidget *widget_button;
    QHBoxLayout *horizontalLayout_6;
    QSpacerItem *horizontalSpacer_6;
    QPushButton *pushButton_save;
    QSpacerItem *horizontalSpacer_7;
    QSpacerItem *verticalSpacer_2;
    QSpacerItem *horizontalSpacer_2;

    void setupUi(QWidget *reset_password)
    {
        if (reset_password->objectName().isEmpty())
            reset_password->setObjectName(QStringLiteral("reset_password"));
        reset_password->resize(900, 500);
        horizontalLayout_2 = new QHBoxLayout(reset_password);
        horizontalLayout_2->setSpacing(0);
        horizontalLayout_2->setObjectName(QStringLiteral("horizontalLayout_2"));
        horizontalLayout_2->setContentsMargins(0, 0, 0, 0);
        widget_main = new QWidget(reset_password);
        widget_main->setObjectName(QStringLiteral("widget_main"));
        QFont font;
        font.setFamily(QStringLiteral("Microsoft YaHei UI"));
        widget_main->setFont(font);
        widget_main->setStyleSheet(QStringLiteral("background:#fff;"));
        horizontalLayout = new QHBoxLayout(widget_main);
        horizontalLayout->setSpacing(0);
        horizontalLayout->setObjectName(QStringLiteral("horizontalLayout"));
        horizontalLayout->setContentsMargins(0, 0, 0, 0);
        horizontalSpacer = new QSpacerItem(100, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer);

        widget_reset_box = new QWidget(widget_main);
        widget_reset_box->setObjectName(QStringLiteral("widget_reset_box"));
        widget_reset_box->setFont(font);
        verticalLayout = new QVBoxLayout(widget_reset_box);
        verticalLayout->setSpacing(10);
        verticalLayout->setObjectName(QStringLiteral("verticalLayout"));
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        verticalSpacer = new QSpacerItem(20, 60, QSizePolicy::Minimum, QSizePolicy::Fixed);

        verticalLayout->addItem(verticalSpacer);

        widget_old_password = new QWidget(widget_reset_box);
        widget_old_password->setObjectName(QStringLiteral("widget_old_password"));
        widget_old_password->setMinimumSize(QSize(0, 40));
        horizontalLayout_5 = new QHBoxLayout(widget_old_password);
        horizontalLayout_5->setSpacing(0);
        horizontalLayout_5->setObjectName(QStringLiteral("horizontalLayout_5"));
        horizontalLayout_5->setContentsMargins(0, 0, 0, 0);
        label_4 = new QLabel(widget_old_password);
        label_4->setObjectName(QStringLiteral("label_4"));
        label_4->setStyleSheet(QStringLiteral("color:red;"));

        horizontalLayout_5->addWidget(label_4);

        label = new QLabel(widget_old_password);
        label->setObjectName(QStringLiteral("label"));
        label->setMinimumSize(QSize(60, 0));
        label->setFont(font);

        horizontalLayout_5->addWidget(label);

        lineEdit_old_password = new QLineEdit(widget_old_password);
        lineEdit_old_password->setObjectName(QStringLiteral("lineEdit_old_password"));
        lineEdit_old_password->setMinimumSize(QSize(260, 26));
        lineEdit_old_password->setMaximumSize(QSize(260, 16777215));
        lineEdit_old_password->setFont(font);
        lineEdit_old_password->setStyleSheet(QLatin1String("background:none;\n"
"border:1px solid #bbb;\n"
"border-radius:2px;\n"
"color:#333;"));
        lineEdit_old_password->setEchoMode(QLineEdit::Password);

        horizontalLayout_5->addWidget(lineEdit_old_password);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_5->addItem(horizontalSpacer_3);


        verticalLayout->addWidget(widget_old_password);

        widget_new_password = new QWidget(widget_reset_box);
        widget_new_password->setObjectName(QStringLiteral("widget_new_password"));
        widget_new_password->setMinimumSize(QSize(0, 40));
        horizontalLayout_4 = new QHBoxLayout(widget_new_password);
        horizontalLayout_4->setSpacing(0);
        horizontalLayout_4->setObjectName(QStringLiteral("horizontalLayout_4"));
        horizontalLayout_4->setContentsMargins(0, 0, 0, 0);
        label_5 = new QLabel(widget_new_password);
        label_5->setObjectName(QStringLiteral("label_5"));
        label_5->setStyleSheet(QStringLiteral("color:red;"));

        horizontalLayout_4->addWidget(label_5);

        label_2 = new QLabel(widget_new_password);
        label_2->setObjectName(QStringLiteral("label_2"));
        label_2->setMinimumSize(QSize(60, 0));
        label_2->setFont(font);

        horizontalLayout_4->addWidget(label_2);

        lineEdit_new_password = new QLineEdit(widget_new_password);
        lineEdit_new_password->setObjectName(QStringLiteral("lineEdit_new_password"));
        lineEdit_new_password->setMinimumSize(QSize(260, 26));
        lineEdit_new_password->setMaximumSize(QSize(260, 16777215));
        lineEdit_new_password->setFont(font);
        lineEdit_new_password->setStyleSheet(QLatin1String("background:none;\n"
"border:1px solid #bbb;\n"
"border-radius:2px;\n"
"color:#333;"));
        lineEdit_new_password->setEchoMode(QLineEdit::Password);

        horizontalLayout_4->addWidget(lineEdit_new_password);

        horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_4->addItem(horizontalSpacer_4);


        verticalLayout->addWidget(widget_new_password);

        widget_confirm_password = new QWidget(widget_reset_box);
        widget_confirm_password->setObjectName(QStringLiteral("widget_confirm_password"));
        widget_confirm_password->setMinimumSize(QSize(0, 40));
        horizontalLayout_3 = new QHBoxLayout(widget_confirm_password);
        horizontalLayout_3->setSpacing(0);
        horizontalLayout_3->setObjectName(QStringLiteral("horizontalLayout_3"));
        horizontalLayout_3->setContentsMargins(0, 0, 0, 0);
        label_6 = new QLabel(widget_confirm_password);
        label_6->setObjectName(QStringLiteral("label_6"));
        label_6->setStyleSheet(QStringLiteral("color:red;"));

        horizontalLayout_3->addWidget(label_6);

        label_3 = new QLabel(widget_confirm_password);
        label_3->setObjectName(QStringLiteral("label_3"));
        label_3->setMinimumSize(QSize(60, 0));
        label_3->setFont(font);

        horizontalLayout_3->addWidget(label_3);

        lineEdit_confirm_password = new QLineEdit(widget_confirm_password);
        lineEdit_confirm_password->setObjectName(QStringLiteral("lineEdit_confirm_password"));
        lineEdit_confirm_password->setMinimumSize(QSize(260, 26));
        lineEdit_confirm_password->setMaximumSize(QSize(260, 16777215));
        lineEdit_confirm_password->setFont(font);
        lineEdit_confirm_password->setStyleSheet(QLatin1String("background:none;\n"
"border:1px solid #bbb;\n"
"border-radius:2px;\n"
"color:#333;"));
        lineEdit_confirm_password->setEchoMode(QLineEdit::Password);

        horizontalLayout_3->addWidget(lineEdit_confirm_password);

        horizontalSpacer_5 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_3->addItem(horizontalSpacer_5);


        verticalLayout->addWidget(widget_confirm_password);

        widget_button = new QWidget(widget_reset_box);
        widget_button->setObjectName(QStringLiteral("widget_button"));
        widget_button->setMinimumSize(QSize(0, 40));
        horizontalLayout_6 = new QHBoxLayout(widget_button);
        horizontalLayout_6->setSpacing(0);
        horizontalLayout_6->setObjectName(QStringLiteral("horizontalLayout_6"));
        horizontalLayout_6->setContentsMargins(0, 0, 0, 0);
        horizontalSpacer_6 = new QSpacerItem(66, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_6->addItem(horizontalSpacer_6);

        pushButton_save = new QPushButton(widget_button);
        pushButton_save->setObjectName(QStringLiteral("pushButton_save"));
        pushButton_save->setMinimumSize(QSize(260, 26));
        pushButton_save->setMaximumSize(QSize(260, 16777215));
        pushButton_save->setFont(font);
        pushButton_save->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#1890FF;\n"
"	border:none;\n"
"	color:#fafafa;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#1281E8;\n"
"}"));

        horizontalLayout_6->addWidget(pushButton_save);

        horizontalSpacer_7 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_6->addItem(horizontalSpacer_7);


        verticalLayout->addWidget(widget_button);

        verticalSpacer_2 = new QSpacerItem(20, 42, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout->addItem(verticalSpacer_2);


        horizontalLayout->addWidget(widget_reset_box);

        horizontalSpacer_2 = new QSpacerItem(97, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_2);


        horizontalLayout_2->addWidget(widget_main);


        retranslateUi(reset_password);

        QMetaObject::connectSlotsByName(reset_password);
    } // setupUi

    void retranslateUi(QWidget *reset_password)
    {
        reset_password->setWindowTitle(QApplication::translate("reset_password", "\351\207\215\347\275\256\345\257\206\347\240\201", Q_NULLPTR));
        label_4->setText(QApplication::translate("reset_password", "*", Q_NULLPTR));
        label->setText(QApplication::translate("reset_password", "\345\216\237\345\247\213\345\257\206\347\240\201\357\274\232", Q_NULLPTR));
        lineEdit_old_password->setPlaceholderText(QApplication::translate("reset_password", "\350\257\267\350\276\223\345\205\245\345\216\237\345\247\213\345\257\206\347\240\201", Q_NULLPTR));
        label_5->setText(QApplication::translate("reset_password", "*", Q_NULLPTR));
        label_2->setText(QApplication::translate("reset_password", "\346\226\260\345\257\206\347\240\201\357\274\232", Q_NULLPTR));
        lineEdit_new_password->setPlaceholderText(QApplication::translate("reset_password", "\350\257\267\350\276\223\345\205\245\346\226\260\345\257\206\347\240\201", Q_NULLPTR));
        label_6->setText(QApplication::translate("reset_password", "*", Q_NULLPTR));
        label_3->setText(QApplication::translate("reset_password", "\351\207\215\345\244\215\345\257\206\347\240\201\357\274\232", Q_NULLPTR));
        lineEdit_confirm_password->setPlaceholderText(QApplication::translate("reset_password", "\350\257\267\350\276\223\345\205\245\351\207\215\345\244\215\345\257\206\347\240\201", Q_NULLPTR));
        pushButton_save->setText(QApplication::translate("reset_password", "\344\277\235 \345\255\230", Q_NULLPTR));
    } // retranslateUi

};

namespace Ui {
    class reset_password: public Ui_reset_password {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_RESET_PASSWORD_H
