#ifndef PROJECT_EDIT_H
#define PROJECT_EDIT_H

#include <QWidget>
#include "message.h"

namespace Ui {
class Project_edit;
}

class Project_edit : public QWidget
{
    Q_OBJECT

public:
    explicit Project_edit(Project* p,QWidget *parent = nullptr);
    ~Project_edit();

private slots:
    void on_pushButton_path_clicked();

    void on_pushButton_save_clicked();

    void on_pushButton_cancel_clicked();

    void on_pushButton_sensor_setting_clicked();

private:
    Ui::Project_edit *ui;
    class Project* project;
};

#endif // PROJECT_EDIT_H
