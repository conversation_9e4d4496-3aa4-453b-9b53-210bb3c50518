#include "fft_processer.h"
#include <cmath>
#include <QDebug>

fft_processer::fft_processer(int fftSize, double sampleRate)
    : m_fftSize(fftSize), m_sampleRate(sampleRate)
{
    // 初始化FFT配置
    m_fftConfig = kiss_fftr_alloc(m_fftSize, 0, nullptr, nullptr);
    m_ifftConfig = kiss_fftr_alloc(m_fftSize, 1, nullptr, nullptr);
}

fft_processer::~fft_processer()
{
    // 释放FFT配置
    if (m_fftConfig)
        free(m_fftConfig);
    if (m_ifftConfig)
        free(m_ifftConfig);
}

QVector<std::complex<double>> fft_processer::performFFT(const QVector<double>& input)
{
    QVector<std::complex<double>> output(m_fftSize / 2 + 1);

    // 检查输入数据长度
    if (input.size() < m_fftSize)
    {
        qWarning() << "Input data size is smaller than FFT size!";
        return output;
    }

    // 准备输入数组
    float* in_real = new float[m_fftSize];
    for (int i = 0; i < m_fftSize; ++i)
        in_real[i] = static_cast<float>(input[i]);

    // 执行FFT
    kiss_fft_cpx* out_complex = new kiss_fft_cpx[m_fftSize / 2 + 1];
    kiss_fftr(m_fftConfig, in_real, out_complex);

    // 转换结果为复数向量
    for (int i = 0; i <= m_fftSize / 2; ++i)
        output[i] = std::complex<double>(out_complex[i].r, out_complex[i].i);

    // 释放资源
    delete[] in_real;
    delete[] out_complex;

    return output;
}

QVector<double> fft_processer::performIFFT(const QVector<std::complex<double>>& spectrum)
{
    QVector<double> output(m_fftSize);

    // 检查输入数据长度
    if (spectrum.size() < m_fftSize / 2 + 1) {
        qDebug() << "Spectrum size is smaller than expected!";
        return output;
    }

    // 准备输入数组
    kiss_fft_cpx* in_complex = new kiss_fft_cpx[m_fftSize / 2 + 1];
    for (int i = 0; i <= m_fftSize / 2; ++i) {
        in_complex[i].r = static_cast<float>(spectrum[i].real());
        in_complex[i].i = static_cast<float>(spectrum[i].imag());
    }

    // 执行IFFT
    float* out_real = new float[m_fftSize];
    kiss_fftri(m_ifftConfig, in_complex, out_real);

    // 转换结果并归一化（IFFT结果需要除以FFT_SIZE）
    for (int i = 0; i < m_fftSize; ++i)
        output[i] = static_cast<double>(out_real[i]) / m_fftSize;

    // 释放资源
    delete[] in_complex;
    delete[] out_real;

    return output;
}

QVector<double> fft_processer::calculateMagnitude(const QVector<std::complex<double>>& spectrum)
{
    QVector<double> magnitude(spectrum.size());
    for (int i = 0; i < spectrum.size(); ++i)
        magnitude[i] = std::abs(spectrum[i]);
    return magnitude;
}

QVector<double> fft_processer::calculatePhase(const QVector<std::complex<double>>& spectrum)
{
    QVector<double> phase(spectrum.size());
    for (int i = 0; i < spectrum.size(); ++i)
        phase[i] = std::arg(spectrum[i]);
    return phase;
}

QVector<double> fft_processer::getFrequencyAxis() const
{
    QVector<double> freqAxis(m_fftSize / 2 + 1);
    double freqStep = m_sampleRate / m_fftSize;
    for (int i = 0; i <= m_fftSize / 2; ++i)
        freqAxis[i] = i * freqStep;
    return freqAxis;
}

int fft_processer::getFFTSize() const
{
    return m_fftSize;
}

double fft_processer::getSampleRate() const
{
    return m_sampleRate;
}
