/********************************************************************************
** Form generated from reading UI file 'fft_test.ui'
**
** Created by: Qt User Interface Compiler version 5.9.8
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_FFT_TEST_H
#define UI_FFT_TEST_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QWidget>
#include "qcustomplot.h"

QT_BEGIN_NAMESPACE

class Ui_fft_test
{
public:
    QWidget *centralWidget;
    QCustomPlot *widget;
    QCustomPlot *widget_2;
    QCustomPlot *widget_3;
    QCustomPlot *widget_4;

    void setupUi(QMainWindow *fft_test)
    {
        if (fft_test->objectName().isEmpty())
            fft_test->setObjectName(QStringLiteral("fft_test"));
        fft_test->resize(872, 572);
        centralWidget = new QWidget(fft_test);
        centralWidget->setObjectName(QStringLiteral("centralWidget"));
        widget = new QCustomPlot(centralWidget);
        widget->setObjectName(QStringLiteral("widget"));
        widget->setGeometry(QRect(10, 20, 421, 251));
        widget_2 = new QCustomPlot(centralWidget);
        widget_2->setObjectName(QStringLiteral("widget_2"));
        widget_2->setGeometry(QRect(10, 280, 421, 241));
        widget_3 = new QCustomPlot(centralWidget);
        widget_3->setObjectName(QStringLiteral("widget_3"));
        widget_3->setGeometry(QRect(440, 20, 381, 251));
        widget_4 = new QCustomPlot(centralWidget);
        widget_4->setObjectName(QStringLiteral("widget_4"));
        widget_4->setGeometry(QRect(440, 280, 381, 241));
        fft_test->setCentralWidget(centralWidget);

        retranslateUi(fft_test);

        QMetaObject::connectSlotsByName(fft_test);
    } // setupUi

    void retranslateUi(QMainWindow *fft_test)
    {
        fft_test->setWindowTitle(QApplication::translate("fft_test", "FFT\345\217\230\346\215\242\345\217\257\350\247\206\345\214\226\345\267\245\345\205\267", Q_NULLPTR));
    } // retranslateUi

};

namespace Ui {
    class fft_test: public Ui_fft_test {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_FFT_TEST_H
