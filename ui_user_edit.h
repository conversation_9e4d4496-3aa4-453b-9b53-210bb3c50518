/********************************************************************************
** Form generated from reading UI file 'user_edit.ui'
**
** Created by: Qt User Interface Compiler version 5.9.8
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_USER_EDIT_H
#define UI_USER_EDIT_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QRadioButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_user_edit
{
public:
    QHBoxLayout *horizontalLayout_2;
    QWidget *widget_main;
    QHBoxLayout *horizontalLayout;
    QSpacerItem *horizontalSpacer;
    QWidget *widget_user_box;
    QVBoxLayout *verticalLayout;
    QSpacerItem *verticalSpacer;
    QWidget *widget_username;
    QHBoxLayout *horizontalLayout_6;
    QLabel *label;
    QLabel *label_username;
    QLineEdit *lineEdit_username;
    QWidget *widget_password;
    QHBoxLayout *horizontalLayout_4;
    QLabel *label_2;
    QLabel *label_password;
    QLineEdit *lineEdit_password;
    QWidget *widget_realname;
    QHBoxLayout *horizontalLayout_5;
    QLabel *label_3;
    QLabel *label_realname;
    QLineEdit *lineEdit_realname;
    QWidget *widget_status_box;
    QHBoxLayout *horizontalLayout_3;
    QSpacerItem *horizontalSpacer_4;
    QRadioButton *radioButton_enable;
    QRadioButton *radioButton_disable;
    QSpacerItem *horizontalSpacer_3;
    QWidget *widget_btn;
    QHBoxLayout *horizontalLayout_7;
    QSpacerItem *horizontalSpacer_5;
    QPushButton *pushButton_save;
    QSpacerItem *verticalSpacer_2;
    QSpacerItem *horizontalSpacer_2;

    void setupUi(QWidget *user_edit)
    {
        if (user_edit->objectName().isEmpty())
            user_edit->setObjectName(QStringLiteral("user_edit"));
        user_edit->resize(400, 300);
        horizontalLayout_2 = new QHBoxLayout(user_edit);
        horizontalLayout_2->setSpacing(0);
        horizontalLayout_2->setObjectName(QStringLiteral("horizontalLayout_2"));
        horizontalLayout_2->setContentsMargins(0, 0, 0, 0);
        widget_main = new QWidget(user_edit);
        widget_main->setObjectName(QStringLiteral("widget_main"));
        QFont font;
        font.setFamily(QStringLiteral("Microsoft YaHei UI"));
        widget_main->setFont(font);
        widget_main->setStyleSheet(QStringLiteral("background:#fff;"));
        horizontalLayout = new QHBoxLayout(widget_main);
        horizontalLayout->setSpacing(0);
        horizontalLayout->setObjectName(QStringLiteral("horizontalLayout"));
        horizontalLayout->setContentsMargins(0, 0, 0, 0);
        horizontalSpacer = new QSpacerItem(97, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer);

        widget_user_box = new QWidget(widget_main);
        widget_user_box->setObjectName(QStringLiteral("widget_user_box"));
        widget_user_box->setMinimumSize(QSize(0, 300));
        widget_user_box->setFont(font);
        verticalLayout = new QVBoxLayout(widget_user_box);
        verticalLayout->setSpacing(10);
        verticalLayout->setObjectName(QStringLiteral("verticalLayout"));
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        verticalSpacer = new QSpacerItem(20, 42, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout->addItem(verticalSpacer);

        widget_username = new QWidget(widget_user_box);
        widget_username->setObjectName(QStringLiteral("widget_username"));
        horizontalLayout_6 = new QHBoxLayout(widget_username);
        horizontalLayout_6->setSpacing(0);
        horizontalLayout_6->setObjectName(QStringLiteral("horizontalLayout_6"));
        horizontalLayout_6->setContentsMargins(0, 0, 0, 0);
        label = new QLabel(widget_username);
        label->setObjectName(QStringLiteral("label"));
        label->setStyleSheet(QStringLiteral("color:red;"));

        horizontalLayout_6->addWidget(label);

        label_username = new QLabel(widget_username);
        label_username->setObjectName(QStringLiteral("label_username"));
        label_username->setMinimumSize(QSize(40, 0));
        label_username->setFont(font);
        label_username->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_6->addWidget(label_username);

        lineEdit_username = new QLineEdit(widget_username);
        lineEdit_username->setObjectName(QStringLiteral("lineEdit_username"));
        lineEdit_username->setEnabled(true);
        lineEdit_username->setMinimumSize(QSize(200, 30));
        lineEdit_username->setFont(font);
        lineEdit_username->setStyleSheet(QLatin1String("background:none;\n"
"border:1px solid #bbb;\n"
"border-radius:2px;\n"
"color:#333;"));
        lineEdit_username->setDragEnabled(false);

        horizontalLayout_6->addWidget(lineEdit_username);


        verticalLayout->addWidget(widget_username);

        widget_password = new QWidget(widget_user_box);
        widget_password->setObjectName(QStringLiteral("widget_password"));
        horizontalLayout_4 = new QHBoxLayout(widget_password);
        horizontalLayout_4->setSpacing(0);
        horizontalLayout_4->setObjectName(QStringLiteral("horizontalLayout_4"));
        horizontalLayout_4->setContentsMargins(0, 0, 0, 0);
        label_2 = new QLabel(widget_password);
        label_2->setObjectName(QStringLiteral("label_2"));
        label_2->setStyleSheet(QStringLiteral("color:red;"));

        horizontalLayout_4->addWidget(label_2);

        label_password = new QLabel(widget_password);
        label_password->setObjectName(QStringLiteral("label_password"));
        label_password->setMinimumSize(QSize(40, 0));
        label_password->setFont(font);
        label_password->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_4->addWidget(label_password);

        lineEdit_password = new QLineEdit(widget_password);
        lineEdit_password->setObjectName(QStringLiteral("lineEdit_password"));
        lineEdit_password->setMinimumSize(QSize(200, 30));
        lineEdit_password->setFont(font);
        lineEdit_password->setStyleSheet(QLatin1String("background:none;\n"
"border:1px solid #bbb;\n"
"border-radius:2px;\n"
"color:#333;"));
        lineEdit_password->setEchoMode(QLineEdit::Password);

        horizontalLayout_4->addWidget(lineEdit_password);


        verticalLayout->addWidget(widget_password);

        widget_realname = new QWidget(widget_user_box);
        widget_realname->setObjectName(QStringLiteral("widget_realname"));
        horizontalLayout_5 = new QHBoxLayout(widget_realname);
        horizontalLayout_5->setSpacing(0);
        horizontalLayout_5->setObjectName(QStringLiteral("horizontalLayout_5"));
        horizontalLayout_5->setContentsMargins(0, 0, 0, 0);
        label_3 = new QLabel(widget_realname);
        label_3->setObjectName(QStringLiteral("label_3"));
        label_3->setStyleSheet(QStringLiteral("color:red;"));

        horizontalLayout_5->addWidget(label_3);

        label_realname = new QLabel(widget_realname);
        label_realname->setObjectName(QStringLiteral("label_realname"));
        label_realname->setMinimumSize(QSize(40, 0));
        label_realname->setFont(font);
        label_realname->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_5->addWidget(label_realname);

        lineEdit_realname = new QLineEdit(widget_realname);
        lineEdit_realname->setObjectName(QStringLiteral("lineEdit_realname"));
        lineEdit_realname->setMinimumSize(QSize(200, 30));
        lineEdit_realname->setFont(font);
        lineEdit_realname->setStyleSheet(QLatin1String("background:none;\n"
"border:1px solid #bbb;\n"
"border-radius:2px;\n"
"color:#333;"));

        horizontalLayout_5->addWidget(lineEdit_realname);


        verticalLayout->addWidget(widget_realname);

        widget_status_box = new QWidget(widget_user_box);
        widget_status_box->setObjectName(QStringLiteral("widget_status_box"));
        widget_status_box->setMinimumSize(QSize(0, 30));
        horizontalLayout_3 = new QHBoxLayout(widget_status_box);
        horizontalLayout_3->setSpacing(10);
        horizontalLayout_3->setObjectName(QStringLiteral("horizontalLayout_3"));
        horizontalLayout_3->setContentsMargins(0, 0, 0, 0);
        horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_3->addItem(horizontalSpacer_4);

        radioButton_enable = new QRadioButton(widget_status_box);
        radioButton_enable->setObjectName(QStringLiteral("radioButton_enable"));
        radioButton_enable->setFont(font);
        radioButton_enable->setStyleSheet(QStringLiteral("color:#333;"));
        radioButton_enable->setChecked(true);

        horizontalLayout_3->addWidget(radioButton_enable);

        radioButton_disable = new QRadioButton(widget_status_box);
        radioButton_disable->setObjectName(QStringLiteral("radioButton_disable"));
        radioButton_disable->setFont(font);
        radioButton_disable->setStyleSheet(QStringLiteral("color:#333;"));

        horizontalLayout_3->addWidget(radioButton_disable);

        horizontalSpacer_3 = new QSpacerItem(73, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_3->addItem(horizontalSpacer_3);


        verticalLayout->addWidget(widget_status_box);

        widget_btn = new QWidget(widget_user_box);
        widget_btn->setObjectName(QStringLiteral("widget_btn"));
        widget_btn->setMinimumSize(QSize(0, 0));
        horizontalLayout_7 = new QHBoxLayout(widget_btn);
        horizontalLayout_7->setSpacing(0);
        horizontalLayout_7->setObjectName(QStringLiteral("horizontalLayout_7"));
        horizontalLayout_7->setContentsMargins(0, 0, 0, 0);
        horizontalSpacer_5 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_7->addItem(horizontalSpacer_5);

        pushButton_save = new QPushButton(widget_btn);
        pushButton_save->setObjectName(QStringLiteral("pushButton_save"));
        pushButton_save->setMinimumSize(QSize(200, 30));
        pushButton_save->setFont(font);
        pushButton_save->setStyleSheet(QLatin1String("QPushButton{\n"
"	background:#1890FF;\n"
"	border:none;\n"
"	color:#fafafa;\n"
"	border-radius:2px;\n"
"}\n"
"QPushButton:hover{\n"
"	background:#1281E8;\n"
"}"));

        horizontalLayout_7->addWidget(pushButton_save);


        verticalLayout->addWidget(widget_btn);

        verticalSpacer_2 = new QSpacerItem(20, 42, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout->addItem(verticalSpacer_2);


        horizontalLayout->addWidget(widget_user_box);

        horizontalSpacer_2 = new QSpacerItem(97, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_2);


        horizontalLayout_2->addWidget(widget_main);


        retranslateUi(user_edit);

        QMetaObject::connectSlotsByName(user_edit);
    } // setupUi

    void retranslateUi(QWidget *user_edit)
    {
        user_edit->setWindowTitle(QApplication::translate("user_edit", "\347\224\250\346\210\267\347\256\241\347\220\206", Q_NULLPTR));
        label->setText(QApplication::translate("user_edit", "*", Q_NULLPTR));
        label_username->setText(QApplication::translate("user_edit", "\350\264\246\345\217\267\357\274\232", Q_NULLPTR));
        lineEdit_username->setPlaceholderText(QApplication::translate("user_edit", "\350\257\267\350\276\223\345\205\245\350\264\246\345\217\267", Q_NULLPTR));
        label_2->setText(QApplication::translate("user_edit", "*", Q_NULLPTR));
        label_password->setText(QApplication::translate("user_edit", "\345\257\206\347\240\201\357\274\232", Q_NULLPTR));
        lineEdit_password->setPlaceholderText(QApplication::translate("user_edit", "\350\257\267\350\276\223\345\205\245\345\257\206\347\240\201", Q_NULLPTR));
        label_3->setText(QApplication::translate("user_edit", "*", Q_NULLPTR));
        label_realname->setText(QApplication::translate("user_edit", "\345\247\223\345\220\215\357\274\232", Q_NULLPTR));
        lineEdit_realname->setPlaceholderText(QApplication::translate("user_edit", "\350\257\267\350\276\223\345\205\245\345\247\223\345\220\215", Q_NULLPTR));
        radioButton_enable->setText(QApplication::translate("user_edit", "\345\220\257\347\224\250", Q_NULLPTR));
        radioButton_disable->setText(QApplication::translate("user_edit", "\345\201\234\347\224\250", Q_NULLPTR));
        pushButton_save->setText(QApplication::translate("user_edit", "\344\277\235 \345\255\230", Q_NULLPTR));
    } // retranslateUi

};

namespace Ui {
    class user_edit: public Ui_user_edit {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_USER_EDIT_H
